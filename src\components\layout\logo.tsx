import { useTheme } from "@/lib/theme-provider";
import { useBrandTheme } from "@/themes/brand-theme-provider";
import { cn } from "@/lib/utils";

export function Logo({ className }: { className?: string }) {
  const { theme } = useTheme();
  const { currentTheme } = useBrandTheme();
  
  // Use theme-specific logo if available, otherwise fallback to default logo
  const logoSrc = currentTheme?.branding?.logo 
    ? (theme === "dark" ? currentTheme.branding.logo.dark : currentTheme.branding.logo.light)
    : (theme === "dark" ? "/logo-dark.png" : "/logo.png");

  // Get theme-specific logo styles
  const logoStyle = currentTheme?.branding?.logoStyle || {};

  return (
    <img 
      key={logoSrc}
      src={logoSrc} 
      alt={`${currentTheme?.name || 'Catalyst AI'} Logo`}
      className={cn(
        "transition-all duration-200",
        currentTheme?.id === 'google' ? 'object-contain' : 'object-cover',
        className
      )}
      style={logoStyle}
      onError={(e) => {
        // Fallback if the logo doesn't exist
        e.currentTarget.src = theme === "dark" ? "/logo-dark.png" : "/logo.png";
      }}
    />
  );
} 