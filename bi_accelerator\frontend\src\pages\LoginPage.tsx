import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Loader } from '@/components/shared/Loader';
import { useToast } from '@/components/ui/use-toast';

const LoginPage = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  
  // Check if already authenticated on mount
  useEffect(() => {
    const isAuth = localStorage.getItem('isAuthenticated');
    if (isAuth === 'true') {
      setIsAuthenticated(true);
      navigate('/');
    }
    
    // Add debugging information about the .env login details
    console.log("Attempting to fetch credentials from backend...");
    fetch('http://localhost:8001/api/auth/credentials')
      .then(res => res.json())
      .then(data => {
        console.log("Credentials loaded from backend:", { 
          username: data.username,
          // Don't log the actual password, just its length for safety
          passwordLength: data.password ? data.password.length : 0
        });
      })
      .catch(err => {
        console.error("Failed to load credentials:", err);
      });
  }, [navigate]);
  
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      // Trim whitespace from input credentials
      const trimmedUsername = username.trim();
      const trimmedPassword = password.trim();
      
      // Fetch credentials from backend JSON file
      const response = await fetch('http://localhost:8001/api/auth/credentials');
      if (!response.ok) {
        throw new Error(`Failed to fetch credentials: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      console.log("API response:", {
        username: data.username,
        passwordLength: data.password ? data.password.length : 0
      });
      console.log("Input values:", { 
        username: trimmedUsername,
        passwordLength: trimmedPassword.length
      });
      
      // Compare the trimmed values - explicitly convert to strings
      if (String(trimmedUsername) === String(data.username) && 
          String(trimmedPassword) === String(data.password)) {
        
        console.log("Login successful: credentials match");
        
        // Store authentication state
        localStorage.setItem('isAuthenticated', 'true');
        setIsAuthenticated(true);
        toast({
          title: "Login successful",
          description: "Welcome to BI Accelerator!",
        });
        navigate('/');
      } else {
        console.error("Authentication failed: credentials don't match");
        console.log("Exact comparison:");
        console.log(`Input username: "${trimmedUsername}" (${trimmedUsername.length} chars)`);
        console.log(`Stored username: "${data.username}" (${data.username.length} chars)`);
        // For security, we don't log the actual passwords
        console.log(`Input password length: ${trimmedPassword.length}`);
        console.log(`Stored password length: ${data.password.length}`);

        toast({
          title: "Authentication failed",
          description: "Invalid username or password. Please try again.",
          variant: "destructive",
        });
      }
      setIsLoading(false);
    } catch (error) {
      console.error('Login error:', error);
      toast({
        title: "Authentication failed",
        description: "Something went wrong. Please try again. Make sure the backend server is running on port 8001.",
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };
  
  if (isAuthenticated) {
    return <Loader fullScreen />;
  }
  
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4 animate-fade-in">
      <div className="w-full max-w-md space-y-8">
        <div className="flex flex-col items-center">
          <img
            src="/lovable-uploads/67006617-5581-4839-8344-4baf7e4a0557.png"
            alt="BI Accelerator Logo"
            className="h-24 w-24 mb-4"
          />
          <h1 className="text-4xl font-bold tracking-tight mb-2 text-center">BI Accelerator</h1>
          <p className="text-xl text-center text-muted-foreground mb-8">
            Login to access the BI acceleration platform
          </p>
        </div>
        
        <Card className="p-6 shadow-lg border border-brand-100 bg-gradient-to-br from-brand-50 to-background">
          <form onSubmit={handleLogin} className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  placeholder="Enter your username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  required
                  disabled={isLoading}
                  className="border-brand-200 focus-visible:ring-brand-500"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  disabled={isLoading}
                  className="border-brand-200 focus-visible:ring-brand-500"
                />
              </div>
            </div>
            
            <Button
              type="submit"
              className="w-full bg-brand-500 hover:bg-brand-600 text-white"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center">
                  <span className="animate-spin mr-2">⟳</span> Logging in...
                </div>
              ) : (
                'Log in'
              )}
            </Button>
          </form>
        </Card>
      </div>
    </div>
  );
};

export default LoginPage; 