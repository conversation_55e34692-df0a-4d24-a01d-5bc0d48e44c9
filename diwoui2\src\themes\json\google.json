{"name": "Google", "id": "google", "colors": {"primary": {"50": "#E8F0FE", "100": "#D2E3FC", "200": "#AECBFA", "300": "#8AB4F8", "400": "#669DF6", "500": "#4285F4", "600": "#1A73E8", "700": "#174EA6", "800": "#185ABC", "900": "#0D47A1", "950": "#0B3D91"}, "accent": {"blue": "#4285F4", "red": "#EA4335", "yellow": "#FBBC05", "green": "#34A853"}, "background": {"light": "#ffffff", "dark": "#202124"}, "card": {"light": "#f8fafc", "dark": "#292A2D"}, "text": {"light": "#202124", "dark": "#f8fafc"}, "border": {"light": "#e2e8f0", "dark": "#3C4043"}}, "fonts": {"body": "Google Sans, Roboto, <PERSON><PERSON>, sans-serif", "heading": "Google Sans, Roboto, <PERSON><PERSON>, sans-serif", "mono": "SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace"}, "radii": {"none": "0", "sm": "0.25rem", "md": "0.5rem", "lg": "0.75rem", "xl": "1rem", "2xl": "1.5rem", "3xl": "2rem", "full": "9999px"}, "shadows": {"sm": "0 1px 2px 0 rgba(60,64,67,0.08)", "md": "0 2px 8px rgba(60,64,67,0.15)", "lg": "0 4px 20px rgba(60,64,67,0.18)"}, "branding": {"logo": {"light": "/icons/google-logo-light.svg", "dark": "/icons/google-logo-dark.svg"}, "favicon": "/favicon-google.ico", "logoStyle": {"width": "32px", "height": "32px"}}, "components": {"badge": {"useGradient": true, "getBackgroundColor": {"_code": "function(severity, category) { \n  // Map severity and category to background colors\n  if (category === 'Product') {\n    return '#FFF8E1';\n  }\n  \n  const bgColorMap = {\n    'critical': '#FFEBEE',\n    'high': '#E8F5E9',\n    'medium': '#FFF8E1',\n    'low': '#E3F2FD'\n  };\n  \n  return bgColorMap[severity] || '#E8F0FE';\n}"}, "getTextColor": {"_code": "function(severity, category) { \n  if (category === 'Product') {\n    return '#F57F17';\n  }\n  \n  const textColorMap = {\n    'critical': '#B71C1C',\n    'high': '#1B5E20',\n    'medium': '#F57F17',\n    'low': '#0D47A1'\n  };\n  \n  return textColorMap[severity] || '#0D47A1';\n}"}, "style": {"boxShadow": "0 1px 2px rgba(0,0,0,0.05)", "border": "1px solid rgba(0,0,0,0.05)", "padding": "0.5rem 1rem", "borderRadius": "0.375rem", "fontSize": "0.75rem", "fontWeight": "500", "marginBottom": "0.75rem"}}, "alert": {"getBorderColor": {"_code": "function(severity) {\n  // Map severity to accent colors for border\n  const colorMap = {\n    'critical': 'red',\n    'high': 'green',\n    'medium': 'yellow',\n    'low': 'blue'\n  };\n  \n  return colorMap[severity] || 'blue';\n}"}, "getActionColor": {"_code": "function(severity) {\n  // Map severity to accent colors for actions\n  const colorMap = {\n    'critical': 'red',\n    'high': 'green',\n    'medium': 'yellow',\n    'low': 'blue'\n  };\n  \n  return colorMap[severity] || 'blue';\n}"}, "style": {"backgroundColor": "white", "borderRadius": "0.5rem"}}}}