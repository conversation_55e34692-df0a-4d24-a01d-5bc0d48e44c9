import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery, useMutation } from '@tanstack/react-query';
import { BarChart3, Search, Info, FileText, Grid, List, Database, AlertTriangle, CheckCircle, LayoutList, Loader2, BarChart4, Download, Code, ChevronRight } from 'lucide-react';
import { apiService, Report, Project, BatchAnalysisResponse, ProjectAnalysisResponse, SQLAnalysisRawResponse } from '@/services/api';
import { Input } from '@/components/ui/input';
import Card, { CardHeader, CardTitle, CardContent, CardFooter } from '@/components/shared/Card';
import Button from '@/components/shared/Button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader } from '@/components/shared/Loader';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { useViewMode } from '@/lib/ViewModeContext';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import BulkPowerBIExportModal from '@/components/BulkPowerBIExportModal';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

interface TableField {
  name: string;
  type: string;
}

interface TableInfo {
  id: string;
  name: string;
  type: string;
  fields: TableField[];
}

interface Relationship {
  source: string;
  target: string;
  sourceField: string;
  targetField: string;
  relationship: string;
}

interface ModelStructure {
  model_type: string;
  fact_tables: string[];
  dimension_tables: string[];
  description?: string;
}

interface AnalysisData {
  report_name: string;
  analysis: {
    main_tables?: TableInfo[];
    relationships?: Relationship[];
    model_structure?: ModelStructure;
  };
}

const FFSQLReportsPage = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [analysisSearchTerm, setAnalysisSearchTerm] = useState('');
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const { viewMode, setViewMode } = useViewMode();
  const [selectedReports, setSelectedReports] = useState<string[]>([]);
  const [analysisResults, setAnalysisResults] = useState<BatchAnalysisResponse | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [activeTab, setActiveTab] = useState("reports");
  const [isPowerBIExportOpen, setIsPowerBIExportOpen] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [isSqlDownloading, setIsSqlDownloading] = useState(false);
  const [isBulkExportOpen, setIsBulkExportOpen] = useState(false);
  
  useEffect(() => {
    const storedProject = localStorage.getItem('selectedProject');
    if (storedProject) {
      setSelectedProject(JSON.parse(storedProject));
    }
  }, [navigate]);
  
  // Query for reports
  const { data: reports, isLoading, error } = useQuery({
    queryKey: ['ffsql-reports', selectedProject?.id],
    queryFn: () => selectedProject ? apiService.getFFSQLReports(selectedProject.id) : Promise.resolve([]),
    enabled: !!selectedProject,
  });
  
  // Query for saved analyses
  const { 
    data: projectAnalysis, 
    isLoading: isLoadingAnalysis, 
    error: analysisError,
    refetch: refetchProjectAnalysis
  } = useQuery({
    queryKey: ['project-analysis', selectedProject?.id],
    queryFn: () => selectedProject ? apiService.getProjectAnalysis(selectedProject.id) : Promise.resolve({ project_id: '', analyses: {} }),
    enabled: !!selectedProject && activeTab === "analyses",
  });
  
  // Format the analyses for display
  const formattedAnalyses = React.useMemo(() => {
    if (!projectAnalysis || !projectAnalysis.analyses) return [];
    
    return Object.entries(projectAnalysis.analyses).map(([reportId, data]) => ({
      reportId,
      reportName: (data as AnalysisData).report_name,
      analysis: (data as AnalysisData).analysis,
      tableCount: (data as AnalysisData).analysis.main_tables?.length || 0,
      relationshipCount: (data as AnalysisData).analysis.relationships?.length || 0,
      modelType: (data as AnalysisData).analysis.model_structure?.model_type || "unknown"
    }));
  }, [projectAnalysis]);
  
  // Filter analyses based on search term
  const filteredAnalyses = React.useMemo(() => {
    if (!formattedAnalyses) return [];
    if (!analysisSearchTerm) return formattedAnalyses;
    
    return formattedAnalyses.filter(analysis => 
      analysis.reportName.toLowerCase().includes(analysisSearchTerm.toLowerCase())
    );
  }, [formattedAnalyses, analysisSearchTerm]);
  
  // Mutation for analyzing reports
  const analyzeReportsMutation = useMutation({
    mutationFn: ({ projectId, reportIds }: { projectId: string, reportIds: string[] }) => 
      apiService.analyzeReports(projectId, reportIds),
    onSuccess: (data) => {
      setAnalysisResults(data);
      setIsAnalyzing(false);
      toast({
        title: "Analysis Complete",
        description: `Successfully analyzed ${data.analyses_count} reports.`,
        variant: "default",
      });
      // Refresh the analyses list
      refetchProjectAnalysis();
    },
    onError: (error) => {
      setIsAnalyzing(false);
      toast({
        title: "Analysis Failed",
        description: error instanceof Error ? error.message : "Failed to analyze reports",
        variant: "destructive",
      });
    }
  });
  
  // Mutation for deleting project analysis
  const deleteAnalysisMutation = useMutation({
    mutationFn: (projectId: string) => apiService.deleteProjectAnalysis(projectId),
    onSuccess: () => {
      toast({
        title: "Analyses Deleted",
        description: "All analyses have been deleted for this project.",
        variant: "default",
      });
      refetchProjectAnalysis();
    },
    onError: (error) => {
      toast({
        title: "Deletion Failed",
        description: error instanceof Error ? error.message : "Failed to delete analyses",
        variant: "destructive",
      });
    }
  });
  
  // Mutation for downloading project analysis
  const downloadAnalysisMutation = useMutation({
    mutationFn: ({ projectId, format }: { projectId: string, format: 'pdf' | 'docx' }) => 
      apiService.downloadProjectAnalysis(projectId, format),
    onSuccess: () => {
      setIsDownloading(false);
      toast({
        title: "Download Complete",
        description: "The analysis document has been downloaded successfully.",
        variant: "default",
      });
    },
    onError: (error) => {
      setIsDownloading(false);
      toast({
        title: "Download Failed",
        description: error instanceof Error ? error.message : "Failed to download analysis document",
        variant: "destructive",
      });
    }
  });
  
  const filteredReports = reports?.filter(report => 
    report.name.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const handleReportClick = (report: Report) => {
    navigate(`/reports/${report.id}`);
  };

  const handleBulkExport = () => {
    setIsBulkExportOpen(true);
  };

  const handleReportSelect = (reportId: string) => {
    setSelectedReports(prev => {
      if (prev.includes(reportId)) {
        return prev.filter(id => id !== reportId);
      } else {
        return [...prev, reportId];
      }
    });
  };
  
  const handleAnalyzeClick = () => {
    if (!selectedProject || selectedReports.length === 0) return;
    
    setIsAnalyzing(true);
    analyzeReportsMutation.mutate({
      projectId: selectedProject.id,
      reportIds: selectedReports
    });
  };
  
  const handleDeleteAllAnalyses = () => {
    if (!selectedProject) return;
    
    if (window.confirm("Are you sure you want to delete all analyses for this project?")) {
      deleteAnalysisMutation.mutate(selectedProject.id);
    }
  };
  
  const handleDownloadAnalysis = (format: 'pdf' | 'docx') => {
    if (!selectedProject) return;
    
    console.log(`Download requested for project ${selectedProject.id} in ${format} format`);
    
    setIsDownloading(true);
    
    // Use try-catch to handle any uncaught exceptions
    try {
      downloadAnalysisMutation.mutate(
        {
          projectId: selectedProject.id,
          format: format
        },
        {
          onSuccess: () => {
            console.log('Download mutation completed successfully');
            setIsDownloading(false);
            toast({
              title: "Download Complete",
              description: "The analysis document has been downloaded successfully.",
              variant: "default",
            });
          },
          onError: (error: any) => {
            console.error('Download mutation error:', error);
            setIsDownloading(false);
            toast({
              title: "Download Failed",
              description: error instanceof Error ? error.message : "Failed to download analysis document",
              variant: "destructive",
            });
          }
        }
      );
    } catch (error) {
      console.error('Exception during download mutation:', error);
      setIsDownloading(false);
      toast({
        title: "Download Failed",
        description: "An unexpected error occurred while trying to download the document.",
        variant: "destructive",
      });
    }
  };
  
  // Direct download function without using mutation
  const handleDirectDownload = async () => {
    if (!selectedProject) return;
    
    console.log(`Document download requested for project ${selectedProject.id}`);
    
    setIsDownloading(true);
    
    try {
      await apiService.downloadProjectAnalysis(selectedProject.id); // The function now defaults to 'docx'
      console.log('Download form submitted successfully');
      
      // Reset downloading state after a short delay
      setTimeout(() => {
        setIsDownloading(false);
        toast({
          title: "Download Started",
          description: "Your document should begin downloading automatically in a new window.",
          variant: "default",
        });
      }, 500);
    } catch (error) {
      console.error('Direct download error:', error);
      setIsDownloading(false);
      toast({
        title: "Download Failed",
        description: error instanceof Error ? error.message : "Failed to download document",
        variant: "destructive",
      });
    }
  };
  
  // Function to download SQL for selected reports
  const handleDownloadSql = async () => {
    if (!selectedProject || selectedReports.length === 0) return;
    
    console.log(`SQL download requested for ${selectedReports.length} reports in project ${selectedProject.id}`);
    
    setIsSqlDownloading(true);
    
    try {
      await apiService.downloadReportsSql(selectedProject.id, selectedReports);
      toast({
        title: "SQL Download Started",
        description: `SQL for ${selectedReports.length} reports will be downloaded in a new window.`,
        variant: "default",
      });
    } catch (error) {
      console.error('SQL download error:', error);
      toast({
        title: "SQL Download Failed",
        description: error instanceof Error ? error.message : "Failed to download SQL for selected reports",
        variant: "destructive",
      });
    } finally {
      setIsSqlDownloading(false);
    }
  };
  
  // Function to check if a report has been analyzed successfully
  const isReportAnalyzed = (reportId: string) => {
    if (!analysisResults) return false;
    const result = analysisResults.analyses.find(a => a.report_id === reportId);
    return result && result.status === 'success';
  };
  
  // Function to check if a report analysis failed
  const didReportAnalysisFail = (reportId: string) => {
    if (!analysisResults) return false;
    const result = analysisResults.analyses.find(a => a.report_id === reportId);
    return result && result.status === 'error';
  };
  
  // Show message when no project is selected
  if (!selectedProject) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <div className="text-center py-12">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
            <BarChart3 className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-medium mb-2">No Project Selected</h3>
          <p className="text-muted-foreground">
            Please select a project to view FFSQL reports and analyses
          </p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container px-4 mx-auto animate-fade-in">
      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/')}>
          Home
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/projects')}>
          Projects
        </Button>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground truncate max-w-xs">
          FFSQL Reports
        </span>
      </div>
      
      <div className="flex flex-col mb-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
            <TabsList>
              <TabsTrigger value="reports">Reports</TabsTrigger>
              <TabsTrigger value="analyses">Analyses</TabsTrigger>
            </TabsList>
            
            {activeTab === "reports" ? (
              <div className="flex flex-wrap items-center gap-4">
                {!isLoading && filteredReports && filteredReports.length > 0 && (
                  <div className="flex items-center gap-2">
                    <Checkbox 
                      id="select-all"
                      checked={filteredReports.length > 0 && selectedReports.length === filteredReports.length}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedReports(filteredReports.map(report => report.id));
                        } else {
                          setSelectedReports([]);
                        }
                      }}
                    />
                    <label htmlFor="select-all" className="text-sm font-medium">
                      Select All
                    </label>
                  </div>
                )}
                <Button
                  onClick={handleAnalyzeClick}
                  variant="default"
                  disabled={isAnalyzing || selectedReports.length === 0}
                  icon={isAnalyzing ? <Loader2 className="h-4 w-4 animate-spin" /> : <Database className="h-4 w-4" />}
                >
                  {isAnalyzing ? 'Analyzing...' : 'Analyze'}
                </Button>
                
                {selectedReports.length > 0 && (
                  <Button
                    onClick={handleDownloadSql}
                    variant="outline"
                    disabled={isSqlDownloading}
                    icon={isSqlDownloading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Code className="h-4 w-4" />}
                  >
                    {isSqlDownloading ? 'Downloading...' : 'Download SQL as Excel'}
                  </Button>
                )}
                
                <Button
                  onClick={handleBulkExport}
                  className="flex items-center gap-2"
                  disabled={isLoading || !reports?.length}
                >
                  <BarChart4 className="h-4 w-4" />
                  Bulk Export to Power BI
                </Button>
                
                <ToggleGroup type="single" value={viewMode} onValueChange={(value) => value && setViewMode(value as 'card' | 'list')}>
                  <ToggleGroupItem value="card" aria-label="Card view" disabled={isLoading}>
                    <Grid className="h-4 w-4" />
                  </ToggleGroupItem>
                  <ToggleGroupItem value="list" aria-label="List view" disabled={isLoading}>
                    <List className="h-4 w-4" />
                  </ToggleGroupItem>
                </ToggleGroup>
              </div>
            ) : (
              <div className="flex gap-2">
                {formattedAnalyses.length > 0 && (
                  <>
                    <Button
                      variant="outline"
                      icon={<Download className="h-4 w-4" />}
                      disabled={isDownloading || formattedAnalyses.length === 0}
                      onClick={handleDirectDownload}
                    >
                      Download Document
                    </Button>
                    
                    {isDownloading && (
                      <div className="flex items-center">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        <span>Downloading...</span>
                      </div>
                    )}
                    
                    <Button
                      onClick={handleDeleteAllAnalyses}
                      variant="destructive"
                      className="bg-red-600 hover:bg-red-700"
                      disabled={deleteAnalysisMutation.isPending}
                    >
                      {deleteAnalysisMutation.isPending ? 'Deleting...' : 'Delete All Analyses'}
                    </Button>
                  </>
                )}
              </div>
            )}
          </div>
          
          <TabsContent value="reports" className="mt-0">
            <div className="mb-6">
              <div className="relative max-w-md w-full">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search FFSQL reports..."
                  className="pl-9"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  disabled={isLoading}
                />
              </div>
            </div>
            
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <Loader className="text-brand-600" />
              </div>
            ) : error ? (
              <Card className="border-red-200 bg-red-50">
                <CardHeader>
                  <CardTitle className="text-red-700 flex items-center">
                    <Info className="mr-2 h-5 w-5" />
                    Error Loading FFSQL Reports
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-red-600">
                    There was an error loading the FFSQL reports. Please check your connection and try again.
                  </p>
                </CardContent>
                <CardFooter>
                  <Button 
                    onClick={() => window.location.reload()}
                    variant="outline"
                    className="text-red-600 border-red-200 hover:bg-red-50"
                  >
                    Retry
                  </Button>
                </CardFooter>
              </Card>
            ) : !filteredReports?.length ? (
              <div className="text-center py-12">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
                  <BarChart3 className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium mb-2">No FFSQL Reports Found</h3>
                <p className="text-muted-foreground">
                  {searchTerm 
                    ? `No FFSQL reports match "${searchTerm}"`
                    : "There are no FFSQL reports available"
                  }
                </p>
              </div>
            ) : viewMode === 'card' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredReports?.map((report, index) => (
                  <Card 
                    key={report.id} 
                    hoverable 
                    className={`group transition-all duration-300 animate-slide-up delay-${index * 50}`}
                  >
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center flex-1 mr-2">
                          <div className="p-2 rounded-lg bg-purple-100 mr-3">
                            <BarChart3 className="h-5 w-5 text-purple-600" />
                          </div>
                          <CardTitle className="truncate max-w-[180px]">{report.name}</CardTitle>
                        </div>
                        <div className="flex items-center gap-2">
                          {isReportAnalyzed(report.id) && (
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          )}
                          {didReportAnalysisFail(report.id) && (
                            <AlertTriangle className="h-5 w-5 text-red-500" />
                          )}
                          <Checkbox 
                            checked={selectedReports.includes(report.id)} 
                            onCheckedChange={() => handleReportSelect(report.id)}
                            onClick={(e) => e.stopPropagation()}
                          />
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-2">
                        Type: <span className="font-medium">{report.ext_type}</span>
                      </p>
                      <p className="text-sm text-muted-foreground mb-2">
                        ID: <span className="font-mono text-xs bg-muted rounded px-1 py-0.5">{report.id}</span>
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button 
                        variant="default" 
                        fullWidth
                        className="group-hover:bg-brand-600 transition-colors"
                        icon={<FileText className="h-4 w-4" />}
                        iconPosition="right"
                        onClick={() => handleReportClick(report)}
                      >
                        View Report
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="flex flex-col gap-3">
                {filteredReports?.map((report, index) => (
                  <div 
                    key={report.id} 
                    className={`group flex items-center justify-between p-4 rounded-lg border border-border bg-card transition-all duration-300 animate-slide-up delay-${index * 50}`}
                    onClick={() => handleReportClick(report)}
                  >
                    <div className="flex items-center">
                      <div className="flex items-center gap-2 mr-3">
                        <Checkbox 
                          checked={selectedReports.includes(report.id)} 
                          onCheckedChange={() => handleReportSelect(report.id)}
                          onClick={(e) => e.stopPropagation()}
                        />
                        {isReportAnalyzed(report.id) && (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        )}
                        {didReportAnalysisFail(report.id) && (
                          <AlertTriangle className="h-5 w-5 text-red-500" />
                        )}
                      </div>
                      <div className="p-2 rounded-lg bg-purple-100 mr-3">
                        <BarChart3 className="h-5 w-5 text-purple-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold">{report.name}</h3>
                        <div className="flex gap-4">
                          <p className="text-sm text-muted-foreground">
                            Type: <span className="font-medium">{report.ext_type}</span>
                          </p>
                          <p className="text-sm text-muted-foreground">
                            ID: <span className="font-mono text-xs bg-muted rounded px-1 py-0.5">{report.id}</span>
                          </p>
                        </div>
                      </div>
                    </div>
                    <Button 
                      variant="default" 
                      size="sm"
                      className="group-hover:bg-brand-600 transition-colors"
                      icon={<FileText className="h-4 w-4" />}
                      iconPosition="right"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleReportClick(report);
                      }}
                    >
                      View Report
                    </Button>
                  </div>
                ))}
              </div>
            )}

            {/* Analysis Results Summary */}
            {analysisResults && (
              <div className="mt-8">
                <Card className="border-blue-200 bg-blue-50">
                  <CardHeader>
                    <CardTitle className="text-blue-800 flex items-center">
                      <Database className="mr-2 h-5 w-5" />
                      SQL Analysis Results
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-blue-800 mb-4">
                      Analyzed {analysisResults.analyses_count} reports for project {selectedProject.name}.
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium text-blue-800 mb-2">Success ({analysisResults.analyses.filter(a => a.status === 'success').length})</h4>
                        <ul className="list-disc pl-5 text-blue-700">
                          {analysisResults.analyses
                            .filter(a => a.status === 'success')
                            .map(result => (
                              <li key={result.report_id} className="mb-1">{result.report_name}</li>
                            ))}
                        </ul>
                      </div>
                      {analysisResults.analyses.some(a => a.status === 'error') && (
                        <div>
                          <h4 className="font-medium text-red-800 mb-2">Failed ({analysisResults.analyses.filter(a => a.status === 'error').length})</h4>
                          <ul className="list-disc pl-5 text-red-700">
                            {analysisResults.analyses
                              .filter(a => a.status === 'error')
                              .map(result => (
                                <li key={result.report_id} className="mb-1">
                                  {result.report_name}: {result.error}
                                </li>
                              ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="analyses" className="mt-0">
            <div className="mb-6">
              <div className="relative max-w-md w-full">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search analyses by report name..."
                  className="pl-9"
                  value={analysisSearchTerm}
                  onChange={(e) => setAnalysisSearchTerm(e.target.value)}
                  disabled={isLoadingAnalysis}
                />
              </div>
            </div>
            
            {isLoadingAnalysis ? (
              <div className="flex justify-center items-center py-12">
                <Loader className="text-brand-600" />
              </div>
            ) : analysisError ? (
              <Card className="border-red-200 bg-red-50">
                <CardHeader>
                  <CardTitle className="text-red-700 flex items-center">
                    <Info className="mr-2 h-5 w-5" />
                    Error Loading Analyses
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-red-600">
                    There was an error loading the analyses. Please check your connection and try again.
                  </p>
                </CardContent>
                <CardFooter>
                  <Button 
                    onClick={() => refetchProjectAnalysis()}
                    variant="outline"
                    className="text-red-600 border-red-200 hover:bg-red-50"
                  >
                    Retry
                  </Button>
                </CardFooter>
              </Card>
            ) : !filteredAnalyses?.length ? (
              <div className="text-center py-12">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
                  <Database className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium mb-2">No Analyses Found</h3>
                <p className="text-muted-foreground">
                  {analysisSearchTerm 
                    ? `No analyses match "${analysisSearchTerm}"`
                    : "There are no analyses available for this project"
                  }
                </p>
                <Button 
                  className="mt-4"
                  onClick={() => setActiveTab("reports")}
                >
                  Go to Reports to Analyze
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-4">
                {filteredAnalyses.map((analysis, index) => (
                  <Card 
                    key={analysis.reportId} 
                    className={`transition-all duration-300 animate-slide-up delay-${index * 50}`}
                  >
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="p-2 rounded-lg bg-blue-100 mr-3">
                            <Database className="h-5 w-5 text-blue-600" />
                          </div>
                          <CardTitle>{analysis.reportName}</CardTitle>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-muted-foreground">
                            Model Type: <span className="font-medium capitalize">{analysis.modelType}</span>
                          </span>
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                        <div>
                          <h4 className="font-medium text-gray-700 mb-2">Tables ({analysis.tableCount})</h4>
                          <div className="bg-gray-50 p-3 rounded-md max-h-48 overflow-y-auto">
                            <ul className="list-disc pl-5">
                              {analysis.analysis.main_tables?.map(table => (
                                <li key={table.id} className="mb-1">
                                  <strong>{table.name}</strong> <span className="text-xs text-gray-500">({table.type})</span>
                                  <ul className="list-none pl-4 text-sm text-gray-600">
                                    {table.fields.slice(0, 3).map(field => (
                                      <li key={field.name} className="truncate">
                                        • {field.name} <span className="text-xs text-gray-500">({field.type})</span>
                                      </li>
                                    ))}
                                    {table.fields.length > 3 && (
                                      <li className="text-xs text-gray-500">
                                        +{table.fields.length - 3} more fields
                                      </li>
                                    )}
                                  </ul>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                        
                        <div>
                          <h4 className="font-medium text-gray-700 mb-2">Relationships ({analysis.relationshipCount})</h4>
                          <div className="bg-gray-50 p-3 rounded-md max-h-48 overflow-y-auto">
                            <ul className="list-disc pl-5">
                              {analysis.analysis.relationships?.map((rel, i) => (
                                <li key={i} className="mb-1">
                                  <span className="text-sm">
                                    <strong>{rel.source}</strong> → <strong>{rel.target}</strong>
                                    <div className="text-xs text-gray-500">
                                      {rel.sourceField} → {rel.targetField} ({rel.relationship})
                                    </div>
                                  </span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                      
                      <div className="bg-blue-50 p-3 rounded-md">
                        <h4 className="font-medium text-blue-800 mb-2">Model Structure</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h5 className="text-sm font-medium text-blue-700">Fact Tables</h5>
                            <ul className="list-disc pl-5 text-sm">
                              {analysis.analysis.model_structure?.fact_tables.map(table => (
                                <li key={table}>{table}</li>
                              ))}
                            </ul>
                          </div>
                          <div>
                            <h5 className="text-sm font-medium text-blue-700">Dimension Tables</h5>
                            <ul className="list-disc pl-5 text-sm">
                              {analysis.analysis.model_structure?.dimension_tables.map(table => (
                                <li key={table}>{table}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                        <div className="mt-2">
                          <h5 className="text-sm font-medium text-blue-700">Description</h5>
                          <p className="text-sm text-blue-800">
                            {analysis.analysis.model_structure?.description || "No description available"}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                    
                    <CardFooter>
                      <Button 
                        variant="default" 
                        className="bg-blue-600 hover:bg-blue-700"
                        icon={<FileText className="h-4 w-4" />}
                        iconPosition="right"
                        onClick={() => navigate(`/reports/${analysis.reportId}`)}
                      >
                        View Source Report
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Bulk Export Modal */}
        {selectedProject && reports && (
          <BulkPowerBIExportModal
            isOpen={isBulkExportOpen}
            onClose={() => setIsBulkExportOpen(false)}
            reports={reports}
            projectId={selectedProject.id}
          />
        )}
      </div>
    </div>
  );
};

export default FFSQLReportsPage;