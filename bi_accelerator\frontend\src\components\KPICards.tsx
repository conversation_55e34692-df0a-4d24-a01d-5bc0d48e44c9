import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Users, UserCheck, UserX, FileText, ClipboardCheck, ClipboardX } from "lucide-react";

// KPI filter types - keep in sync with Index.tsx
type KPIFilter = 'all' | 'totalReports' | 'reportsRunLastYear' | 'reportsNeverRunLastTwoYears';

interface KPICardsProps {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  totalReports: number;
  reportsRunLastYear: number;
  reportsNeverRunLastTwoYears: number;
  activeKPIFilter: KPIFilter;
  onKPIFilterChange: (filter: KPIFilter) => void;
}

export function KPICards({ 
  totalUsers, 
  activeUsers, 
  inactiveUsers, 
  totalReports, 
  reportsRunLastYear, 
  reportsNeverRunLastTwoYears,
  activeKPIFilter,
  onKPIFilterChange
}: KPICardsProps) {
  return (
    <div className="grid gap-6 md:grid-cols-3 lg:grid-cols-6">
      <Card className="overflow-hidden border-0 shadow-md hover:shadow-lg transition-shadow">
        <div className="h-2 bg-orange-400" />
        <CardContent className="pt-6 pb-5 px-6">
          <div className="flex flex-col">
            <div className="flex items-center gap-3 mb-3">
              <div className="h-10 w-10 rounded-lg bg-orange-100 flex items-center justify-center">
                <Users className="h-5 w-5 text-orange-500" />
              </div>
              <p className="text-sm font-medium text-muted-foreground">Total Users</p>
            </div>
            <p className="text-3xl font-bold text-orange-600">{totalUsers}</p>
          </div>
        </CardContent>
      </Card>
      
      <Card className="overflow-hidden border-0 shadow-md hover:shadow-lg transition-shadow">
        <div className="h-2 bg-green-500" />
        <CardContent className="pt-6 pb-5 px-6">
          <div className="flex flex-col">
            <div className="flex items-center gap-3 mb-3">
              <div className="h-10 w-10 rounded-lg bg-green-100 flex items-center justify-center">
                <UserCheck className="h-5 w-5 text-green-600" />
              </div>
              <p className="text-sm font-medium text-muted-foreground">Active Users</p>
            </div>
            <p className="text-3xl font-bold text-green-600">{activeUsers}</p>
          </div>
        </CardContent>
      </Card>
      
      <Card className="overflow-hidden border-0 shadow-md hover:shadow-lg transition-shadow">
        <div className="h-2 bg-red-500" />
        <CardContent className="pt-6 pb-5 px-6">
          <div className="flex flex-col">
            <div className="flex items-center gap-3 mb-3">
              <div className="h-10 w-10 rounded-lg bg-red-100 flex items-center justify-center">
                <UserX className="h-5 w-5 text-red-600" />
              </div>
              <p className="text-sm font-medium text-muted-foreground">Inactive Users</p>
            </div>
            <p className="text-3xl font-bold text-red-600">{inactiveUsers}</p>
          </div>
        </CardContent>
      </Card>

      <Card 
        className={`overflow-hidden border-0 shadow-md hover:shadow-lg transition-shadow cursor-pointer ${
          activeKPIFilter === 'totalReports' ? 'ring-2 ring-blue-500 ring-offset-2' : ''
        }`}
        onClick={() => onKPIFilterChange('totalReports')}
      >
        <div className="h-2 bg-blue-500" />
        <CardContent className="pt-6 pb-5 px-6">
          <div className="flex flex-col">
            <div className="flex items-center gap-3 mb-3">
              <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                <FileText className="h-5 w-5 text-blue-600" />
              </div>
              <p className="text-sm font-medium text-muted-foreground">
                Total Reports
                {activeKPIFilter === 'totalReports' && (
                  <span className="ml-1 text-blue-500">•</span>
                )}
              </p>
            </div>
            <p className="text-3xl font-bold text-blue-600">{totalReports}</p>
            {activeKPIFilter === 'totalReports' && (
              <p className="text-xs text-blue-500 mt-1">Click to reset filter</p>
            )}
            {activeKPIFilter !== 'totalReports' && (
              <p className="text-xs text-muted-foreground mt-1">Click to filter</p>
            )}
          </div>
        </CardContent>
      </Card>
      
      <Card 
        className={`overflow-hidden border-0 shadow-md hover:shadow-lg transition-shadow cursor-pointer ${
          activeKPIFilter === 'reportsRunLastYear' ? 'ring-2 ring-indigo-500 ring-offset-2' : ''
        }`}
        onClick={() => onKPIFilterChange('reportsRunLastYear')}
      >
        <div className="h-2 bg-indigo-500" />
        <CardContent className="pt-6 pb-5 px-6">
          <div className="flex flex-col">
            <div className="flex items-center gap-3 mb-3">
              <div className="h-10 w-10 rounded-lg bg-indigo-100 flex items-center justify-center">
                <ClipboardCheck className="h-5 w-5 text-indigo-600" />
              </div>
              <p className="text-sm font-medium text-muted-foreground">
                Reports Run (12mo)
                {activeKPIFilter === 'reportsRunLastYear' && (
                  <span className="ml-1 text-indigo-500">•</span>
                )}
              </p>
            </div>
            <p className="text-3xl font-bold text-indigo-600">{reportsRunLastYear}</p>
            {activeKPIFilter === 'reportsRunLastYear' && (
              <p className="text-xs text-indigo-500 mt-1">Click to reset filter</p>
            )}
            {activeKPIFilter !== 'reportsRunLastYear' && (
              <p className="text-xs text-muted-foreground mt-1">Click to filter</p>
            )}
          </div>
        </CardContent>
      </Card>
      
      <Card 
        className={`overflow-hidden border-0 shadow-md hover:shadow-lg transition-shadow cursor-pointer ${
          activeKPIFilter === 'reportsNeverRunLastTwoYears' ? 'ring-2 ring-amber-500 ring-offset-2' : ''
        }`}
        onClick={() => onKPIFilterChange('reportsNeverRunLastTwoYears')}
      >
        <div className="h-2 bg-amber-500" />
        <CardContent className="pt-6 pb-5 px-6">
          <div className="flex flex-col">
            <div className="flex items-center gap-3 mb-3">
              <div className="h-10 w-10 rounded-lg bg-amber-100 flex items-center justify-center">
                <ClipboardX className="h-5 w-5 text-amber-600" />
              </div>
              <p className="text-sm font-medium text-muted-foreground">
                Never Run (2yr)
                {activeKPIFilter === 'reportsNeverRunLastTwoYears' && (
                  <span className="ml-1 text-amber-500">•</span>
                )}
              </p>
            </div>
            <p className="text-3xl font-bold text-amber-600">{reportsNeverRunLastTwoYears}</p>
            {activeKPIFilter === 'reportsNeverRunLastTwoYears' && (
              <p className="text-xs text-amber-500 mt-1">Click to reset filter</p>
            )}
            {activeKPIFilter !== 'reportsNeverRunLastTwoYears' && (
              <p className="text-xs text-muted-foreground mt-1">Click to filter</p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 