from fastapi import APIRouter, HTTPException, Request, Body, Response, Form
import logging
import traceback
import os
import json
import asyncio
import openai
from pydantic import BaseModel, Field
from typing import Optional, List
from concurrent.futures import Thread<PERSON>oolExecutor
from datetime import datetime
from fastapi.responses import StreamingResponse
from io import StringIO, BytesIO
import docx
from docx import Document
from docx.shared import Pt, Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH

from mstrio.project_objects import Report
from routers.utils import get_connection
from routers.sql_analysis import analyze_sql

# Configure logging
logger = logging.getLogger("bi_accelerator")

# Initialize router
router = APIRouter(prefix="/api", tags=["sql_batch_analysis"])

# Define the path for storing report analyses
ANALYSIS_FILE_PATH = "data/all_reports_analysis.json"

# Ensure data directory exists
os.makedirs("data", exist_ok=True)

# Define the request models
class ReportIdsRequest(BaseModel):
    project_id: str = Field(..., description="The project ID containing the reports")
    report_ids: List[str] = Field(..., description="List of report IDs to analyze")
    
    class Config:
        json_schema_extra = {
            "example": {
                "project_id": "B19DEDCC11D4E0EFC000EB9495D0F44F",
                "report_ids": ["ACCD055D4A13544F5700C7B1AE2B9D68", "4A994B214B91B84C50FC96A45646EF15"]
            }
        }

class ProjectIdRequest(BaseModel):
    project_id: str = Field(..., description="The project ID to retrieve analysis for")
    
    class Config:
        json_schema_extra = {
            "example": {
                "project_id": "B19DEDCC11D4E0EFC000EB9495D0F44F"
            }
        }

class DownloadFormatRequest(BaseModel):
    project_id: str = Field(..., description="The project ID to retrieve analysis for")
    format: str = Field("docx", description="The download format (only docx is supported)")
    
    class Config:
        json_schema_extra = {
            "example": {
                "project_id": "B19DEDCC11D4E0EFC000EB9495D0F44F",
                "format": "docx"
            }
        }

# Function to read the analysis JSON file
def read_analysis_file():
    try:
        if os.path.exists(ANALYSIS_FILE_PATH):
            with open(ANALYSIS_FILE_PATH, 'r') as f:
                return json.load(f)
        return {}
    except Exception as e:
        logger.error(f"Error reading analysis file: {str(e)}")
        return {}

# Function to write to the analysis JSON file
def write_analysis_file(data):
    try:
        with open(ANALYSIS_FILE_PATH, 'w') as f:
            json.dump(data, f, indent=2)
        return True
    except Exception as e:
        logger.error(f"Error writing to analysis file: {str(e)}")
        return False

# Function to analyze a single report
async def analyze_report(project_id, report_id, project_conn=None):
    try:
        # Get connection if not provided
        if not project_conn:
            project_conn = get_connection(project_id)
        
        # Get the report object
        report = Report(connection=project_conn, id=report_id)
        
        # Extract SQL
        sql = report.sql
        
        if not sql:
            return {
                "report_id": report_id,
                "report_name": report.name,
                "status": "error",
                "error": "No SQL available for this report"
            }
        
        # Analyze the SQL
        class SQLRequest:
            def __init__(self, sql_str):
                self.sql = sql_str
        
        sql_request = SQLRequest(sql)
        analysis_result = await analyze_sql(sql_request)
        
        # Return the result with report info
        return {
            "report_id": report_id,
            "report_name": report.name,
            "status": "success",
            "analysis": analysis_result
        }
    except Exception as e:
        logger.error(f"Error analyzing report {report_id}: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            "report_id": report_id,
            "status": "error",
            "error": str(e)
        }

# Function to process batch report analysis
async def process_batch_reports(project_id, report_ids):
    try:
        # Get connection (shared among all reports)
        project_conn = get_connection(project_id)
        
        # Create tasks for each report analysis
        tasks = []
        for report_id in report_ids:
            tasks.append(analyze_report(project_id, report_id, project_conn))
        
        # Process reports concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"Error in batch processing: {str(result)}")
                continue
            processed_results.append(result)
        
        return processed_results
    except Exception as e:
        logger.error(f"Error in batch processing: {str(e)}")
        logger.error(traceback.format_exc())
        raise

@router.post("/analyze-reports")
async def analyze_reports(request: ReportIdsRequest):
    """
    Analyze multiple reports' SQL and save the results to a JSON file.
    If analyses for these reports already exist, they will be updated.
    """
    try:
        logger.info(f"Received request to analyze {len(request.report_ids)} reports for project {request.project_id}")
        
        # Process the reports
        analysis_results = await process_batch_reports(request.project_id, request.report_ids)
        
        # Read existing analysis data
        all_analyses = read_analysis_file()
        
        # Initialize project entry if not exists
        if request.project_id not in all_analyses:
            all_analyses[request.project_id] = {}
        
        # Update/add analysis for each report
        for result in analysis_results:
            if result.get("status") == "success" and "report_id" in result:
                all_analyses[request.project_id][result["report_id"]] = {
                    "report_name": result.get("report_name", "Unknown"),
                    "analysis": result.get("analysis", {})
                }
        
        # Save updated analyses
        write_success = write_analysis_file(all_analyses)
        if not write_success:
            logger.warning("Failed to save analysis to file, but returning results anyway")
        
        logger.info(f"Successfully analyzed {len(analysis_results)} reports for project {request.project_id}")
        return {
            "project_id": request.project_id,
            "analyses_count": len(analysis_results),
            "analyses": analysis_results
        }
    except Exception as e:
        logger.error(f"Error analyzing reports: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to analyze reports: {str(e)}")

@router.post("/get-project-analysis")
async def get_project_analysis(request: ProjectIdRequest):
    """
    Retrieve all report analyses for a specific project from the JSON file.
    """
    try:
        logger.info(f"Retrieving analysis for project {request.project_id}")
        
        # Read analysis data
        all_analyses = read_analysis_file()
        
        # Check if project exists
        if request.project_id not in all_analyses:
            return {"project_id": request.project_id, "analyses": {}}
        
        # Return project analyses
        logger.info(f"Successfully retrieved analyses for project {request.project_id}")
        return {
            "project_id": request.project_id,
            "analyses": all_analyses[request.project_id]
        }
    except Exception as e:
        logger.error(f"Error retrieving project analysis: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to retrieve project analysis: {str(e)}")

@router.delete("/delete-project-analysis")
async def delete_project_analysis(request: ProjectIdRequest):
    """
    Delete all report analyses for a specific project from the JSON file.
    """
    try:
        logger.info(f"Deleting analysis for project {request.project_id}")
        
        # Read analysis data
        all_analyses = read_analysis_file()
        
        # Check if project exists
        if request.project_id not in all_analyses:
            return {"message": f"No analysis found for project {request.project_id}"}
        
        # Delete project analyses
        del all_analyses[request.project_id]
        
        # Save updated analyses
        write_success = write_analysis_file(all_analyses)
        if not write_success:
            raise HTTPException(status_code=500, detail="Failed to save updated analysis file")
        
        logger.info(f"Successfully deleted analyses for project {request.project_id}")
        return {"message": f"Successfully deleted analyses for project {request.project_id}"}
    except Exception as e:
        logger.error(f"Error deleting project analysis: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to delete project analysis: {str(e)}")

@router.post("/download-project-analysis")
async def download_project_analysis(
    request: DownloadFormatRequest = None,
    project_id: str = Form(None),
    format: str = Form("docx")
):
    """
    Download all report analyses for a specific project as a Word document (.docx).
    Accepts both JSON body and form data.
    """
    try:
        # Handle both JSON body and form data
        if request is None and project_id is not None:
            # Create a request object from form data, default to docx format
            request = DownloadFormatRequest(project_id=project_id, format="docx")
        
        if request is None:
            raise HTTPException(status_code=400, detail="Missing required parameters")
            
        logger.info(f"Downloading analysis for project {request.project_id} as Word document")
        
        # Read analysis data
        all_analyses = read_analysis_file()
        
        # Check if project exists
        if request.project_id not in all_analyses:
            raise HTTPException(status_code=404, detail=f"No analysis found for project {request.project_id}")
        
        project_analyses = all_analyses[request.project_id]
        
        if not project_analyses:
            raise HTTPException(status_code=404, detail=f"No analyses found for project {request.project_id}")
        
        # Create a Word document using python-docx
        doc = Document()
        
        # Set up document styling
        style = doc.styles['Normal']
        font = style.font
        font.name = 'Calibri'
        font.size = Pt(11)
        
        # Add document title
        title = doc.add_heading('FFSQL Reports Analysis', level=1)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add generation timestamp
        timestamp_para = doc.add_paragraph(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        timestamp_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        doc.add_paragraph()  # Add blank line
        
        # Process each report
        for report_id, data in project_analyses.items():
            report_name = data.get("report_name", "Unknown")
            analysis = data.get("analysis", {})
            
            # Add report heading
            doc.add_heading(f"Report: {report_name}", level=2)
            doc.add_paragraph(f"ID: {report_id}")
            
            # Add horizontal line
            doc.add_paragraph("_" * 50)
            
            # Model Structure
            if "model_structure" in analysis:
                ms = analysis["model_structure"]
                doc.add_heading("Model Structure", level=3)
                doc.add_paragraph(f"Model Type: {ms.get('model_type', 'Unknown')}")
                
                if "description" in ms:
                    doc.add_paragraph(f"Description: {ms['description']}")
                
                if "fact_tables" in ms and ms["fact_tables"]:
                    doc.add_heading("Fact Tables", level=4)
                    fact_tables = doc.add_paragraph()
                    for table in ms["fact_tables"]:
                        fact_tables.add_run(f"• {table}\n")
                
                if "dimension_tables" in ms and ms["dimension_tables"]:
                    doc.add_heading("Dimension Tables", level=4)
                    dim_tables = doc.add_paragraph()
                    for table in ms["dimension_tables"]:
                        dim_tables.add_run(f"• {table}\n")
            
            # Tables
            if "main_tables" in analysis and analysis["main_tables"]:
                doc.add_heading("Tables", level=3)
                for table in analysis["main_tables"]:
                    doc.add_paragraph(f"• {table.get('name', 'Unknown')} (Type: {table.get('type', 'Unknown')})")
                    
                    if "fields" in table and table["fields"]:
                        fields_para = doc.add_paragraph()
                        fields_para.paragraph_format.left_indent = Inches(0.25)
                        fields_para.add_run("Fields:\n")
                        
                        for field in table["fields"]:
                            fields_para.add_run(f"  - {field.get('name', 'Unknown')} ({field.get('type', 'Unknown')})\n")
            
            # Relationships
            if "relationships" in analysis and analysis["relationships"]:
                doc.add_heading("Relationships", level=3)
                for rel in analysis["relationships"]:
                    rel_para = doc.add_paragraph()
                    rel_para.add_run(f"• {rel.get('source', 'Unknown')} → {rel.get('target', 'Unknown')}\n")
                    rel_para.add_run(f"  ({rel.get('sourceField', 'Unknown')} → {rel.get('targetField', 'Unknown')})\n")
                    rel_para.add_run(f"  Type: {rel.get('relationship', 'Unknown')}")
            
            # Add a page break between reports
            doc.add_page_break()
        
        # Save the document to a BytesIO object
        docx_binary = BytesIO()
        doc.save(docx_binary)
        docx_binary.seek(0)  # Move to the beginning of the stream
        
        # Create a timestamp for the filename
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        filename = f"ffsql_analysis_{request.project_id}_{timestamp}.docx"
        
        headers = {
            "Content-Disposition": f'attachment; filename="{filename}"'
        }
        
        logger.info(f"Successfully generated Word document with filename {filename}")
        
        # Return the document as a streaming response
        return StreamingResponse(
            docx_binary,
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            headers=headers
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating document: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to generate document: {str(e)}"
        )

@router.post("/download-reports-sql")
async def download_reports_sql(
    request: ReportIdsRequest = None,
    project_id: str = Form(None),
    report_ids: List[str] = Form(None)
):
    """
    Download SQL for selected reports in an Excel file.
    Includes project name, report name, and the SQL for each report.
    """
    try:
        # Handle both JSON body and form data
        if request is None and project_id is not None and report_ids is not None:
            # Create a request object from form data
            request = ReportIdsRequest(project_id=project_id, report_ids=report_ids)
        
        if request is None or not request.report_ids:
            raise HTTPException(status_code=400, detail="Missing required parameters or no reports selected")
            
        logger.info(f"Downloading SQL for {len(request.report_ids)} reports from project {request.project_id}")
        
        # Import pandas and io for Excel generation
        import pandas as pd
        from io import BytesIO
        import re
        
        # Get connection
        project_conn = get_connection(request.project_id)
        
        # Get project name - fix the method
        # We can't access projects directly from connection object
        project_name = f"Project {request.project_id}"  # Default project name with ID
        
        # Create Excel file in memory
        output = BytesIO()
        
        # Function to sanitize sheet names for Excel
        def sanitize_sheet_name(name, max_length=31):
            # Remove invalid Excel sheet characters: / \ * ? : [ ]
            sanitized = re.sub(r'[\\/\*\?\:\[\]]', '_', name)
            # Truncate to max length (31 chars for Excel)
            if len(sanitized) > max_length:
                sanitized = sanitized[:max_length-3] + '...'
            # Ensure the name is not empty
            if not sanitized:
                sanitized = "Report"
            return sanitized
        
        # Create a pandas Excel writer using openpyxl as the engine
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            # Create an overview sheet with project info
            overview_df = pd.DataFrame({
                'Project Information': [
                    f"Project ID: {request.project_id}",
                    f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    f"Number of Reports: {len(request.report_ids)}"
                ]
            })
            overview_df.to_excel(writer, sheet_name='Overview', index=False)
            
            # Keep track of used sheet names to avoid duplicates
            used_sheet_names = {'Overview'}
            
            # Process each report
            for i, report_id in enumerate(request.report_ids):
                try:
                    # Get the report
                    report = Report(connection=project_conn, id=report_id)
                    report_name = report.name
                    
                    # Extract SQL
                    sql = report.sql
                    
                    # Create a sheet name using the report name
                    sheet_name = sanitize_sheet_name(report_name)
                    
                    # Handle duplicate sheet names
                    original_sheet_name = sheet_name
                    counter = 1
                    while sheet_name in used_sheet_names:
                        # If name exists, append a number
                        suffix = f"_{counter}"
                        sheet_name = sanitize_sheet_name(
                            original_sheet_name, 
                            max_length=31-len(suffix)
                        ) + suffix
                        counter += 1
                    
                    # Add to used names
                    used_sheet_names.add(sheet_name)
                    
                    # Create a dataframe for this report
                    report_df = pd.DataFrame({
                        'Attribute': ['Report Name', 'Report ID', 'SQL'],
                        'Value': [report_name, report_id, sql if sql else "No SQL available"]
                    })
                    
                    # Write to Excel
                    report_df.to_excel(writer, sheet_name=sheet_name, index=False)
                    
                    # Adjust column widths
                    worksheet = writer.sheets[sheet_name]
                    worksheet.column_dimensions['A'].width = 15
                    worksheet.column_dimensions['B'].width = 100
                    
                except Exception as e:
                    logger.error(f"Error processing report {report_id}: {str(e)}")
                    
                    # Create a sheet for the error report
                    error_sheet_name = f"Error_{i+1}"
                    while error_sheet_name in used_sheet_names:
                        error_sheet_name = f"Error_{i+1}_{counter}"
                        counter += 1
                    
                    used_sheet_names.add(error_sheet_name)
                    
                    error_df = pd.DataFrame({
                        'Attribute': ['Report ID', 'Error'],
                        'Value': [report_id, str(e)]
                    })
                    error_df.to_excel(writer, sheet_name=error_sheet_name, index=False)
        
        # Get the value from the output
        output.seek(0)
        
        # Create a timestamp for the filename
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        filename = f"ffsql_reports_sql_{request.project_id}_{timestamp}.xlsx"
        
        headers = {
            "Content-Disposition": f'attachment; filename="{filename}"'
        }
        
        logger.info(f"Successfully generated Excel file with filename {filename}")
        
        # Return the Excel file as a streaming response
        return StreamingResponse(
            output,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers=headers
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating Excel file: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to generate Excel file: {str(e)}"
        ) 