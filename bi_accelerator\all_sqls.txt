--- SQL Query 1 ---
create table TemplateTable for CB_15Min (
	[SA15MBEGIN]	DATETIME, 
	[SA15MEND]	DATETIME, 
	[SADAYID]	INTEGER, 
	[SAWKID]	INTEGER, 
	[SAMNTHID]	INTEGER, 
	[SAQTRID]	INTEGER, 
	[SAYRID]	INTEGER, 
	[SADAYOFWKID]	INTEGER, 
	[SAHROFDAYID]	INTEGER)


[Analytical SQL calculated by the Analytical Engine:
	select		CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, mn, 15),
		CreateTimeSeries<IsBeginning = false>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, mn, 15),
		((Year(SA15MBEGIN) * 1000) + DayOfYear(SA15MBEGIN)),
		((Year(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SA15MBEGIN), 6)) * 100) + Week<Calendar = Gregorian Calendar>(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SA15MBEGIN), 6))),
		((Year(SA15MBEGIN) * 100) + Month(SA15MBEGIN)),
		((Year(SA15MBEGIN) * 100) + Quarter(SA15MBEGIN)),
		Year(SA15MBEGIN),
		DayOfWeek<Calendar = Gregorian Calendar>(Date(SA15MBEGIN)),
		Hour(SA15MBEGIN)
	from	[previous pass]
]

insert into TemplateTable for CB_15Min values ([Analytical Engine Results: CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, mn, 15), CreateTimeSeries<IsBeginning = false>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, mn, 15), ((Year(SA15MBEGIN) * 1000) + DayOfYear(SA15MBEGIN)), ((Year(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SA15MBEGIN), 6)) * 100) + Week<Calendar = Gregorian Calendar>(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SA15MBEGIN), 6))), ((Year(SA15MBEGIN) * 100) + Month(SA15MBEGIN)), ((Year(SA15MBEGIN) * 100) + Quarter(SA15MBEGIN)), Year(SA15MBEGIN), DayOfWeek<Calendar = Gregorian Calendar>(Date(SA15MBEGIN)), Hour(SA15MBEGIN)])

create table TemplateTable for CB_30Min (
	[SA30MBEGIN]	DATETIME, 
	[SA30MEND]	DATETIME, 
	[SADAYID]	INTEGER, 
	[SAWKID]	INTEGER, 
	[SAMNTHID]	INTEGER, 
	[SAQTRID]	INTEGER, 
	[SAYRID]	INTEGER, 
	[SADAYOFWKID]	INTEGER, 
	[SAHROFDAYID]	INTEGER)


[Analytical SQL calculated by the Analytical Engine:
	select		CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, mn, 30),
		CreateTimeSeries<IsBeginning = false>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, mn, 30),
		((Year(SA30MBEGIN) * 1000) + DayOfYear(SA30MBEGIN)),
		((Year(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SA30MBEGIN), 6)) * 100) + Week<Calendar = Gregorian Calendar>(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SA30MBEGIN), 6))),
		((Year(SA30MBEGIN) * 100) + Month(SA30MBEGIN)),
		((Year(SA30MBEGIN) * 100) + Quarter(SA30MBEGIN)),
		Year(SA30MBEGIN),
		DayOfWeek<Calendar = Gregorian Calendar>(Date(SA30MBEGIN)),
		Hour(SA30MBEGIN)
	from	[previous pass]
]

insert into TemplateTable for CB_30Min values ([Analytical Engine Results: CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, mn, 30), CreateTimeSeries<IsBeginning = false>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, mn, 30), ((Year(SA30MBEGIN) * 1000) + DayOfYear(SA30MBEGIN)), ((Year(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SA30MBEGIN), 6)) * 100) + Week<Calendar = Gregorian Calendar>(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SA30MBEGIN), 6))), ((Year(SA30MBEGIN) * 100) + Month(SA30MBEGIN)), ((Year(SA30MBEGIN) * 100) + Quarter(SA30MBEGIN)), Year(SA30MBEGIN), DayOfWeek<Calendar = Gregorian Calendar>(Date(SA30MBEGIN)), Hour(SA30MBEGIN)])

create table TemplateTable for CB_60Min (
	[SAHOURBEGIN]	DATETIME, 
	[SAHOUREND]	DATETIME, 
	[SADAYID]	INTEGER, 
	[SAWKID]	INTEGER, 
	[SAMNTHID]	INTEGER, 
	[SAQTRID]	INTEGER, 
	[SAYRID]	INTEGER, 
	[SADAYOFWKID]	INTEGER, 
	[SAHROFDAYID]	INTEGER)


[Analytical SQL calculated by the Analytical Engine:
	select		CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, mn, 60),
		CreateTimeSeries<IsBeginning = false>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, mn, 60),
		((Year(SAHOURBEGIN) * 1000) + DayOfYear(SAHOURBEGIN)),
		((Year(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SAHOURBEGIN), 6)) * 100) + Week<Calendar = Gregorian Calendar>(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SAHOURBEGIN), 6))),
		((Year(SAHOURBEGIN) * 100) + Month(SAHOURBEGIN)),
		((Year(SAHOURBEGIN) * 100) + Quarter(SAHOURBEGIN)),
		Year(SAHOURBEGIN),
		DayOfWeek<Calendar = Gregorian Calendar>(Date(SAHOURBEGIN)),
		Hour(SAHOURBEGIN)
	from	[previous pass]
]

insert into TemplateTable for CB_60Min values ([Analytical Engine Results: CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, mn, 60), CreateTimeSeries<IsBeginning = false>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, mn, 60), ((Year(SAHOURBEGIN) * 1000) + DayOfYear(SAHOURBEGIN)), ((Year(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SAHOURBEGIN), 6)) * 100) + Week<Calendar = Gregorian Calendar>(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SAHOURBEGIN), 6))), ((Year(SAHOURBEGIN) * 100) + Month(SAHOURBEGIN)), ((Year(SAHOURBEGIN) * 100) + Quarter(SAHOURBEGIN)), Year(SAHOURBEGIN), DayOfWeek<Calendar = Gregorian Calendar>(Date(SAHOURBEGIN)), Hour(SAHOURBEGIN)])

create table TemplateTable for CB_Day (
	[SADAYBEGIN]	DATETIME, 
	[SADAYEND]	DATETIME, 
	[SADAYID]	INTEGER, 
	[SAWKID]	INTEGER, 
	[SAMNTHID]	INTEGER, 
	[SAQTRID]	INTEGER, 
	[SAYRID]	INTEGER, 
	[SADAYOFWKID]	INTEGER)


[Analytical SQL calculated by the Analytical Engine:
	select		CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, d, 1),
		CreateTimeSeries<IsBeginning = false>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, d, 1),
		((Year(SADAYBEGIN) * 1000) + DayOfYear(SADAYBEGIN)),
		((Year(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SADAYBEGIN), 6)) * 100) + Week<Calendar = Gregorian Calendar>(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SADAYBEGIN), 6))),
		((Year(SADAYBEGIN) * 100) + Month(SADAYBEGIN)),
		((Year(SADAYBEGIN) * 100) + Quarter(SADAYBEGIN)),
		Year(SADAYBEGIN),
		DayOfWeek<Calendar = Gregorian Calendar>(Date(SADAYBEGIN))
	from	[previous pass]
]

insert into TemplateTable for CB_Day values ([Analytical Engine Results: CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, d, 1), CreateTimeSeries<IsBeginning = false>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, d, 1), ((Year(SADAYBEGIN) * 1000) + DayOfYear(SADAYBEGIN)), ((Year(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SADAYBEGIN), 6)) * 100) + Week<Calendar = Gregorian Calendar>(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SADAYBEGIN), 6))), ((Year(SADAYBEGIN) * 100) + Month(SADAYBEGIN)), ((Year(SADAYBEGIN) * 100) + Quarter(SADAYBEGIN)), Year(SADAYBEGIN), DayOfWeek<Calendar = Gregorian Calendar>(Date(SADAYBEGIN))])

create table TemplateTable for CB_Week (
	[SAWKBEGIN]	DATETIME, 
	[SAWKEND]	DATETIME, 
	[SAWKID]	INTEGER)


[Analytical SQL calculated by the Analytical Engine:
	select		CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, w, 1),
		CreateTimeSeries<IsBeginning = false>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, w, 1),
		((Year(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SAWKBEGIN), 6)) * 100) + Week<Calendar = Gregorian Calendar>(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SAWKBEGIN), 6)))
	from	[previous pass]
]

insert into TemplateTable for CB_Week values ([Analytical Engine Results: CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, w, 1), CreateTimeSeries<IsBeginning = false>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, w, 1), ((Year(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SAWKBEGIN), 6)) * 100) + Week<Calendar = Gregorian Calendar>(AddDays(WeekStartDate<Calendar = Gregorian Calendar>(SAWKBEGIN), 6)))])

create table TemplateTable for CB_Month (
	[SAMNTHBEGIN]	DATETIME, 
	[SAMNTHEND]	DATETIME, 
	[SAMNTHID]	INTEGER, 
	[SAQTRID]	INTEGER, 
	[SAYRID]	INTEGER)


[Analytical SQL calculated by the Analytical Engine:
	select		CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, m, 1),
		CreateTimeSeries<IsBeginning = false>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, m, 1),
		((Year(SAMNTHBEGIN) * 100) + Month(SAMNTHBEGIN)),
		((Year(SAMNTHBEGIN) * 100) + Quarter(SAMNTHBEGIN)),
		Year(SAMNTHBEGIN)
	from	[previous pass]
]

insert into TemplateTable for CB_Month values ([Analytical Engine Results: CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, m, 1), CreateTimeSeries<IsBeginning = false>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, m, 1), ((Year(SAMNTHBEGIN) * 100) + Month(SAMNTHBEGIN)), ((Year(SAMNTHBEGIN) * 100) + Quarter(SAMNTHBEGIN)), Year(SAMNTHBEGIN)])

create table TemplateTable for CB_Quarter (
	[SAQTRBEGIN]	DATETIME, 
	[SAQTREND]	DATETIME, 
	[SAQTRID]	INTEGER, 
	[SAYRID]	INTEGER)


[Analytical SQL calculated by the Analytical Engine:
	select		CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, q, 1),
		CreateTimeSeries<IsBeginning = false>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, q, 1),
		((Year(SAQTRBEGIN) * 100) + Quarter(SAQTRBEGIN)),
		Year(SAQTRBEGIN)
	from	[previous pass]
]

insert into TemplateTable for CB_Quarter values ([Analytical Engine Results: CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, q, 1), CreateTimeSeries<IsBeginning = false>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, q, 1), ((Year(SAQTRBEGIN) * 100) + Quarter(SAQTRBEGIN)), Year(SAQTRBEGIN)])

create table TemplateTable for CB_Year (
	[SAYRBEGIN]	DATETIME, 
	[SAYREND]	DATETIME, 
	[SAYRID]	INTEGER)


[Analytical SQL calculated by the Analytical Engine:
	select		CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, y, 1),
		CreateTimeSeries<IsBeginning = false>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, y, 1),
		Year(SAYRBEGIN)
	from	[previous pass]
]

insert into TemplateTable for CB_Year values ([Analytical Engine Results: CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, y, 1), CreateTimeSeries<IsBeginning = false>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, y, 1), Year(SAYRBEGIN)])

create table TemplateTable for CB_DW (
	[SADAYOFWKID]	INTEGER)


[Analytical SQL calculated by the Analytical Engine:
	select		CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, dw, 1)
	from	[previous pass]
]

insert into TemplateTable for CB_DW values ([Analytical Engine Results: CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, dw, 1)])

create table TemplateTable for CB_HD (
	[SAHROFDAYBEGIN]	DATETIME, 
	[SAHROFDAYID]	INTEGER)


[Analytical SQL calculated by the Analytical Engine:
	select		CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, hd, 1),
		Hour(SAHROFDAYBEGIN)
	from	[previous pass]
]

insert into TemplateTable for CB_HD values ([Analytical Engine Results: CreateTimeSeries<IsBeginning = true>(2023-01-01 00:00:00.000, 2026-12-31 23:59:59.999, hd, 1), Hour(SAHROFDAYBEGIN)])

[Empty SQL]

drop table TemplateTable for CB_15Min

drop table TemplateTable for CB_30Min

drop table TemplateTable for CB_60Min

drop table TemplateTable for CB_Day

drop table TemplateTable for CB_Week

drop table TemplateTable for CB_Month

drop table TemplateTable for CB_Quarter

drop table TemplateTable for CB_Year

drop table TemplateTable for CB_DW

drop table TemplateTable for CB_HD

[Analytical engine calculation steps:
	1.  Perform cross-tabbing
]


--- SQL Query 2 ---
create table #ZZMD00(
	[Year1]	BIGINT, 
	[Active]	VARCHAR(1), 
	[CustCol_2]	VARCHAR(50), 
	[Quarter]	INTEGER, 
	[ManagerId]	BIGINT, 
	[ToDate]	DATE, 
	[CustCol]	VARCHAR(15), 
	[Name]	NVARCHAR(50), 
	[Designation]	VARCHAR(50), 
	[Department]	NVARCHAR(50), 
	[WJXBFS1]	FLOAT, 
	[WJXBFS2]	FLOAT, 
	[WJXBFS3]	FLOAT, 
	[WJXBFS4]	FLOAT, 
	[WJXBFS5]	FLOAT, 
	[WJXBFS6]	FLOAT, 
	[WJXBFS7]	FLOAT, 
	[WJXBFS8]	FLOAT, 
	[WJXBFS9]	FLOAT)

insert into #ZZMD00 
select	[a14].[Year]  [Year1],
	[a12].[Active]  [Active],
	CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END  [CustCol_2],
	[a14].[Quarter]  [Quarter],
	[a13].[Manager_ID]  [ManagerId],
	[a14].[To_Date]  [ToDate],
	CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END  [CustCol],
	[a13].[Name]  [Name],
	[a12].[Designation]  [Designation],
	[a12].[Department]  [Department],
	sum([a11].[Availed_PTO])  [WJXBFS1],
	sum([a11].[Availed_Compoff])  [WJXBFS2],
	sum([a11].[Balance_Compoff])  [WJXBFS3],
	sum([a11].[Loss_of_Pay])  [WJXBFS4],
	sum([a11].[Total_WFH])  [WJXBFS5],
	sum([a11].[Total_PTO])  [WJXBFS6],
	sum([a11].[Balance_WFH])  [WJXBFS7],
	sum([a11].[Balance_PTO])  [WJXBFS8],
	sum(CASE 
        WHEN [a11].[Loss_of_Pay] = 1 THEN 1000
        WHEN [a11].[Loss_of_Pay] =2 THEN 2000 
        ELSE 0    END)  [WJXBFS9]
from	[dbo].[Leavebalance]	[a11]
	join	[dbo].[Login]	[a12]
	  on 	([a11].[Employee_ID] = [a12].[Employee_ID])
	join	[dbo].[Employee]	[a13]
	  on 	([a11].[Employee_ID] = [a13].[Employee_ID])
	join	[dbo].[QuarterProcess]	[a14]
	  on 	([a11].[Employee_ID] = [a14].[Employee_ID])
where	([a13].[Name] in (N'Employee 1', N'Employee 10', N'Employee 11', N'Employee 13', N'Employee 14', N'Employee 15', N'Employee 16', N'Employee 17', N'Employee 18', N'Employee 19')
 and [a14].[Year] in (2023))
group by	[a14].[Year],
	[a12].[Active],
	CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END,
	[a14].[Quarter],
	[a13].[Manager_ID],
	[a14].[To_Date],
	CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END,
	[a13].[Name],
	[a12].[Designation],
	[a12].[Department]

select	distinct [pa12].[ManagerId]  [ManagerId],
	[a13].[Manager_Name]  [Manager_Name],
	[pa12].[Department]  [Department],
	[pa12].[Designation]  [Designation],
	[pa12].[Quarter]  [Quarter],
	[pa12].[Active]  [Active],
	[pa12].[Year1]  [Year1],
	[pa12].[CustCol]  [CustCol],
	[pa12].[CustCol_2]  [CustCol_2],
	[pa12].[Name]  [Name],
	[a13].[Gender]  [Gender],
	[a13].[Email]  [Email],
	[pa12].[ToDate]  [ToDate],
	[pa12].[WJXBFS1]  [WJXBFS1],
	[pa12].[WJXBFS2]  [WJXBFS2],
	[pa12].[WJXBFS3]  [WJXBFS3],
	[pa12].[WJXBFS4]  [WJXBFS4],
	[pa12].[WJXBFS5]  [WJXBFS5],
	[pa12].[WJXBFS6]  [WJXBFS6],
	[pa12].[WJXBFS7]  [WJXBFS7],
	[pa12].[WJXBFS8]  [WJXBFS8],
	[pa12].[WJXBFS9]  [WJXBFS9]
from	#ZZMD00	[pa12]
	join	[dbo].[Employee]	[a13]
	  on 	([pa12].[CustCol] = CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END and 
	[pa12].[CustCol_2] = CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END and 
	[pa12].[ManagerId] = [a13].[Manager_ID] and 
	[pa12].[Name] = [a13].[Name])
where	([pa12].[Name] in (N'Employee 1', N'Employee 10', N'Employee 11', N'Employee 13', N'Employee 14', N'Employee 15', N'Employee 16', N'Employee 17', N'Employee 18', N'Employee 19')
 and [pa12].[Year1] in (2023))

create table #ZZMD01(
	[Year1]	BIGINT, 
	[Active]	VARCHAR(1), 
	[CustCol_2]	VARCHAR(50), 
	[Quarter]	INTEGER, 
	[ManagerId]	BIGINT, 
	[ToDate]	DATE, 
	[CustCol]	VARCHAR(15), 
	[Name]	NVARCHAR(50), 
	[Designation]	VARCHAR(50), 
	[Department]	NVARCHAR(50), 
	[WJXBFS1]	FLOAT, 
	[WJXBFS2]	FLOAT, 
	[WJXBFS3]	FLOAT, 
	[WJXBFS4]	FLOAT, 
	[WJXBFS5]	FLOAT, 
	[WJXBFS6]	FLOAT, 
	[WJXBFS7]	FLOAT, 
	[WJXBFS8]	FLOAT, 
	[WJXBFS9]	FLOAT)

insert into #ZZMD01 
select	[a14].[Year]  [Year1],
	[a12].[Active]  [Active],
	CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END  [CustCol_2],
	[a14].[Quarter]  [Quarter],
	[a13].[Manager_ID]  [ManagerId],
	[a14].[To_Date]  [ToDate],
	CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END  [CustCol],
	[a13].[Name]  [Name],
	[a12].[Designation]  [Designation],
	[a12].[Department]  [Department],
	sum([a11].[Availed_PTO])  [WJXBFS1],
	sum([a11].[Availed_Compoff])  [WJXBFS2],
	sum([a11].[Balance_Compoff])  [WJXBFS3],
	sum([a11].[Loss_of_Pay])  [WJXBFS4],
	sum([a11].[Total_WFH])  [WJXBFS5],
	sum([a11].[Total_PTO])  [WJXBFS6],
	sum([a11].[Balance_WFH])  [WJXBFS7],
	sum([a11].[Balance_PTO])  [WJXBFS8],
	sum(CASE 
        WHEN [a11].[Loss_of_Pay] = 1 THEN 1000
        WHEN [a11].[Loss_of_Pay] =2 THEN 2000 
        ELSE 0    END)  [WJXBFS9]
from	[dbo].[Leavebalance]	[a11]
	join	[dbo].[Login]	[a12]
	  on 	([a11].[Employee_ID] = [a12].[Employee_ID])
	join	[dbo].[Employee]	[a13]
	  on 	([a11].[Employee_ID] = [a13].[Employee_ID])
	join	[dbo].[QuarterProcess]	[a14]
	  on 	([a11].[Employee_ID] = [a14].[Employee_ID])
where	([a13].[Name] in (N'Employee 34', N'Employee 35', N'Employee 36', N'Employee 37', N'Employee 38', N'Employee 39', N'Employee 4', N'Employee 40', N'Employee 41', N'Employee 42', N'Employee 43')
 and [a14].[Year] in (2023))
group by	[a14].[Year],
	[a12].[Active],
	CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END,
	[a14].[Quarter],
	[a13].[Manager_ID],
	[a14].[To_Date],
	CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END,
	[a13].[Name],
	[a12].[Designation],
	[a12].[Department]

select	distinct [pa12].[ManagerId]  [ManagerId],
	[a13].[Manager_Name]  [Manager_Name],
	[pa12].[Department]  [Department],
	[pa12].[Designation]  [Designation],
	[pa12].[Quarter]  [Quarter],
	[pa12].[Active]  [Active],
	[pa12].[Year1]  [Year1],
	[pa12].[CustCol]  [CustCol],
	[pa12].[CustCol_2]  [CustCol_2],
	[pa12].[Name]  [Name],
	[a13].[Gender]  [Gender],
	[a13].[Email]  [Email],
	[pa12].[ToDate]  [ToDate],
	[pa12].[WJXBFS1]  [WJXBFS1],
	[pa12].[WJXBFS2]  [WJXBFS2],
	[pa12].[WJXBFS3]  [WJXBFS3],
	[pa12].[WJXBFS4]  [WJXBFS4],
	[pa12].[WJXBFS5]  [WJXBFS5],
	[pa12].[WJXBFS6]  [WJXBFS6],
	[pa12].[WJXBFS7]  [WJXBFS7],
	[pa12].[WJXBFS8]  [WJXBFS8],
	[pa12].[WJXBFS9]  [WJXBFS9]
from	#ZZMD01	[pa12]
	join	[dbo].[Employee]	[a13]
	  on 	([pa12].[CustCol] = CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END and 
	[pa12].[CustCol_2] = CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END and 
	[pa12].[ManagerId] = [a13].[Manager_ID] and 
	[pa12].[Name] = [a13].[Name])
where	([pa12].[Name] in (N'Employee 34', N'Employee 35', N'Employee 36', N'Employee 37', N'Employee 38', N'Employee 39', N'Employee 4', N'Employee 40', N'Employee 41', N'Employee 42', N'Employee 43')
 and [pa12].[Year1] in (2023))

create table #ZZMD02(
	[Year1]	BIGINT, 
	[Active]	VARCHAR(1), 
	[CustCol_2]	VARCHAR(50), 
	[Quarter]	INTEGER, 
	[ManagerId]	BIGINT, 
	[ToDate]	DATE, 
	[CustCol]	VARCHAR(15), 
	[Name]	NVARCHAR(50), 
	[Designation]	VARCHAR(50), 
	[Department]	NVARCHAR(50), 
	[WJXBFS1]	FLOAT, 
	[WJXBFS2]	FLOAT, 
	[WJXBFS3]	FLOAT, 
	[WJXBFS4]	FLOAT, 
	[WJXBFS5]	FLOAT, 
	[WJXBFS6]	FLOAT, 
	[WJXBFS7]	FLOAT, 
	[WJXBFS8]	FLOAT, 
	[WJXBFS9]	FLOAT)

insert into #ZZMD02 
select	[a15].[Year]  [Year1],
	[a12].[Active]  [Active],
	CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END  [CustCol_2],
	[a15].[Quarter]  [Quarter],
	[a13].[Manager_ID]  [ManagerId],
	[a15].[To_Date]  [ToDate],
	CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END  [CustCol],
	[a13].[Name]  [Name],
	[a12].[Designation]  [Designation],
	[a12].[Department]  [Department],
	sum([a11].[Availed_PTO])  [WJXBFS1],
	sum([a11].[Availed_Compoff])  [WJXBFS2],
	sum([a11].[Balance_Compoff])  [WJXBFS3],
	sum([a11].[Loss_of_Pay])  [WJXBFS4],
	sum([a11].[Total_WFH])  [WJXBFS5],
	sum([a11].[Total_PTO])  [WJXBFS6],
	sum([a11].[Balance_WFH])  [WJXBFS7],
	sum([a11].[Balance_PTO])  [WJXBFS8],
	sum(CASE 
        WHEN [a11].[Loss_of_Pay] = 1 THEN 1000
        WHEN [a11].[Loss_of_Pay] =2 THEN 2000 
        ELSE 0    END)  [WJXBFS9]
from	[dbo].[Leavebalance]	[a11]
	join	[dbo].[Login]	[a12]
	  on 	([a11].[Employee_ID] = [a12].[Employee_ID])
	join	[dbo].[Employee]	[a13]
	  on 	([a11].[Employee_ID] = [a13].[Employee_ID])
	join	[dbo].[Swipe]	[a14]
	  on 	([a11].[Employee_ID] = [a14].[Employee_ID])
	join	[dbo].[QuarterProcess]	[a15]
	  on 	([a11].[Employee_ID] = [a15].[Employee_ID])
where	([a13].[Name] in (N'Employee 1', N'Employee 10', N'Employee 11', N'Employee 13', N'Employee 14', N'Employee 15', N'Employee 16', N'Employee 17', N'Employee 18', N'Employee 19')
 and [a14].[First_Check_In] in (N'00:00', N'00:01', N'00:02', N'00:03', N'00:04', N'00:05', N'00:06', N'00:07'))
group by	[a15].[Year],
	[a12].[Active],
	CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END,
	[a15].[Quarter],
	[a13].[Manager_ID],
	[a15].[To_Date],
	CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END,
	[a13].[Name],
	[a12].[Designation],
	[a12].[Department]

select	distinct [pa12].[ManagerId]  [ManagerId],
	[a13].[Manager_Name]  [Manager_Name],
	[pa12].[Department]  [Department],
	[pa12].[Designation]  [Designation],
	[pa12].[Quarter]  [Quarter],
	[pa12].[Active]  [Active],
	[pa12].[Year1]  [Year1],
	[pa12].[CustCol]  [CustCol],
	[pa12].[CustCol_2]  [CustCol_2],
	[pa12].[Name]  [Name],
	[a13].[Gender]  [Gender],
	[a13].[Email]  [Email],
	[pa12].[ToDate]  [ToDate],
	[pa12].[WJXBFS1]  [WJXBFS1],
	[pa12].[WJXBFS2]  [WJXBFS2],
	[pa12].[WJXBFS3]  [WJXBFS3],
	[pa12].[WJXBFS4]  [WJXBFS4],
	[pa12].[WJXBFS5]  [WJXBFS5],
	[pa12].[WJXBFS6]  [WJXBFS6],
	[pa12].[WJXBFS7]  [WJXBFS7],
	[pa12].[WJXBFS8]  [WJXBFS8],
	[pa12].[WJXBFS9]  [WJXBFS9]
from	#ZZMD02	[pa12]
	join	[dbo].[Employee]	[a13]
	  on 	([pa12].[CustCol] = CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END and 
	[pa12].[CustCol_2] = CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END and 
	[pa12].[ManagerId] = [a13].[Manager_ID] and 
	[pa12].[Name] = [a13].[Name])
where	(exists (select	*
	from	[dbo].[Login]	[s21]
		join	[dbo].[Employee]	[s22]
		  on 	([s21].[Employee_ID] = [s22].[Employee_ID])
		join	[dbo].[Swipe]	[s23]
		  on 	([s21].[Employee_ID] = [s23].[Employee_ID])
		join	[dbo].[QuarterProcess]	[s24]
		  on 	([s21].[Employee_ID] = [s24].[Employee_ID])
	where	([s22].[Name] in (N'Employee 1', N'Employee 10', N'Employee 11', N'Employee 13', N'Employee 14', N'Employee 15', N'Employee 16', N'Employee 17', N'Employee 18', N'Employee 19')
	 and [s23].[First_Check_In] in (N'00:00', N'00:01', N'00:02', N'00:03', N'00:04', N'00:05', N'00:06', N'00:07'))
	 and	[s24].[Year] = [pa12].[Year1]
	 and 	[s21].[Active] = [pa12].[Active]
	 and 	CASE 
        WHEN [s22].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [s22].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END = [pa12].[CustCol_2]
	 and 	[s24].[Quarter] = [pa12].[Quarter]
	 and 	[s22].[Manager_ID] = [pa12].[ManagerId]
	 and 	[s24].[To_Date] = [pa12].[ToDate]
	 and 	CASE 
        WHEN [s22].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [s22].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END = [pa12].[CustCol]
	 and 	[s22].[Name] = [pa12].[Name]
	 and 	[s21].[Designation] = [pa12].[Designation]
	 and 	[s21].[Department] = [pa12].[Department]))

create table #ZZMD03(
	[Year1]	BIGINT, 
	[Active]	VARCHAR(1), 
	[CustCol_2]	VARCHAR(50), 
	[Quarter]	INTEGER, 
	[ManagerId]	BIGINT, 
	[ToDate]	DATE, 
	[CustCol]	VARCHAR(15), 
	[Name]	NVARCHAR(50), 
	[Designation]	VARCHAR(50), 
	[Department]	NVARCHAR(50), 
	[WJXBFS1]	FLOAT, 
	[WJXBFS2]	FLOAT, 
	[WJXBFS3]	FLOAT, 
	[WJXBFS4]	FLOAT, 
	[WJXBFS5]	FLOAT, 
	[WJXBFS6]	FLOAT, 
	[WJXBFS7]	FLOAT, 
	[WJXBFS8]	FLOAT, 
	[WJXBFS9]	FLOAT)

insert into #ZZMD03 
select	[a15].[Year]  [Year1],
	[a12].[Active]  [Active],
	CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END  [CustCol_2],
	[a15].[Quarter]  [Quarter],
	[a13].[Manager_ID]  [ManagerId],
	[a15].[To_Date]  [ToDate],
	CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END  [CustCol],
	[a13].[Name]  [Name],
	[a12].[Designation]  [Designation],
	[a12].[Department]  [Department],
	sum([a11].[Availed_PTO])  [WJXBFS1],
	sum([a11].[Availed_Compoff])  [WJXBFS2],
	sum([a11].[Balance_Compoff])  [WJXBFS3],
	sum([a11].[Loss_of_Pay])  [WJXBFS4],
	sum([a11].[Total_WFH])  [WJXBFS5],
	sum([a11].[Total_PTO])  [WJXBFS6],
	sum([a11].[Balance_WFH])  [WJXBFS7],
	sum([a11].[Balance_PTO])  [WJXBFS8],
	sum(CASE 
        WHEN [a11].[Loss_of_Pay] = 1 THEN 1000
        WHEN [a11].[Loss_of_Pay] =2 THEN 2000 
        ELSE 0    END)  [WJXBFS9]
from	[dbo].[Leavebalance]	[a11]
	join	[dbo].[Login]	[a12]
	  on 	([a11].[Employee_ID] = [a12].[Employee_ID])
	join	[dbo].[Employee]	[a13]
	  on 	([a11].[Employee_ID] = [a13].[Employee_ID])
	join	[dbo].[Swipe]	[a14]
	  on 	([a11].[Employee_ID] = [a14].[Employee_ID])
	join	[dbo].[QuarterProcess]	[a15]
	  on 	([a11].[Employee_ID] = [a15].[Employee_ID])
where	([a13].[Name] in (N'Employee 34', N'Employee 35', N'Employee 36', N'Employee 37', N'Employee 38', N'Employee 39', N'Employee 4', N'Employee 40', N'Employee 41', N'Employee 42', N'Employee 43')
 and [a14].[First_Check_In] in (N'00:00', N'00:01', N'00:02', N'00:03', N'00:04', N'00:05', N'00:06', N'00:07'))
group by	[a15].[Year],
	[a12].[Active],
	CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END,
	[a15].[Quarter],
	[a13].[Manager_ID],
	[a15].[To_Date],
	CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END,
	[a13].[Name],
	[a12].[Designation],
	[a12].[Department]

select	distinct [pa12].[ManagerId]  [ManagerId],
	[a13].[Manager_Name]  [Manager_Name],
	[pa12].[Department]  [Department],
	[pa12].[Designation]  [Designation],
	[pa12].[Quarter]  [Quarter],
	[pa12].[Active]  [Active],
	[pa12].[Year1]  [Year1],
	[pa12].[CustCol]  [CustCol],
	[pa12].[CustCol_2]  [CustCol_2],
	[pa12].[Name]  [Name],
	[a13].[Gender]  [Gender],
	[a13].[Email]  [Email],
	[pa12].[ToDate]  [ToDate],
	[pa12].[WJXBFS1]  [WJXBFS1],
	[pa12].[WJXBFS2]  [WJXBFS2],
	[pa12].[WJXBFS3]  [WJXBFS3],
	[pa12].[WJXBFS4]  [WJXBFS4],
	[pa12].[WJXBFS5]  [WJXBFS5],
	[pa12].[WJXBFS6]  [WJXBFS6],
	[pa12].[WJXBFS7]  [WJXBFS7],
	[pa12].[WJXBFS8]  [WJXBFS8],
	[pa12].[WJXBFS9]  [WJXBFS9]
from	#ZZMD03	[pa12]
	join	[dbo].[Employee]	[a13]
	  on 	([pa12].[CustCol] = CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END and 
	[pa12].[CustCol_2] = CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END and 
	[pa12].[ManagerId] = [a13].[Manager_ID] and 
	[pa12].[Name] = [a13].[Name])
where	(exists (select	*
	from	[dbo].[Login]	[s21]
		join	[dbo].[Employee]	[s22]
		  on 	([s21].[Employee_ID] = [s22].[Employee_ID])
		join	[dbo].[Swipe]	[s23]
		  on 	([s21].[Employee_ID] = [s23].[Employee_ID])
		join	[dbo].[QuarterProcess]	[s24]
		  on 	([s21].[Employee_ID] = [s24].[Employee_ID])
	where	([s22].[Name] in (N'Employee 34', N'Employee 35', N'Employee 36', N'Employee 37', N'Employee 38', N'Employee 39', N'Employee 4', N'Employee 40', N'Employee 41', N'Employee 42', N'Employee 43')
	 and [s23].[First_Check_In] in (N'00:00', N'00:01', N'00:02', N'00:03', N'00:04', N'00:05', N'00:06', N'00:07'))
	 and	[s24].[Year] = [pa12].[Year1]
	 and 	[s21].[Active] = [pa12].[Active]
	 and 	CASE 
        WHEN [s22].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [s22].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END = [pa12].[CustCol_2]
	 and 	[s24].[Quarter] = [pa12].[Quarter]
	 and 	[s22].[Manager_ID] = [pa12].[ManagerId]
	 and 	[s24].[To_Date] = [pa12].[ToDate]
	 and 	CASE 
        WHEN [s22].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [s22].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END = [pa12].[CustCol]
	 and 	[s22].[Name] = [pa12].[Name]
	 and 	[s21].[Designation] = [pa12].[Designation]
	 and 	[s21].[Department] = [pa12].[Department]))

create table #ZZMD04(
	[Year1]	BIGINT, 
	[Active]	VARCHAR(1), 
	[CustCol_2]	VARCHAR(50), 
	[Quarter]	INTEGER, 
	[ManagerId]	BIGINT, 
	[ToDate]	DATE, 
	[CustCol]	VARCHAR(15), 
	[Name]	NVARCHAR(50), 
	[Designation]	VARCHAR(50), 
	[Department]	NVARCHAR(50), 
	[WJXBFS1]	FLOAT, 
	[WJXBFS2]	FLOAT, 
	[WJXBFS3]	FLOAT, 
	[WJXBFS4]	FLOAT, 
	[WJXBFS5]	FLOAT, 
	[WJXBFS6]	FLOAT, 
	[WJXBFS7]	FLOAT, 
	[WJXBFS8]	FLOAT, 
	[WJXBFS9]	FLOAT)

insert into #ZZMD04 
select	[a15].[Year]  [Year1],
	[a12].[Active]  [Active],
	CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END  [CustCol_2],
	[a15].[Quarter]  [Quarter],
	[a13].[Manager_ID]  [ManagerId],
	[a15].[To_Date]  [ToDate],
	CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END  [CustCol],
	[a13].[Name]  [Name],
	[a12].[Designation]  [Designation],
	[a12].[Department]  [Department],
	sum([a11].[Availed_PTO])  [WJXBFS1],
	sum([a11].[Availed_Compoff])  [WJXBFS2],
	sum([a11].[Balance_Compoff])  [WJXBFS3],
	sum([a11].[Loss_of_Pay])  [WJXBFS4],
	sum([a11].[Total_WFH])  [WJXBFS5],
	sum([a11].[Total_PTO])  [WJXBFS6],
	sum([a11].[Balance_WFH])  [WJXBFS7],
	sum([a11].[Balance_PTO])  [WJXBFS8],
	sum(CASE 
        WHEN [a11].[Loss_of_Pay] = 1 THEN 1000
        WHEN [a11].[Loss_of_Pay] =2 THEN 2000 
        ELSE 0    END)  [WJXBFS9]
from	[dbo].[Leavebalance]	[a11]
	join	[dbo].[Login]	[a12]
	  on 	([a11].[Employee_ID] = [a12].[Employee_ID])
	join	[dbo].[Employee]	[a13]
	  on 	([a11].[Employee_ID] = [a13].[Employee_ID])
	join	[dbo].[Swipe]	[a14]
	  on 	([a11].[Employee_ID] = [a14].[Employee_ID])
	join	[dbo].[QuarterProcess]	[a15]
	  on 	([a11].[Employee_ID] = [a15].[Employee_ID])
where	([a13].[Name] in (N'Employee 1', N'Employee 10', N'Employee 11', N'Employee 13', N'Employee 14', N'Employee 15', N'Employee 16', N'Employee 17', N'Employee 18', N'Employee 19')
 and [a14].[Last_Check_Out] in (N'21:04', N'21:05', N'21:06', N'21:07', N'21:08', N'21:09', N'21:10', N'21:11', N'21:12', N'21:13', N'21:14', N'21:15', N'21:16', N'21:17', N'21:18'))
group by	[a15].[Year],
	[a12].[Active],
	CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END,
	[a15].[Quarter],
	[a13].[Manager_ID],
	[a15].[To_Date],
	CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END,
	[a13].[Name],
	[a12].[Designation],
	[a12].[Department]

select	distinct [pa12].[ManagerId]  [ManagerId],
	[a13].[Manager_Name]  [Manager_Name],
	[pa12].[Department]  [Department],
	[pa12].[Designation]  [Designation],
	[pa12].[Quarter]  [Quarter],
	[pa12].[Active]  [Active],
	[pa12].[Year1]  [Year1],
	[pa12].[CustCol]  [CustCol],
	[pa12].[CustCol_2]  [CustCol_2],
	[pa12].[Name]  [Name],
	[a13].[Gender]  [Gender],
	[a13].[Email]  [Email],
	[pa12].[ToDate]  [ToDate],
	[pa12].[WJXBFS1]  [WJXBFS1],
	[pa12].[WJXBFS2]  [WJXBFS2],
	[pa12].[WJXBFS3]  [WJXBFS3],
	[pa12].[WJXBFS4]  [WJXBFS4],
	[pa12].[WJXBFS5]  [WJXBFS5],
	[pa12].[WJXBFS6]  [WJXBFS6],
	[pa12].[WJXBFS7]  [WJXBFS7],
	[pa12].[WJXBFS8]  [WJXBFS8],
	[pa12].[WJXBFS9]  [WJXBFS9]
from	#ZZMD04	[pa12]
	join	[dbo].[Employee]	[a13]
	  on 	([pa12].[CustCol] = CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END and 
	[pa12].[CustCol_2] = CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END and 
	[pa12].[ManagerId] = [a13].[Manager_ID] and 
	[pa12].[Name] = [a13].[Name])
where	(exists (select	*
	from	[dbo].[Login]	[s21]
		join	[dbo].[Employee]	[s22]
		  on 	([s21].[Employee_ID] = [s22].[Employee_ID])
		join	[dbo].[Swipe]	[s23]
		  on 	([s21].[Employee_ID] = [s23].[Employee_ID])
		join	[dbo].[QuarterProcess]	[s24]
		  on 	([s21].[Employee_ID] = [s24].[Employee_ID])
	where	([s22].[Name] in (N'Employee 1', N'Employee 10', N'Employee 11', N'Employee 13', N'Employee 14', N'Employee 15', N'Employee 16', N'Employee 17', N'Employee 18', N'Employee 19')
	 and [s23].[Last_Check_Out] in (N'21:04', N'21:05', N'21:06', N'21:07', N'21:08', N'21:09', N'21:10', N'21:11', N'21:12', N'21:13', N'21:14', N'21:15', N'21:16', N'21:17', N'21:18'))
	 and	[s24].[Year] = [pa12].[Year1]
	 and 	[s21].[Active] = [pa12].[Active]
	 and 	CASE 
        WHEN [s22].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [s22].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END = [pa12].[CustCol_2]
	 and 	[s24].[Quarter] = [pa12].[Quarter]
	 and 	[s22].[Manager_ID] = [pa12].[ManagerId]
	 and 	[s24].[To_Date] = [pa12].[ToDate]
	 and 	CASE 
        WHEN [s22].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [s22].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END = [pa12].[CustCol]
	 and 	[s22].[Name] = [pa12].[Name]
	 and 	[s21].[Designation] = [pa12].[Designation]
	 and 	[s21].[Department] = [pa12].[Department]))

create table #ZZMD05(
	[Year1]	BIGINT, 
	[Active]	VARCHAR(1), 
	[CustCol_2]	VARCHAR(50), 
	[Quarter]	INTEGER, 
	[ManagerId]	BIGINT, 
	[ToDate]	DATE, 
	[CustCol]	VARCHAR(15), 
	[Name]	NVARCHAR(50), 
	[Designation]	VARCHAR(50), 
	[Department]	NVARCHAR(50), 
	[WJXBFS1]	FLOAT, 
	[WJXBFS2]	FLOAT, 
	[WJXBFS3]	FLOAT, 
	[WJXBFS4]	FLOAT, 
	[WJXBFS5]	FLOAT, 
	[WJXBFS6]	FLOAT, 
	[WJXBFS7]	FLOAT, 
	[WJXBFS8]	FLOAT, 
	[WJXBFS9]	FLOAT)

insert into #ZZMD05 
select	[a15].[Year]  [Year1],
	[a12].[Active]  [Active],
	CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END  [CustCol_2],
	[a15].[Quarter]  [Quarter],
	[a13].[Manager_ID]  [ManagerId],
	[a15].[To_Date]  [ToDate],
	CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END  [CustCol],
	[a13].[Name]  [Name],
	[a12].[Designation]  [Designation],
	[a12].[Department]  [Department],
	sum([a11].[Availed_PTO])  [WJXBFS1],
	sum([a11].[Availed_Compoff])  [WJXBFS2],
	sum([a11].[Balance_Compoff])  [WJXBFS3],
	sum([a11].[Loss_of_Pay])  [WJXBFS4],
	sum([a11].[Total_WFH])  [WJXBFS5],
	sum([a11].[Total_PTO])  [WJXBFS6],
	sum([a11].[Balance_WFH])  [WJXBFS7],
	sum([a11].[Balance_PTO])  [WJXBFS8],
	sum(CASE 
        WHEN [a11].[Loss_of_Pay] = 1 THEN 1000
        WHEN [a11].[Loss_of_Pay] =2 THEN 2000 
        ELSE 0    END)  [WJXBFS9]
from	[dbo].[Leavebalance]	[a11]
	join	[dbo].[Login]	[a12]
	  on 	([a11].[Employee_ID] = [a12].[Employee_ID])
	join	[dbo].[Employee]	[a13]
	  on 	([a11].[Employee_ID] = [a13].[Employee_ID])
	join	[dbo].[Swipe]	[a14]
	  on 	([a11].[Employee_ID] = [a14].[Employee_ID])
	join	[dbo].[QuarterProcess]	[a15]
	  on 	([a11].[Employee_ID] = [a15].[Employee_ID])
where	([a13].[Name] in (N'Employee 34', N'Employee 35', N'Employee 36', N'Employee 37', N'Employee 38', N'Employee 39', N'Employee 4', N'Employee 40', N'Employee 41', N'Employee 42', N'Employee 43')
 and [a14].[Last_Check_Out] in (N'21:04', N'21:05', N'21:06', N'21:07', N'21:08', N'21:09', N'21:10', N'21:11', N'21:12', N'21:13', N'21:14', N'21:15', N'21:16', N'21:17', N'21:18'))
group by	[a15].[Year],
	[a12].[Active],
	CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END,
	[a15].[Quarter],
	[a13].[Manager_ID],
	[a15].[To_Date],
	CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END,
	[a13].[Name],
	[a12].[Designation],
	[a12].[Department]

select	distinct [pa12].[ManagerId]  [ManagerId],
	[a13].[Manager_Name]  [Manager_Name],
	[pa12].[Department]  [Department],
	[pa12].[Designation]  [Designation],
	[pa12].[Quarter]  [Quarter],
	[pa12].[Active]  [Active],
	[pa12].[Year1]  [Year1],
	[pa12].[CustCol]  [CustCol],
	[pa12].[CustCol_2]  [CustCol_2],
	[pa12].[Name]  [Name],
	[a13].[Gender]  [Gender],
	[a13].[Email]  [Email],
	[pa12].[ToDate]  [ToDate],
	[pa12].[WJXBFS1]  [WJXBFS1],
	[pa12].[WJXBFS2]  [WJXBFS2],
	[pa12].[WJXBFS3]  [WJXBFS3],
	[pa12].[WJXBFS4]  [WJXBFS4],
	[pa12].[WJXBFS5]  [WJXBFS5],
	[pa12].[WJXBFS6]  [WJXBFS6],
	[pa12].[WJXBFS7]  [WJXBFS7],
	[pa12].[WJXBFS8]  [WJXBFS8],
	[pa12].[WJXBFS9]  [WJXBFS9]
from	#ZZMD05	[pa12]
	join	[dbo].[Employee]	[a13]
	  on 	([pa12].[CustCol] = CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END and 
	[pa12].[CustCol_2] = CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END and 
	[pa12].[ManagerId] = [a13].[Manager_ID] and 
	[pa12].[Name] = [a13].[Name])
where	(exists (select	*
	from	[dbo].[Login]	[s21]
		join	[dbo].[Employee]	[s22]
		  on 	([s21].[Employee_ID] = [s22].[Employee_ID])
		join	[dbo].[Swipe]	[s23]
		  on 	([s21].[Employee_ID] = [s23].[Employee_ID])
		join	[dbo].[QuarterProcess]	[s24]
		  on 	([s21].[Employee_ID] = [s24].[Employee_ID])
	where	([s22].[Name] in (N'Employee 34', N'Employee 35', N'Employee 36', N'Employee 37', N'Employee 38', N'Employee 39', N'Employee 4', N'Employee 40', N'Employee 41', N'Employee 42', N'Employee 43')
	 and [s23].[Last_Check_Out] in (N'21:04', N'21:05', N'21:06', N'21:07', N'21:08', N'21:09', N'21:10', N'21:11', N'21:12', N'21:13', N'21:14', N'21:15', N'21:16', N'21:17', N'21:18'))
	 and	[s24].[Year] = [pa12].[Year1]
	 and 	[s21].[Active] = [pa12].[Active]
	 and 	CASE 
        WHEN [s22].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [s22].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END = [pa12].[CustCol_2]
	 and 	[s24].[Quarter] = [pa12].[Quarter]
	 and 	[s22].[Manager_ID] = [pa12].[ManagerId]
	 and 	[s24].[To_Date] = [pa12].[ToDate]
	 and 	CASE 
        WHEN [s22].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [s22].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END = [pa12].[CustCol]
	 and 	[s22].[Name] = [pa12].[Name]
	 and 	[s21].[Designation] = [pa12].[Designation]
	 and 	[s21].[Department] = [pa12].[Department]))

drop table #ZZMD00

drop table #ZZMD01

drop table #ZZMD02

drop table #ZZMD03

drop table #ZZMD04

drop table #ZZMD05

[Analytical engine calculation steps:
	1.  Perform cross-tabbing
]


--- SQL Query 3 ---
select	[a13].[Manager_ID]  [ManagerId],
	max([a13].[Manager_Name])  [Manager_Name],
	[a12].[Department]  [Department],
	[a12].[Designation]  [Designation],
	[a13].[Name]  [Name],
	max([a13].[Gender])  [Gender],
	max([a13].[Email])  [Email],
	[a14].[From_Date]  [From_Date1],
	[a14].[Quarter]  [Quarter],
	[a12].[Active]  [Active],
	[a14].[Year]  [Year1],
	CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END  [CustCol],
	CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END  [CustCol_2],
	sum([a11].[Availed_PTO])  [WJXBFS1],
	sum([a11].[Availed_Compoff])  [WJXBFS2],
	sum([a11].[Balance_Compoff])  [WJXBFS3],
	sum([a11].[Loss_of_Pay])  [WJXBFS4],
	sum([a11].[Total_WFH])  [WJXBFS5],
	sum([a11].[Total_PTO])  [WJXBFS6],
	sum([a11].[Balance_WFH])  [WJXBFS7],
	sum([a11].[Balance_PTO])  [WJXBFS8],
	sum(CASE 
        WHEN [a11].[Loss_of_Pay] = 1 THEN 1000
        WHEN [a11].[Loss_of_Pay] =2 THEN 2000 
        ELSE 0    END)  [WJXBFS9]
from	[dbo].[Leavebalance]	[a11]
	join	[dbo].[Login]	[a12]
	  on 	([a11].[Employee_ID] = [a12].[Employee_ID])
	join	[dbo].[Employee]	[a13]
	  on 	([a11].[Employee_ID] = [a13].[Employee_ID])
	join	[dbo].[QuarterProcess]	[a14]
	  on 	([a11].[Employee_ID] = [a14].[Employee_ID])
group by	[a13].[Manager_ID],
	[a12].[Department],
	[a12].[Designation],
	[a13].[Name],
	[a14].[From_Date],
	[a14].[Quarter],
	[a12].[Active],
	[a14].[Year],
	CASE 
        WHEN [a13].[Employee_ID] BETWEEN 16001 AND 16089 THEN 'Team A'
        WHEN [a13].[Employee_ID] BETWEEN 16090 AND 16120 THEN 'Team B'
        ELSE 'Team C'
    END,
	CASE 
        WHEN [a13].[Manager_Name]  = 'Shashi Kumar' THEN  'BI Engineers'        
   WHEN [a13].[Manager_Name]  ='RamaKrishna'  THEN  'ETL Developers'          
 ELSE  'HR Team'       END

[Analytical engine calculation steps:
	1.  Perform cross-tabbing
]


--- SQL Query 4 ---
select	[a13].[Manager_ID]  [Manager_ID],
	[a13].[Manager_Name]  [Manager_Name],
	[a12].[Department]  [Department],
	[a12].[Designation]  [Designation],
	[a13].[Gender]  [Gender],
	[a13].[Email]  [Email],
	[a14].[From_Date]  [From_Date1],
	[a14].[Quarter]  [Quarter],
	[a12].[Active]  [Active],
	[a14].[Year]  [Year],
                sum([a11].[Availed_PTO]  ),
	sum([a11].[Availed_Compoff])  ,
	sum([a11].[Balance_Compoff])  ,
	sum([a11].[Loss_of_Pay]  ),
	sum([a11].  [Total_WFH]),
	sum([a11].  [Total_PTO]),
	sum([a11].  [Balance_WFH]),
	sum([a11].  [Balance_PTO])
from	[dbo].[Leavebalance]	[a11]
	join	[dbo].[Login]	[a12]
	  on 	([a11].[Employee_ID] = [a12].[Employee_ID])
	join	[dbo].[Employee]	[a13]
	  on 	([a11].[Employee_ID] = [a13].[Employee_ID])
	join	[dbo].[QuarterProcess]	[a14]
	  on 	([a11].[Employee_ID] = [a14].[Employee_ID] and 
	[a14].[Year] = [a14].[Year])
where	[a13].[Manager_ID] in (16008)
group by 
[a13].  [Manager_ID],
	[a13].  [Manager_Name],
	[a12].  [Department],
	[a12].  [Designation],
	[a13].  [Gender],
	[a13].  [Email],
	[a14].  [From_Date],
	[a14].  [Quarter],
	[a12].  [Active],
	[a14]. [Year]



[Analytical engine calculation steps:
	1.  Perform cross-tabbing
]


--- SQL Query 5 ---
create table #ZZOP00(
	[Department]	NVARCHAR(50), 
	[Designation]	VARCHAR(50), 
	[Name]	NVARCHAR(50), 
	[Gender]	VARCHAR(25), 
	[Email]	VARCHAR(100), 
	[Active]	VARCHAR(1), 
	[WJXBFS1]	FLOAT, 
	[WJXBFS2]	FLOAT, 
	[WJXBFS3]	FLOAT, 
	[WJXBFS4]	FLOAT, 
	[WJXBFS5]	FLOAT, 
	[WJXBFS6]	FLOAT, 
	[GODWFLAG1_1]	INTEGER, 
	[WJXBFS7]	FLOAT, 
	[WJXBFS8]	FLOAT, 
	[WJXBFS9]	FLOAT, 
	[WJXBFSa]	FLOAT, 
	[WJXBFSb]	FLOAT, 
	[WJXBFSc]	FLOAT, 
	[GODWFLAG2_1]	INTEGER)

insert into #ZZOP00 
select	[a12].[Department]  [Department],
	[a12].[Designation]  [Designation],
	[a13].[Name]  [Name],
	max([a13].[Gender])  [Gender],
	max([a13].[Email])  [Email],
	[a12].[Active]  [Active],
	sum((Case when [a13].[Name] in (N'Employee 1', N'Employee 10', N'Employee 11', N'Employee 13', N'Employee 14', N'Employee 15', N'Employee 16', N'Employee 17', N'Employee 18', N'Employee 19') then [a11].[Availed_PTO] else NULL end))  [WJXBFS1],
	sum((Case when [a13].[Name] in (N'Employee 1', N'Employee 10', N'Employee 11', N'Employee 13', N'Employee 14', N'Employee 15', N'Employee 16', N'Employee 17', N'Employee 18', N'Employee 19') then [a11].[Availed_Compoff] else NULL end))  [WJXBFS2],
	sum((Case when [a13].[Name] in (N'Employee 1', N'Employee 10', N'Employee 11', N'Employee 13', N'Employee 14', N'Employee 15', N'Employee 16', N'Employee 17', N'Employee 18', N'Employee 19') then [a11].[Balance_Compoff] else NULL end))  [WJXBFS3],
	sum((Case when [a13].[Name] in (N'Employee 1', N'Employee 10', N'Employee 11', N'Employee 13', N'Employee 14', N'Employee 15', N'Employee 16', N'Employee 17', N'Employee 18', N'Employee 19') then [a11].[Loss_of_Pay] else NULL end))  [WJXBFS4],
	sum((Case when [a13].[Name] in (N'Employee 1', N'Employee 10', N'Employee 11', N'Employee 13', N'Employee 14', N'Employee 15', N'Employee 16', N'Employee 17', N'Employee 18', N'Employee 19') then [a11].[Total_PTO] else NULL end))  [WJXBFS5],
	sum((Case when [a13].[Name] in (N'Employee 1', N'Employee 10', N'Employee 11', N'Employee 13', N'Employee 14', N'Employee 15', N'Employee 16', N'Employee 17', N'Employee 18', N'Employee 19') then [a11].[Total_WFH] else NULL end))  [WJXBFS6],
	max((Case when [a13].[Name] in (N'Employee 1', N'Employee 10', N'Employee 11', N'Employee 13', N'Employee 14', N'Employee 15', N'Employee 16', N'Employee 17', N'Employee 18', N'Employee 19') then 1 else 0 end))  [GODWFLAG1_1],
	sum((Case when [a13].[Name] in (N'Employee 34', N'Employee 35', N'Employee 36', N'Employee 37', N'Employee 38', N'Employee 39', N'Employee 4', N'Employee 40', N'Employee 41', N'Employee 42', N'Employee 43') then [a11].[Availed_PTO] else NULL end))  [WJXBFS7],
	sum((Case when [a13].[Name] in (N'Employee 34', N'Employee 35', N'Employee 36', N'Employee 37', N'Employee 38', N'Employee 39', N'Employee 4', N'Employee 40', N'Employee 41', N'Employee 42', N'Employee 43') then [a11].[Availed_Compoff] else NULL end))  [WJXBFS8],
	sum((Case when [a13].[Name] in (N'Employee 34', N'Employee 35', N'Employee 36', N'Employee 37', N'Employee 38', N'Employee 39', N'Employee 4', N'Employee 40', N'Employee 41', N'Employee 42', N'Employee 43') then [a11].[Balance_Compoff] else NULL end))  [WJXBFS9],
	sum((Case when [a13].[Name] in (N'Employee 34', N'Employee 35', N'Employee 36', N'Employee 37', N'Employee 38', N'Employee 39', N'Employee 4', N'Employee 40', N'Employee 41', N'Employee 42', N'Employee 43') then [a11].[Loss_of_Pay] else NULL end))  [WJXBFSa],
	sum((Case when [a13].[Name] in (N'Employee 34', N'Employee 35', N'Employee 36', N'Employee 37', N'Employee 38', N'Employee 39', N'Employee 4', N'Employee 40', N'Employee 41', N'Employee 42', N'Employee 43') then [a11].[Total_PTO] else NULL end))  [WJXBFSb],
	sum((Case when [a13].[Name] in (N'Employee 34', N'Employee 35', N'Employee 36', N'Employee 37', N'Employee 38', N'Employee 39', N'Employee 4', N'Employee 40', N'Employee 41', N'Employee 42', N'Employee 43') then [a11].[Total_WFH] else NULL end))  [WJXBFSc],
	max((Case when [a13].[Name] in (N'Employee 34', N'Employee 35', N'Employee 36', N'Employee 37', N'Employee 38', N'Employee 39', N'Employee 4', N'Employee 40', N'Employee 41', N'Employee 42', N'Employee 43') then 1 else 0 end))  [GODWFLAG2_1]
from	[dbo].[Leavebalance]	[a11]
	join	[dbo].[Login]	[a12]
	  on 	([a11].[Employee_ID] = [a12].[Employee_ID])
	join	[dbo].[Employee]	[a13]
	  on 	([a11].[Employee_ID] = [a13].[Employee_ID])
where	([a13].[Manager_ID] in (16008)
 and ([a13].[Name] in (N'Employee 1', N'Employee 10', N'Employee 11', N'Employee 13', N'Employee 14', N'Employee 15', N'Employee 16', N'Employee 17', N'Employee 18', N'Employee 19')
 or [a13].[Name] in (N'Employee 34', N'Employee 35', N'Employee 36', N'Employee 37', N'Employee 38', N'Employee 39', N'Employee 4', N'Employee 40', N'Employee 41', N'Employee 42', N'Employee 43')))
group by	[a12].[Department],
	[a12].[Designation],
	[a13].[Name],
	[a12].[Active]

select	[pa01].[Department]  [Department],
	[pa01].[Designation]  [Designation],
	[pa01].[Name]  [Name],
	[pa01].[Gender]  [Gender],
	[pa01].[Email]  [Email],
	[pa01].[Active]  [Active],
	[pa01].[WJXBFS1]  [WJXBFS1],
	[pa01].[WJXBFS2]  [WJXBFS2],
	[pa01].[WJXBFS3]  [WJXBFS3],
	[pa01].[WJXBFS4]  [WJXBFS4],
	[pa01].[WJXBFS5]  [WJXBFS5],
	[pa01].[WJXBFS6]  [WJXBFS6]
from	#ZZOP00	[pa01]
where	[pa01].[GODWFLAG1_1] = 1

select	[pa01].[Department]  [Department],
	[pa01].[Designation]  [Designation],
	[pa01].[Name]  [Name],
	[pa01].[Gender]  [Gender],
	[pa01].[Email]  [Email],
	[pa01].[Active]  [Active],
	[pa01].[WJXBFS7]  [WJXBFS1],
	[pa01].[WJXBFS8]  [WJXBFS2],
	[pa01].[WJXBFS9]  [WJXBFS3],
	[pa01].[WJXBFSa]  [WJXBFS4],
	[pa01].[WJXBFSb]  [WJXBFS5],
	[pa01].[WJXBFSc]  [WJXBFS6]
from	#ZZOP00	[pa01]
where	[pa01].[GODWFLAG2_1] = 1

drop table #ZZOP00

[Analytical engine calculation steps:
	1.  Perform cross-tabbing
]


--- SQL Query 6 ---
select	[a13].[Manager_ID]  [Manager_ID],
	[a13].[Manager_Name]  [Manager_Name],
	[a12].[Department]  [Department],
	[a12].[Designation]  [Designation],
	[a13].[Gender]  [Gender],
	[a13].[Email]  [Email],
	[a14].[From_Date]  [From_Date1],
	[a14].[Quarter]  [Quarter],
	[a12].[Active]  [Active],
	[a14].[Year]  [Year],
                sum([a11].[Availed_PTO]  ),
	sum([a11].[Availed_Compoff])  ,
	sum([a11].[Balance_Compoff])  ,
	sum([a11].[Loss_of_Pay]  ),
	sum([a11].  [Total_WFH]),
	sum([a11].  [Total_PTO]),
	sum([a11].  [Balance_WFH]),
	sum([a11].  [Balance_PTO])
from	[dbo].[Leavebalance]	[a11]
	join	[dbo].[Login]	[a12]
	  on 	([a11].[Employee_ID] = [a12].[Employee_ID])
	join	[dbo].[Employee]	[a13]
	  on 	([a11].[Employee_ID] = [a13].[Employee_ID])
	join	[dbo].[QuarterProcess]	[a14]
	  on 	([a11].[Employee_ID] = [a14].[Employee_ID] and 
	[a14].[Year] = [a14].[Year])
where	[a13].[Manager_ID] in (16130)
group by 
[a13].  [Manager_ID],
	[a13].  [Manager_Name],
	[a12].  [Department],
	[a12].  [Designation],
	[a13].  [Gender],
	[a13].  [Email],
	[a14].  [From_Date],
	[a14].  [Quarter],
	[a12].  [Active],
	[a14]. [Year]


[Analytical engine calculation steps:
	1.  Perform cross-tabbing
]


--- SQL Query 7 ---
select	[a12].[Name]  [Name],
	max([a12].[Gender])  [Gender],
	max([a12].[Email])  [Email],
	[a13].[Year]  [Year1],
	[a13].[To_Date]  [ToDate],
	[a13].[From_Date]  [From_Date1],
	sum([a11].[Availed_PTO])  [WJXBFS1],
	sum([a11].[Loss_of_Pay])  [WJXBFS2],
	sum([a11].[Balance_PTO])  [WJXBFS3],
	sum([a11].[Balance_WFH])  [WJXBFS4],
	sum([a11].[Total_WFH])  [WJXBFS5]
from	[dbo].[Leavebalance]	[a11]
	join	[dbo].[Employee]	[a12]
	  on 	([a11].[Employee_ID] = [a12].[Employee_ID])
	join	[dbo].[QuarterProcess]	[a13]
	  on 	([a11].[Employee_ID] = [a13].[Employee_ID])
where	[a12].[Manager_ID] in (16130)
group by	[a12].[Name],
	[a13].[Year],
	[a13].[To_Date],
	[a13].[From_Date]

[Analytical engine calculation steps:
	1.  Perform cross-tabbing
]


--- SQL Query 8 ---
select	[a13]. [Manager_ID] [Manager_ID],
	[a13].[Manager_Name]  [Manager_Name],
	[a12].[Department]  [Department],
	[a12].[Designation]  [Designation],
		[a13].[Gender]  [Gender],
	[a13].[Email]  [Email],
	[a14].[From_Date]  [From_Date1],
	[a14].[Quarter]  [Quarter],
	[a12].[Active]  [Active],
	[a14].[Year]  [Year],
	sum([a11].[Availed_PTO]  ),
	sum([a11].[Availed_Compoff])  ,
	sum([a11].[Balance_Compoff])  ,
	sum([a11].[Loss_of_Pay]  ),
	sum([a11].  [Total_WFH]),
	sum([a11].  [Total_PTO]),
	sum([a11].  [Balance_WFH]),
	sum([a11].  [Balance_PTO])
from	[dbo].[Leavebalance]	[a11]
	join	[dbo].[Login]	[a12]
	  on 	([a11].[Employee_ID] = [a12].[Employee_ID])
	join	[dbo].[Employee]	[a13]
	  on 	([a11].[Employee_ID] = [a13].[Employee_ID])
	join	[dbo].[QuarterProcess]	[a14]
	  on 	([a11].[Employee_ID] = [a14].[Employee_ID] and 
	[a14].[Year] = [a14].[Year])
group by 
[a13].  [Manager_ID],
	[a13].  [Manager_Name],
	[a12].  [Department],
	[a12].  [Designation],
	[a13].  [Gender],
	[a13].  [Email],
	[a14].  [From_Date],
	[a14].  [Quarter],
	[a12].  [Active],
	[a14]. [Year]



[Analytical engine calculation steps:
	1.  Perform cross-tabbing
]


