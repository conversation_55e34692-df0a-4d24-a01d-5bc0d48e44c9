import os
import json
import requests
import decimal
import datetime
import pyodbc
from dotenv import load_dotenv
 
# MicroStrategy Imports
from mstrio.connection import Connection
from mstrio.project_objects import Report, list_reports
 
# OpenAI Imports
import openai
 
# For Power BI authentication (Azure AD)
import msal
 
load_dotenv()
 
#########################
# 1. MICROSTRATEGY SETUP
#########################
 
# MicroStrategy credentials and connection details
MSTR_URL = os.getenv('URL')  # e.g. "https://your-mstr-server.com/MicroStrategyLibrary/api"
MSTR_USER = os.getenv('USER')
MSTR_PASSWORD = os.getenv('PASSWORD')
MSTR_PROJECT_ID = "95550C99497DAAC3AC19DD86D91C4BB3" # e.g. "A428F8524EAD4DF409AAD49981473140"
 
# Connect to MicroStrategy project
mstr_conn = Connection(
    base_url=MSTR_URL,
    username=MSTR_USER,
    password=MSTR_PASSWORD,
    project_id=MSTR_PROJECT_ID,
    login_mode=1
)
mstr_conn.connect()
print("Connected to MicroStrategy Project successfully!")
 
# List available reports
print("\nList of Reports in the Project:")
reports = list_reports(connection=mstr_conn)
for r in reports:
    print(f"- {r.name} (ID: {r.id}) (Type: {r.type})")
 
# Ask user to select a report ID
report_id = input("\nEnter the Report ID to fetch its SQL view: ")
 
# Load the selected report and fetch its SQL view
report_obj = Report(connection=mstr_conn, id=report_id)
mstr_sql = report_obj.sql
print("\nFetched Report SQL:")
print(mstr_sql)
 
###############################################
# 2. EXTRACT SCHEMA INFORMATION VIA OPENAI API
###############################################
 
openai.api_key = os.getenv("OPENAI_API_KEY")
 
def extract_schema_from_sql(sql_text: str) -> dict:
    prompt = f"""
You are an expert data engineer. Given the following SQL query from MicroStrategy, extract and output a JSON with the following structure:
{{
  "tables": [
      {{
         "name": "<table_name>",
         "columns": [
              {{"name": "<column_name>", "dataType": "<PowerBI-compatible data type>"}}
         ]
      }}
  ],
  "relationships": [
      {{
         "name": "<relationship_name>",
         "fromTable": "<table_name>",
         "fromColumn": "<column_name>",
         "toTable": "<table_name>",
         "toColumn": "<column_name>",
         "crossFilteringBehavior": "<OneDirection | BothDirections | Automatic>"
      }}
  ]
}}
only give unique relationship, if no relationships exist, output an empty array for "relationships". Use data types such as "Int64", "string", "double", "DateTime", or "bool".
Here is the SQL:
\"\"\"{sql_text}\"\"\"
Output only valid JSON with no additional text.
"""
    try:
        response = openai.chat.completions.create(
            model="o3-mini",
            messages=[
                {"role": "system", "content": "You are a helpful assistant that extracts schema information from SQL queries."},
                {"role": "user", "content": prompt}
            ],
            # temperature=0
        )
        schema_str = response.choices[0].message.content.strip()
        # Remove markdown code fences if present
        if schema_str.startswith("```"):
            lines = schema_str.splitlines()
            if lines[0].startswith("```"):
                lines = lines[1:]
            if lines and lines[-1].startswith("```"):
                lines = lines[:-1]
            schema_str = "\n".join(lines).strip()
        schema_json = json.loads(schema_str)
        return schema_json
    except Exception as e:
        print("Error extracting schema from SQL via OpenAI:", e)
        return {}
 
# Extract schema from MicroStrategy SQL
mstr_schema = extract_schema_from_sql(mstr_sql)
print("\nExtracted Schema Info from MSTR SQL:")
print(json.dumps(mstr_schema, indent=2))
 
#########################################
# 3. GET ACTUAL SQL SERVER SCHEMA INFO
#########################################
 
def get_sql_column_schema(connection, table_name):
    """
    Given a pyodbc connection and a table name, returns a list of (column_name, data_type)
    using SQL Server's INFORMATION_SCHEMA.
    """
    query = f"""
        SELECT COLUMN_NAME, DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = '{table_name}'
        ORDER BY ORDINAL_POSITION
    """
    cursor = connection.cursor()
    cursor.execute(query)
    return cursor.fetchall()
 
def map_sql_type_to_powerbi_type(sql_type):
    """
    Map common SQL Server data types to Power BI data types.
    """
    sql_type_lower = sql_type.lower()
    if "int" in sql_type_lower:
        return "Int64"
    elif "char" in sql_type_lower or "text" in sql_type_lower or "varchar" in sql_type_lower:
        return "string"
    elif "decimal" in sql_type_lower or "numeric" in sql_type_lower or "float" in sql_type_lower or "real" in sql_type_lower:
        return "double"
    elif "date" in sql_type_lower or "time" in sql_type_lower:
        return "DateTime"
    elif "bit" in sql_type_lower or "bool" in sql_type_lower:
        return "bool"
    else:
        return "string"
 
def merge_schema_with_sqlserver(mstr_schema: dict, sql_conn) -> dict:
    """
    For each table extracted from MicroStrategy SQL (mstr_schema), fetch the actual column schema
    from SQL Server. Build a new schema definition that uses the SQL Server metadata for the tables.
    """
    merged_tables = []
    for table in mstr_schema.get("tables", []):
        table_name = table.get("name")
        # First, try to fetch using the table name as-is.
        columns_schema = get_sql_column_schema(sql_conn, table_name)
        # If not found, try with "dbo." prefix removed (since INFORMATION_SCHEMA.TABLES stores name without schema)
        if not columns_schema:
            if "." in table_name:
                table_name_no_schema = table_name.split(".")[-1]
                columns_schema = get_sql_column_schema(sql_conn, table_name_no_schema)
            else:
                print(f"Warning: No columns found for table '{table_name}' in SQL Server.")
                continue
        # Build columns definition from SQL Server metadata
        columns_def = []
        for col_name, sql_type in columns_schema:
            pbi_type = map_sql_type_to_powerbi_type(sql_type)
            columns_def.append({"name": col_name, "dataType": pbi_type})
        merged_tables.append({"name": table_name, "columns": columns_def})
    # We keep relationships if needed; however, note that push datasets do not support relationships.
    # For this example, we'll omit relationships.
    new_schema = {"tables": merged_tables}
    return new_schema
 
# Connect to SQL Server using pyodbc
db_server = os.getenv("SERVER")
db_name = os.getenv("DATABASE")
db_user = os.getenv("DB_USER") or os.getenv("USERNAME")
db_pass = os.getenv("PASS")
conn_str = f"Driver={{ODBC Driver 17 for SQL Server}};Server={db_server};Database={db_name};UID={db_user};PWD=*********;"
try:
    sql_conn = pyodbc.connect(conn_str)
except Exception as e:
    print("Error connecting to SQL Server:", e)
    exit()
 
# Merge the schema from MSTR with actual SQL Server metadata
merged_schema = merge_schema_with_sqlserver(mstr_schema, sql_conn)
print("\nMerged Schema (from SQL Server):")
print(json.dumps(merged_schema, indent=2))
 
#########################################
# 4. CREATE POWER BI DATASET (Push Mode)
#########################################
 
# Power BI Credentials from .env
TENANT_ID = os.getenv("TENANT_ID")
CLIENT_ID = os.getenv("CLIENT_ID")
CLIENT_SECRET = os.getenv("CLIENT_SECRET")
WORKSPACE_ID = os.getenv("PPU_WORKSPACE_ID")  # Power BI workspace (group) ID
 
# Use MSAL for authentication
power_bi_scope = ["https://analysis.windows.net/powerbi/api/.default"]
app = msal.ConfidentialClientApplication(
    client_id=CLIENT_ID,
    authority=f"https://login.microsoftonline.com/{TENANT_ID}",
    client_credential=CLIENT_SECRET
)

# Acquire token
token_response = app.acquire_token_for_client(scopes=power_bi_scope)
if "access_token" not in token_response:
    print("Failed to authenticate:", token_response.get("error"), token_response.get("error_description"))
    exit()

access_token = token_response["access_token"]
 
headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {access_token}"
}
 
def build_dataset_definition(dataset_name: str, schema: dict, gateway_id: str = None) -> dict:
    """
    Build the dataset definition for a push dataset using the merged schema.
    Includes datasources configuration for refresh capabilities.
    """
    # Get SQL Server connection details from environment
    server = os.getenv("SERVER")
    database = os.getenv("DATABASE")
    
    # Create datasource configuration for SQL Server
    datasources = [{
        "datasourceType": "Sql",
        "connectionDetails": {
            "server": server,
            "database": database
        }
    }]
    
    # If gateway ID is provided, associate it with the datasource
    if gateway_id:
        datasources[0]["gatewayId"] = gateway_id
    
    dataset_def = {
        "name": dataset_name,
        "defaultMode": "Push",
        "tables": schema.get("tables", []),
        "datasources": datasources
    }
    return dataset_def
 
dataset_name = input("\nEnter desired Power BI dataset name: ").strip()

#########################
# HELPER FUNCTIONS
#########################

def list_power_bi_gateways(headers):
    """
    List all available Power BI gateways that the authenticated user has access to.
    """
    try:
        gateways_url = "https://api.powerbi.com/v1.0/myorg/gateways"
        resp = requests.get(gateways_url, headers=headers)
        if resp.status_code == 200:
            gateways = resp.json().get("value", [])
            if gateways:
                print("\nAvailable Power BI Gateways:")
                for gw in gateways:
                    print(f"- {gw.get('name')} (ID: {gw.get('id')})")
                return True
            else:
                print("\nNo Power BI Gateways found.")
                return False
        else:
            print(f"\nFailed to retrieve gateways. Status code: {resp.status_code}")
            print(f"Response: {resp.text}")
            return False
    except Exception as e:
        print(f"\nError listing gateways: {e}")
        return False

def bind_dataset_to_gateway(workspace_id, dataset_id, gateway_id, headers):
    """
    Binds a dataset's datasource to a gateway to enable refresh capabilities for on-prem sources.
    """
    try:
        # First, get the datasources in the dataset
        datasources_url = f"https://api.powerbi.com/v1.0/myorg/groups/{workspace_id}/datasets/{dataset_id}/datasources"
        resp = requests.get(datasources_url, headers=headers)
        
        if resp.status_code != 200:
            print(f"Failed to get datasources. Status code: {resp.status_code}")
            print(f"Response: {resp.text}")
            return False, None
            
        datasources = resp.json().get("value", [])
        if not datasources:
            print("No datasources found in the dataset.")
            return False, None
            
        # Find the SQL datasource
        sql_datasource = None
        for ds in datasources:
            if ds.get("datasourceType") == "Sql":
                sql_datasource = ds
                break
                
        if not sql_datasource:
            print("No SQL datasource found in the dataset.")
            return False, None
            
        datasource_id = sql_datasource.get("datasourceId")
        
        # Build the binding request
        binding_url = f"https://api.powerbi.com/v1.0/myorg/groups/{workspace_id}/datasets/{dataset_id}/Default.BindToGateway"
        binding_payload = {
            "gatewayObjectId": gateway_id,
            "datasourceIds": [datasource_id]
        }
        
        print(f"\nBinding datasource {datasource_id} to gateway {gateway_id}...")
        binding_resp = requests.post(binding_url, headers=headers, json=binding_payload)
        
        if binding_resp.status_code in [200, 202]:
            print("Successfully bound dataset to gateway.")
            return True, datasource_id
        else:
            print(f"Failed to bind dataset to gateway. Status code: {binding_resp.status_code}")
            print(f"Response: {binding_resp.text}")
            return False, None
            
    except Exception as e:
        print(f"Error binding dataset to gateway: {e}")
        return False, None
        
def set_datasource_credentials(workspace_id, gateway_id, datasource_id, headers):
    """
    Set credentials for the datasource on the gateway.
    """
    try:
        # Prompt user for credentials
        print("\nPlease provide credentials for the SQL Server data source:")
        cred_type = input("Authentication type (basic or windows): ").strip().lower()
        username = input("Username: ").strip()
        password = input("Password: ").strip()
        
        # Build credential details based on auth type
        if cred_type == "basic":
            creds = {
                "credentialDetails": {
                    "credentialType": "Basic",
                    "credentials": json.dumps({
                        "credentialData": [
                            {"name": "username", "value": username},
                            {"name": "password", "value": password}
                        ]
                    }),
                    "encryptedConnection": "Encrypted",
                    "encryptionAlgorithm": "None",
                    "privacyLevel": "None"
                }
            }
        else:  # Windows auth
            creds = {
                "credentialDetails": {
                    "credentialType": "Windows",
                    "credentials": json.dumps({
                        "credentialData": [
                            {"name": "username", "value": username},
                            {"name": "password", "value": password}
                        ]
                    }),
                    "encryptedConnection": "Encrypted",
                    "encryptionAlgorithm": "None",
                    "privacyLevel": "None"
                }
            }
            
        # Set credentials
        creds_url = f"https://api.powerbi.com/v1.0/myorg/gateways/{gateway_id}/datasources/{datasource_id}"
        creds_resp = requests.patch(creds_url, headers=headers, json=creds)
        
        if creds_resp.status_code in [200, 202]:
            print("Successfully set datasource credentials.")
            return True
        else:
            print(f"Failed to set datasource credentials. Status code: {creds_resp.status_code}")
            print(f"Response: {creds_resp.text}")
            return False
            
    except Exception as e:
        print(f"Error setting datasource credentials: {e}")
        return False

def set_dataset_refresh_schedule(workspace_id, dataset_id, headers):
    """
    Set up a refresh schedule for the dataset.
    """
    try:
        # Ask user if they want to set up scheduled refresh
        setup_refresh = input("\nDo you want to set up scheduled refresh for this dataset? (y/n): ").strip().lower()
        if setup_refresh != 'y':
            print("Skipping refresh schedule setup.")
            return False
            
        # Get current time in local timezone
        local_time = datetime.datetime.now().time()
        # Default refresh time to current time
        default_hour = local_time.hour
        default_minute = local_time.minute
        
        # Ask for refresh time
        refresh_time = input(f"\nEnter refresh time (HH:MM) [default: {default_hour:02d}:{default_minute:02d}]: ").strip()
        if not refresh_time:
            hour = default_hour
            minute = default_minute
        else:
            try:
                hour, minute = map(int, refresh_time.split(':'))
                if hour < 0 or hour > 23 or minute < 0 or minute > 59:
                    print("Invalid time format. Using default time.")
                    hour = default_hour
                    minute = default_minute
            except:
                print("Invalid time format. Using default time.")
                hour = default_hour
                minute = default_minute
                
        # Build refresh schedule with improved structure
        schedule = {
            "value": {
                "enabled": True,
                "notifyOption": "MailOnFailure",
                "days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
                "times": [f"{hour:02d}:{minute:02d}"],
                "localTimeZoneId": "UTC"
            }
        }
        
        # Set refresh schedule
        schedule_url = f"https://api.powerbi.com/v1.0/myorg/groups/{workspace_id}/datasets/{dataset_id}/refreshSchedule"
        schedule_resp = requests.patch(schedule_url, headers=headers, json=schedule)
        
        if schedule_resp.status_code in [200, 202]:
            print("Successfully set up refresh schedule.")
            return True
        else:
            print(f"Failed to set up refresh schedule. Status code: {schedule_resp.status_code}")
            print(f"Response: {schedule_resp.text}")
            return False
            
    except Exception as e:
        print(f"Error setting up refresh schedule: {e}")
        return False

def trigger_dataset_refresh(workspace_id, dataset_id, headers):
    """
    Trigger an immediate refresh of the dataset.
    """
    try:
        # Ask user if they want to refresh now
        refresh_now = input("\nDo you want to trigger an immediate refresh? (y/n): ").strip().lower()
        if refresh_now != 'y':
            print("Skipping immediate refresh.")
            return False
            
        # Trigger refresh
        refresh_url = f"https://api.powerbi.com/v1.0/myorg/groups/{workspace_id}/datasets/{dataset_id}/refreshes"
        refresh_resp = requests.post(refresh_url, headers=headers)
        
        if refresh_resp.status_code in [200, 202]:
            print("Refresh triggered successfully. This may take a few minutes to complete.")
            return True
        else:
            print(f"Failed to trigger refresh. Status code: {refresh_resp.status_code}")
            print(f"Response: {refresh_resp.text}")
            return False
            
    except Exception as e:
        print(f"Error triggering refresh: {e}")
        return False

# List available gateways
list_power_bi_gateways(headers)

# Prompt for gateway ID for on-premises data source
gateway_id = input("\nEnter Power BI Gateway ID for on-premises SQL Server (leave empty if none): ").strip()
if not gateway_id:
    print("Warning: No gateway ID provided. Dataset refresh may not work for on-premises data sources.")

dataset_body = build_dataset_definition(dataset_name, merged_schema, gateway_id if gateway_id else None)
print("\nDataset Definition to be sent to Power BI:")
print(json.dumps(dataset_body, indent=2))
 
dataset_url = f"https://api.powerbi.com/v1.0/myorg/groups/{WORKSPACE_ID}/datasets?defaultRetentionPolicy=basicFIFO"
resp_create = requests.post(dataset_url, headers=headers, json=dataset_body)
print("\n--- Dataset Creation Response ---")
print("Status code:", resp_create.status_code)
try:
    resp_json = resp_create.json()
    print("Response JSON:", json.dumps(resp_json, indent=2))
except Exception as e:
    print("Error parsing response:", e)
    print("Response text:", resp_create.text)
    exit()
 
if resp_create.status_code not in [200, 201]:
    print("Failed to create dataset. Exiting.")
    exit()
 
dataset_id = resp_json.get("id")
if not dataset_id:
    print("No dataset ID returned. Exiting.")
    exit()
 
print(f"Dataset created successfully. Dataset ID: {dataset_id}")
 
#########################################
# 5. FETCH DATA FROM SQL SERVER & PUSH IT
#########################################
 
def convert_data_types(data_rows):
    """
    Convert non-JSON serializable types to JSON-serializable ones.
    """
    new_rows = []
    for row in data_rows:
        converted_row = {}
        for k, v in row.items():
            if v is None:
                converted_row[k] = None
            elif isinstance(v, decimal.Decimal):
                converted_row[k] = float(v)
            elif isinstance(v, (datetime.date, datetime.datetime)):
                converted_row[k] = v.isoformat()
            elif isinstance(v, (bytes, bytearray)):
                converted_row[k] = v.decode('utf-8', errors='replace')
            else:
                try:
                    json.dumps({k: v})
                    converted_row[k] = v
                except (TypeError, OverflowError):
                    converted_row[k] = str(v)
        new_rows.append(converted_row)
    return new_rows
 
def fetch_table_data(table_name: str) -> list:
    """
    Fetch all rows from a SQL Server table by first looking up the table schema.
    If the table's schema is found, it builds the fully qualified table name as [schema].[table_name].
    """
    try:
        cursor = sql_conn.cursor()
        # Lookup the schema name for the given table
        cursor.execute(f"SELECT TABLE_SCHEMA FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{table_name}'")
        row = cursor.fetchone()
        if row:
            table_schema = row[0]
            full_table_name = f"[{table_schema}].[{table_name}]"
        else:
            # If not found, fall back to using the table name as-is (no schema prefix)
            full_table_name = f"[{table_name}]"
       
        query = f"SELECT * FROM {full_table_name}"
        cursor.execute(query)
        rows = cursor.fetchall()
        col_names = [desc[0] for desc in cursor.description]
        data = [dict(zip(col_names, row)) for row in rows]
        return data
    except Exception as e:
        print(f"Error fetching data from table {table_name}: {e}")
        return []
 
 
# Loop through each table in the merged schema and push data
for table in merged_schema.get("tables", []):
    table_name = table.get("name")
    print(f"\n--- Fetching data for table '{table_name}' ---")
    data_rows = fetch_table_data(table_name)
   
    if not data_rows:
        print(f"No data found for table '{table_name}' or error occurred.")
        continue
   
    data_rows = convert_data_types(data_rows)
    rows_url = f"https://api.powerbi.com/v1.0/myorg/groups/{WORKSPACE_ID}/datasets/{dataset_id}/tables/{table_name}/rows"
    payload = {"rows": data_rows}
    print(f"Pushing rows for table '{table_name}'...")
    resp_push = requests.post(rows_url, headers=headers, json=payload)
    print(f"Status code for table '{table_name}':", resp_push.status_code)
    print("Response:", resp_push.text)

# If gateway ID was provided, try to bind the dataset to the gateway
if gateway_id:
    bind_result, datasource_id = bind_dataset_to_gateway(WORKSPACE_ID, dataset_id, gateway_id, headers)
    if bind_result:
        set_datasource_credentials(WORKSPACE_ID, gateway_id, datasource_id, headers)

# Set up refresh schedule if needed
set_dataset_refresh_schedule(WORKSPACE_ID, dataset_id, headers)

# Trigger immediate refresh if needed
trigger_dataset_refresh(WORKSPACE_ID, dataset_id, headers)

print("\nProcess complete!")