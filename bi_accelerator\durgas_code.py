import os
import json
import requests
import decimal
import datetime
import pyodbc
from dotenv import load_dotenv
 
# MicroStrategy Imports
from mstrio.connection import Connection
from mstrio.project_objects import Report, list_reports
 
# OpenAI Imports
import openai
 
# For Power BI authentication (Azure AD)
from azure.identity import ClientSecretCredential
 
load_dotenv()
 
#########################
# 1. MICROSTRATEGY SETUP
#########################
 
# MicroStrategy credentials and connection details
MSTR_URL = os.getenv('URL')  # e.g. "https://your-mstr-server.com/MicroStrategyLibrary/api"
MSTR_USER = os.getenv('USER')
MSTR_PASSWORD = os.getenv('PASSWORD')
MSTR_PROJECT_ID = '95550C99497DAAC3AC19DD86D91C4BB3' # e.g. "A428F8524EAD4DF409AAD49981473140"
 
# Connect to MicroStrategy project
mstr_conn = Connection(
    base_url=MSTR_URL,
    username=MSTR_USER,
    password=MSTR_PASSWORD,
    project_id=MSTR_PROJECT_ID,
    login_mode=1
)
mstr_conn.connect()
print("Connected to MicroStrategy Project successfully!")
 
# List available reports
print("\nList of Reports in the Project:")
reports = list_reports(connection=mstr_conn)
for r in reports:
    print(f"- {r.name} (ID: {r.id}) (Type: {r.type})")
 
# Ask user to select a report ID
report_id = input("\nEnter the Report ID to fetch its SQL view: ")
 
# Load the selected report and fetch its SQL view
report_obj = Report(connection=mstr_conn, id=report_id)
mstr_sql = report_obj.sql
print("\nFetched Report SQL:")
print(mstr_sql)
 
###############################################
# 2. EXTRACT SCHEMA INFORMATION VIA OPENAI API
###############################################
 
openai.api_key = os.getenv("OPENAI_API_KEY")
 
def extract_schema_from_sql(sql_text: str) -> dict:
    prompt = f"""
You are an expert data engineer. Given the following SQL query from MicroStrategy, extract and output a JSON with the following structure:
{{
  "tables": [
      {{
         "name": "<table_name>",
         "columns": [
              {{"name": "<column_name>", "dataType": "<PowerBI-compatible data type>"}}
         ]
      }}
  ],
  "relationships": [
      {{
         "name": "<relationship_name>",
         "fromTable": "<table_name>",
         "fromColumn": "<column_name>",
         "toTable": "<table_name>",
         "toColumn": "<column_name>",
         "crossFilteringBehavior": "<OneDirection | BothDirections | Automatic>"
      }}
  ]
}}
only give unique relationship, if no relationships exist, output an empty array for "relationships". Use data types such as "Int64", "string", "double", "DateTime", or "bool".
Here is the SQL:
{sql_text}
Output only valid JSON with no additional text.
"""
    try:
        response = openai.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a helpful assistant that extracts schema information from SQL queries."},
                {"role": "user", "content": prompt}
            ],
            temperature=0
        )
        schema_str = response.choices[0].message.content.strip()
        # Remove markdown code fences if present
        if schema_str.startswith("```"):
            lines = schema_str.splitlines()
            if lines[0].startswith("```"):
                lines = lines[1:]
            if lines and lines[-1].startswith("```"):
                lines = lines[:-1]
            schema_str = "\n".join(lines).strip()
        schema_json = json.loads(schema_str)
        return schema_json
    except Exception as e:
        print("Error extracting schema from SQL via OpenAI:", e)
        return {}
 
# Extract schema from MicroStrategy SQL
mstr_schema = extract_schema_from_sql(mstr_sql)
print("\nExtracted Schema Info from MSTR SQL:")
print(json.dumps(mstr_schema, indent=2))
 
#########################################
# 3. GET ACTUAL SQL SERVER SCHEMA INFO
#########################################
 
def get_sql_column_schema(connection, table_name):
    """
    Given a pyodbc connection and a table name, returns a list of (column_name, data_type)
    using SQL Server's INFORMATION_SCHEMA.
    """
    query = f"""
        SELECT COLUMN_NAME, DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = '{table_name}'
        ORDER BY ORDINAL_POSITION
    """
    cursor = connection.cursor()
    cursor.execute(query)
    return cursor.fetchall()
 
def map_sql_type_to_powerbi_type(sql_type):
    """
    Map common SQL Server data types to Power BI data types.
    """
    sql_type_lower = sql_type.lower()
    if "int" in sql_type_lower:
        return "Int64"
    elif "char" in sql_type_lower or "text" in sql_type_lower or "varchar" in sql_type_lower:
        return "string"
    elif "decimal" in sql_type_lower or "numeric" in sql_type_lower or "float" in sql_type_lower or "real" in sql_type_lower:
        return "double"
    elif "date" in sql_type_lower or "time" in sql_type_lower:
        return "DateTime"
    elif "bit" in sql_type_lower or "bool" in sql_type_lower:
        return "bool"
    else:
        return "string"
 
def merge_schema_with_sqlserver(mstr_schema: dict, sql_conn) -> dict:
    """
    For each table extracted from MicroStrategy SQL (mstr_schema), fetch the actual column schema
    from SQL Server. Build a new schema definition that uses the SQL Server metadata for the tables.
    """
    merged_tables = []
    for table in mstr_schema.get("tables", []):
        table_name = table.get("name")
        # First, try to fetch using the table name as-is.
        columns_schema = get_sql_column_schema(sql_conn, table_name)
        # If not found, try with "." prefix removed (since INFORMATION_SCHEMA.TABLES
        # might store name without schema).
        if not columns_schema:
            if "." in table_name:
                table_name_no_schema = table_name.split(".")[-1]
                columns_schema = get_sql_column_schema(sql_conn, table_name_no_schema)
            else:
                print(f"Warning: No columns found for table '{table_name}' in SQL Server.")
                continue
        # Build columns definition from SQL Server metadata
        columns_def = []
        for col_name, sql_type in columns_schema:
            pbi_type = map_sql_type_to_powerbi_type(sql_type)
            columns_def.append({"name": col_name, "dataType": pbi_type})
        merged_tables.append({"name": table_name, "columns": columns_def})
    # We'll keep the relationships from mstr_schema, if any, though push datasets
    # have limited modeling capabilities.
    relationships = mstr_schema.get("relationships", [])
    new_schema = {
        "tables": merged_tables,
        "relationships": relationships  # though push datasets typically don't use them
    }
    return new_schema
 
# Connect to SQL Server using pyodbc
db_server = os.getenv("SERVER")
db_name = os.getenv("DATABASE")
db_user = os.getenv("DB_USER") or os.getenv("USERNAME")
db_pass = os.getenv("PASS")
conn_str = f"Driver={{ODBC Driver 17 for SQL Server}};Server={db_server};Database={db_name};UID={db_user};PWD=*********;"
try:
    sql_conn = pyodbc.connect(conn_str)
except Exception as e:
    print("Error connecting to SQL Server:", e)
    exit()
 
# Merge the schema from MSTR with actual SQL Server metadata
merged_schema = merge_schema_with_sqlserver(mstr_schema, sql_conn)
print("\nMerged Schema (from SQL Server):")
print(json.dumps(merged_schema, indent=2))
 
#########################################
# 4. CREATE POWER BI DATASET (Push Mode)
#########################################
 
# Power BI Credentials from .env
TENANT_ID = os.getenv("TENANT_ID")
CLIENT_ID = os.getenv("CLIENT_ID")
CLIENT_SECRET = os.getenv("CLIENT_SECRET")
WORKSPACE_ID = os.getenv("PPU_WORKSPACE_ID")  # Power BI workspace (group) ID
 
credential = ClientSecretCredential(
    tenant_id=TENANT_ID,
    client_id=CLIENT_ID,
    client_secret=CLIENT_SECRET
)
token = credential.get_token("https://analysis.windows.net/powerbi/api/.default").token
 
headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {token}"
}
 
def build_dataset_definition(dataset_name: str, schema: dict) -> dict:
    """
    Build the dataset definition for a push dataset using the merged schema.
    We also include relationships if they exist, but push datasets do not always
    fully support relationships.
    """
    dataset_def = {
        "name": dataset_name,
        "defaultMode": "Push",
        "tables": schema.get("tables", [])
    }
    # Attempt to include relationships if your scenario absolutely needs them.
    # For standard push datasets, the relationships field might be ignored, but
    # we keep it here in case you want to experiment.
    rels = schema.get("relationships", [])
    if rels:
        dataset_def["relationships"] = rels
 
    return dataset_def
 
dataset_name = input("\nEnter desired Power BI dataset name: ").strip()
dataset_body = build_dataset_definition(dataset_name, merged_schema)
print("\nDataset Definition to be sent to Power BI:")
print(json.dumps(dataset_body, indent=2))
 
dataset_url = f"https://api.powerbi.com/v1.0/myorg/groups/{WORKSPACE_ID}/datasets?defaultRetentionPolicy=basicFIFO"
resp_create = requests.post(dataset_url, headers=headers, json=dataset_body)
print("\n--- Dataset Creation Response ---")
print("Status code:", resp_create.status_code)
try:
    resp_json = resp_create.json()
    print("Response JSON:", json.dumps(resp_json, indent=2))
except Exception as e:
    print("Error parsing response:", e)
    print("Response text:", resp_create.text)
    exit()
 
if resp_create.status_code not in [200, 201]:
    print("Failed to create dataset. Exiting.")
    exit()
 
dataset_id = resp_json.get("id")
if not dataset_id:
    print("No dataset ID returned. Exiting.")
    exit()
 
print(f"Dataset created successfully. Dataset ID: {dataset_id}")
 
#########################################
# 4.1 ON-PREM GATEWAY SETUP & DATA SOURCE
#     (OPTIONAL - For Scheduled Refresh)
#########################################
 
# If you want to schedule refresh from on-prem SQL (instead of pushing data),
# you need to bind the dataset to a gateway data source.
# Steps:
#   1) Identify gateway ID (gateway_id).
#   2) Create data source on the gateway or re-use existing data source ID.
#   3) Encrypt credentials (RSA) and pass them in the Create Datasource call.
#   4) Bind dataset to the gateway data source.
 
# For demonstration, we'll show placeholders.
# If you already have a gateway & data source, skip to '4.2 Bind Dataset to Gateway'.
 
USE_GATEWAY = os.getenv("USE_GATEWAY", "false").lower() == "true"
 
if USE_GATEWAY:
    gateway_id = os.getenv("GATEWAY_ID")  # The GUID of your on-prem gateway
    sql_server_name = db_server           # The server name
    sql_db_name = db_name
 
    # Step 4.1a: Retrieve gateway public key
    gateway_info_url = f"https://api.powerbi.com/v1.0/myorg/gateways/{gateway_id}"
    resp_gateway_info = requests.get(gateway_info_url, headers=headers)
    if resp_gateway_info.status_code == 200:
        ginfo = resp_gateway_info.json()
        public_key = ginfo.get("publicKey", {})
        exponent_b64 = public_key.get("exponent")
        modulus_b64 = public_key.get("modulus")
        if not exponent_b64 or not modulus_b64:
            print("Could not retrieve gateway public key.")
            gateway_id = None
    else:
        print("Failed to fetch gateway info.", resp_gateway_info.text)
        gateway_id = None
 
    # Step 4.1b: Encrypt credentials with RSA (Requires 'pycryptodome' or 'cryptography')
    # This example will show how you might do it with 'pycryptodome'.
    from Crypto.PublicKey import RSA
    from Crypto.Cipher import PKCS1_OAEP
    import base64
 
    def encrypt_credentials(username, password, exponent_b64, modulus_b64):
        # Build credential JSON
        cred_str = json.dumps({
            "credentialData": [
                {"name": "username", "value": username},
                {"name": "password", "value": password}
            ]
        })
 
        # Convert exponent & modulus
        exponent = int.from_bytes(base64.b64decode(exponent_b64), byteorder='big')
        modulus = int.from_bytes(base64.b64decode(modulus_b64), byteorder='big')
        rsa_key = RSA.construct((modulus, exponent))
        cipher = PKCS1_OAEP.new(rsa_key)
        encrypted_bytes = cipher.encrypt(cred_str.encode("utf-8"))
        return base64.b64encode(encrypted_bytes).decode("utf-8")
 
    if gateway_id:
        # Attempt to create a data source on the gateway
        ds_url = f"https://api.powerbi.com/v1.0/myorg/gateways/{gateway_id}/datasources"
        encrypted_cred = encrypt_credentials(db_user, db_pass, exponent_b64, modulus_b64)
        datasource_payload = {
            "dataSourceType": "SQL",
            "connectionDetails": json.dumps({
                "server": sql_server_name,
                "database": sql_db_name
            }),
            "datasourceName": "MyOnPremSQLDatasource",
            "credentialDetails": {
                "credentialType": "Basic",  # or 'Windows'
                "credentials": encrypted_cred,
                "encryptedConnection": "Encrypted",
                "encryptionAlgorithm": "RSA-OAEP",
                "privacyLevel": "None"
            }
        }
        resp_ds_create = requests.post(ds_url, headers=headers, json=datasource_payload)
        print("\n--- Gateway Data Source Creation Response ---")
        print("Status code:", resp_ds_create.status_code)
        print("Response:", resp_ds_create.text)
 
        if resp_ds_create.status_code in [200, 201]:
            ds_json = resp_ds_create.json()
            data_source_id = ds_json.get("id")
            print(f"Data Source created. ID: {data_source_id}")
        else:
            print("Failed to create data source.")
            data_source_id = None
    else:
        data_source_id = None
 
#########################################
# 4.2 BIND DATASET TO GATEWAY (OPTIONAL)
#########################################
BIND_TO_GATEWAY = os.getenv("BIND_TO_GATEWAY", "false").lower() == "true"
if USE_GATEWAY and BIND_TO_GATEWAY and gateway_id and data_source_id:
    bind_url = f"https://api.powerbi.com/v1.0/myorg/groups/{WORKSPACE_ID}/datasets/{dataset_id}/Default.BindToGateway"
    bind_payload = {
        "gatewayObjectId": gateway_id,
        "datasourceObjectIds": [data_source_id]
    }
    resp_bind = requests.post(bind_url, headers=headers, json=bind_payload)
    print("\n--- Bind to Gateway Response ---")
    print("Status code:", resp_bind.status_code)
    print("Response:", resp_bind.text)
    if resp_bind.status_code == 200:
        print("Dataset bound to gateway successfully!")
    else:
        print("Failed to bind dataset to gateway.")
 
#########################################
# 4.3 CONFIGURE SCHEDULED REFRESH (OPTIONAL)
#########################################
CONFIGURE_REFRESH = os.getenv("CONFIGURE_REFRESH", "false").lower() == "true"
 
if CONFIGURE_REFRESH and USE_GATEWAY and gateway_id and data_source_id:
    # Example: Refresh every weekday at 07:00 and 18:00
    sched_url = f"https://api.powerbi.com/v1.0/myorg/groups/{WORKSPACE_ID}/datasets/{dataset_id}/refreshSchedule"
    sched_payload = {
        "value": {
            "days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
            "times": ["07:00", "18:00"],
            "localTimeZoneId": "Pacific Standard Time",  # e.g., "UTC" or "Pacific Standard Time"
            "notifyOption": "MailOnFailure"
        }
    }
    resp_sched = requests.patch(sched_url, headers=headers, json=sched_payload)
    print("\n--- Configure Refresh Schedule Response ---")
    print("Status code:", resp_sched.status_code)
    print("Response:", resp_sched.text)
    if resp_sched.status_code == 200:
        print("Refresh schedule configured successfully!")
    else:
        print("Failed to configure refresh schedule.")
 
#########################################
# 5. FETCH DATA FROM SQL SERVER & PUSH IT
#########################################
 
def convert_data_types(data_rows):
    """
    Convert non-JSON serializable types to JSON-serializable ones.
    """
    new_rows = []
    for row in data_rows:
        converted_row = {}
        for k, v in row.items():
            if v is None:
                converted_row[k] = None
            elif isinstance(v, decimal.Decimal):
                converted_row[k] = float(v)
            elif isinstance(v, (datetime.date, datetime.datetime)):
                converted_row[k] = v.isoformat()
            elif isinstance(v, (bytes, bytearray)):
                converted_row[k] = v.decode('utf-8', errors='replace')
            else:
                try:
                    json.dumps({k: v})
                    converted_row[k] = v
                except (TypeError, OverflowError):
                    converted_row[k] = str(v)
        new_rows.append(converted_row)
    return new_rows
 
def fetch_table_data(table_name: str) -> list:
    """
    Fetch all rows from a SQL Server table by first looking up the table schema.
    If the table's schema is found, build a fully qualified name as [schema].[table_name].
    """
    try:
        cursor = sql_conn.cursor()
        # Lookup the schema name for the given table
        cursor.execute(f"SELECT TABLE_SCHEMA FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{table_name}'")
        row = cursor.fetchone()
        if row:
            table_schema = row[0]
            full_table_name = f"[{table_schema}].[{table_name}]"
        else:
            # If not found, fall back to using the table name as-is (no schema prefix)
            full_table_name = f"[{table_name}]"
       
        query = f"SELECT * FROM {full_table_name}"
        cursor.execute(query)
        rows = cursor.fetchall()
        col_names = [desc[0] for desc in cursor.description]
        data = [dict(zip(col_names, row)) for row in rows]
        return data
    except Exception as e:
        print(f"Error fetching data from table {full_table_name}: {e}")
        return []
 
# Loop through each table in the merged schema and push data
for table in merged_schema.get("tables", []):
    table_name = table.get("name")
    print(f"\n--- Fetching data for table '{table_name}' ---")
    data_rows = fetch_table_data(table_name)
   
    if not data_rows:
        print(f"No data found for table '{table_name}' or error occurred.")
        continue
   
    data_rows = convert_data_types(data_rows)
    rows_url = f"https://api.powerbi.com/v1.0/myorg/groups/{WORKSPACE_ID}/datasets/{dataset_id}/tables/{table_name}/rows"
    payload = {"rows": data_rows}
    print(f"Pushing rows for table '{table_name}'...")
    resp_push = requests.post(rows_url, headers=headers, json=payload)
    print(f"Status code for table '{table_name}':", resp_push.status_code)
    print("Response:", resp_push.text)
 
print("\nProcess complete!")