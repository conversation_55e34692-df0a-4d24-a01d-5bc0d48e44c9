# Analytics Platform

A FastAPI-based web application for interacting with MicroStrategy reports, dossiers, and cubes.

## Prerequisites

- Python 3.8 or higher
- pip (Python package installer)
- MicroStrategy environment with proper access credentials

## Setup

1. Clone this repository:
```bash
git clone <repository-url>
cd bi-accelerator
```

2. Create and activate a virtual environment:
```bash
# Windows
python -m venv .venv
.venv\Scripts\activate

# Linux/MacOS
python -m venv .venv
source .venv/bin/activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Configure environment variables:
Create a `.env` file in the root directory with the following content:
```
URL = "your-microstrategy-url"
USER = "your-username"
PASSWORD = "your-password"
```

## Running the Application

1. Start the FastAPI server:
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

2. Open your browser and navigate to:
```
http://localhost:8000
```

3. Access the interactive API documentation:
```
http://localhost:8000/docs
```

## Features

- Browse MicroStrategy projects
- View reports, dossiers, and cubes
- Analyze report and dossier structures
- Extract SQL queries from reports and cubes
- Explore object attributes, metrics, and their dependencies
- Examine backend relationship information
- Comprehensive error handling
- Real-time streaming updates via Server-Sent Events for long-running processes

## API Endpoints

### General
- `GET /`: Main application interface
- `GET /health`: Health check endpoint

### Projects
- `GET /api/projects`: List all available projects

### Reports
- `GET /api/reports?project_id={project_id}`: List reports in a project
- `GET /api/report/{report_id}?project_id={project_id}`: Get detailed report information

### Dossiers
- `GET /api/dossiers?project_id={project_id}`: List dossiers in a project
- `GET /api/dossier/{dossier_id}?project_id={project_id}`: Get detailed dossier information

### Cubes
- `GET /api/cubes?project_id={project_id}`: List all cubes in a project
- `GET /api/cube/{cube_id}?project_id={project_id}&cube_type={cube_type}`: Get detailed cube information
- `GET /api/cube/{cube_id}/sql?project_id={project_id}&cube_type={cube_type}`: Get SQL view of a cube

### Attributes
- `GET /api/attributes?project_id={project_id}`: List all attributes in a project
- `GET /api/attribute/{attribute_id}?project_id={project_id}`: Get detailed attribute information

### Metrics
- `GET /api/metrics?project_id={project_id}`: List all metrics in a project
- `GET /api/metric/{metric_id}?project_id={project_id}`: Get detailed metric information with dependencies

### Analysis Tools
- `POST /api/parse-sql`: Parse SQL query structure
- `GET /api/object-attributes?object_type={object_type}&object_id={object_id}&project_id={project_id}`: Get object attributes and metrics
- `GET /api/backend-relationships?object_type={object_type}&object_id={object_id}&project_id={project_id}`: Get backend table relationships

### Power BI Integration
- `POST /api/power-bi/create-dataset`: Create a Power BI dataset from a MicroStrategy report
- `GET /api/power-bi/dataset-status/{task_id}`: Check the status of a dataset creation task
- `GET /api/power-bi/dataset-updates/{task_id}`: Stream real-time updates via SSE during dataset creation

## Query Parameters
- `project_id`: The ID of the MicroStrategy project
- `object_type`: Type of object ("report", "dossier", "supercube", or "olapcube")
- `cube_type`: Type of cube ("SuperCube" or "OlapCube")

## Security Note

- Never commit your `.env` file containing sensitive credentials
- Use appropriate authentication mechanisms in production
- Follow your organization's security guidelines
- For production, set specific CORS origins instead of wildcard ("*") 