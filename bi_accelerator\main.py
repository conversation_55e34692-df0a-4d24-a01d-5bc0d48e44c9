from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
import os
import logging
import time
import traceback
from logging.handlers import RotatingFileHandler
from dotenv import load_dotenv

# Import routers
from routers import projects, reports, dashboards
from routers import cubes, metrics, attributes, utils_api
from routers import sql_analysis, credentials, power_bi, sql_batch_analysis
from routers import mstr_summary  # Add the new mstr_summary router
from routers import auth  # Import the new auth router
from routers import environments  # Import the environments router
from routers import tableau
# Configure logging
logger = logging.getLogger("bi_accelerator")
logger.setLevel(logging.DEBUG)

# Create handlers
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)

# Create a rotating file handler (10MB max size, keep 5 backup files)
file_handler = RotatingFileHandler(
    "bi_accelerator.log", maxBytes=10*1024*1024, backupCount=5
)
file_handler.setLevel(logging.DEBUG)

# Create formatters and add them to handlers
log_format = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
console_handler.setFormatter(log_format)
file_handler.setFormatter(log_format)

# Add handlers to the logger
logger.addHandler(console_handler)
logger.addHandler(file_handler)

# Initialize FastAPI app
app = FastAPI(title="Analytics API", description="API for MicroStrategy Analytics")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Log startup information
logger.info("Starting Analytics API")

# Add middleware to log request timing
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    
    # Get client IP and request details
    client_host = request.client.host if request.client else "unknown"
    request_path = request.url.path
    
    logger.info(f"Request started: {request.method} {request_path} from {client_host}")
    
    try:
        response = await call_next(request)
        process_time = time.time() - start_time
        logger.info(f"Request completed: {request.method} {request_path} - Status: {response.status_code} - Time: {process_time:.4f}s")
        return response
    except Exception as e:
        logger.error(f"Request failed: {request.method} {request_path} - Error: {str(e)}")
        logger.error(traceback.format_exc())
        raise

@app.get("/")
async def root():
    return {"message": "Analytics API is running. Use the /docs endpoint to view the API documentation."}

# We've moved this to the auth router, so we can remove it from here
# @app.get("/api/auth/credentials")
# async def get_auth_credentials():
#     """
#     Return default login credentials from .env file.
#     This is for demo purposes only and should be replaced with proper auth in production.
#     """
#     # In production, you would NEVER expose credentials like this
#     # This is only for the demo/development environment
#     return {
#         "username": os.getenv("LOGIN_USERNAME", "admin"),
#         "password": os.getenv("LOGIN_PASSWORD", "password")
#     }

# Include all routers
app.include_router(credentials.router)
app.include_router(projects.router)
app.include_router(reports.router)
app.include_router(dashboards.router)
app.include_router(cubes.router)
app.include_router(metrics.router)
app.include_router(attributes.router)
app.include_router(utils_api.router)
app.include_router(sql_analysis.router)
app.include_router(sql_batch_analysis.router)
app.include_router(power_bi.router)
app.include_router(mstr_summary.router)  # Add the new mstr_summary router
app.include_router(auth.router)  # Add the auth router
app.include_router(environments.router)  # Add the environments router 
app.include_router(tableau.router)  # Add the Tableau router