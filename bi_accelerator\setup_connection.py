#!/usr/bin/env python
"""
MicroStrategy Connection Setup Utility

This script helps set up and test the connection to the MicroStrategy server.
It allows you to enter credentials and test the connection before running the main application.

Usage:
    python setup_connection.py
"""

import os
import sys
import getpass
from dotenv import load_dotenv
import requests
from mstrio.connection import Connection

# Load environment variables from .env file
load_dotenv()

def test_connection(url, username, password):
    """Test connection to MicroStrategy using provided credentials"""
    print("\nTesting connection to MicroStrategy...")
    
    try:
        # First, check if the URL is reachable
        response = requests.get(url.split('/api')[0], timeout=5)
        if response.status_code >= 400:
            print(f"Warning: URL returned status code {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"Warning: Could not reach the URL ({url}): {str(e)}")
        return False
    
    try:
        # Now try to connect using mstrio
        conn = Connection(
            base_url=url,
            username=username,
            password=password,
            login_mode=1
        )
        conn.connect()
        print("\n✅ Connection successful!")
        print(f"Connected to MicroStrategy as user: {username}")
        return True
    except Exception as e:
        print(f"\n❌ Connection failed: {str(e)}")
        return False

def main():
    """Main function to setup and test connection"""
    print("=" * 50)
    print("MicroStrategy Connection Setup Utility")
    print("=" * 50)
    
    # Get current values from .env file
    current_url = os.getenv('URL', '')
    current_user = os.getenv('USER', '')
    
    # Prompt for MicroStrategy connection details
    print("\nEnter MicroStrategy connection details (press Enter to keep current value):")
    
    url = input(f"URL [{current_url}]: ") or current_url
    username = input(f"Username [{current_user}]: ") or current_user
    password = getpass.getpass("Password (will not be displayed): ")
    
    # Test the connection
    if test_connection(url, username, password):
        # Ask if user wants to save these credentials
        save = input("\nDo you want to save these credentials for the application? (y/n): ").lower()
        if save == 'y':
            print("\nCredentials can be saved in the following ways:")
            print("1. Set as environment variables in your terminal session")
            print("2. Save password in the .env file (less secure)")
            choice = input("Choose an option (1/2): ")
            
            if choice == '1':
                print("\nTo set environment variables in your terminal, run:")
                print(f"export URL=\"{url}\"")
                print(f"export USER=\"{username}\"")
                print(f"export MSTR_PASSWORD=\"{password}\"")
                print("\nThen run your application in the same terminal session.")
            elif choice == '2':
                try:
                    with open('.env', 'r') as file:
                        content = file.readlines()
                    
                    # Update values in .env file
                    for i, line in enumerate(content):
                        if line.startswith('URL ='):
                            content[i] = f'URL = "{url}"\n'
                        elif line.startswith('USER ='):
                            content[i] = f'USER = "{username}"\n'
                        elif line.startswith('PASSWORD ='):
                            content[i] = f'PASSWORD = "{password}"  # Updated by setup script\n'
                    
                    with open('.env', 'w') as file:
                        file.writelines(content)
                    
                    print("\n✅ Credentials saved to .env file.")
                    print("Warning: Storing passwords in the .env file is less secure.")
                except Exception as e:
                    print(f"\n❌ Failed to update .env file: {str(e)}")
            else:
                print("\nNo changes were made to the configuration.")
    
    print("\nSetup complete. You can now run the application.")

if __name__ == "__main__":
    main() 