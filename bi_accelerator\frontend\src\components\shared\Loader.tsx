import { cn } from "@/lib/utils";

interface LoaderProps {
  fullScreen?: boolean;
  className?: string;
}

/**
 * Loader component that displays the lightbulb animation
 * Can be displayed as a fullscreen loader or inline
 */
export function Loader({ fullScreen = false, className }: LoaderProps) {
  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-background/80 flex items-center justify-center z-50">
        <div className={cn("flex flex-col items-center", className)}>
          <img
            src="/dfz_Lightbulb-animation.gif"
            alt="Loading..."
            className="w-32 h-32 object-contain"
          />
          <p className="text-muted-foreground mt-3 text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col items-center", className)}>
      <img
        src="/dfz_Lightbulb-animation.gif"
        alt="Loading..."
        className="w-24 h-24 object-contain"
      />
      <p className="text-muted-foreground text-base mt-2">Loading...</p>
    </div>
  );
}

export default Loader; 