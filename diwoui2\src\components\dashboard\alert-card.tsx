import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { ArrowRight } from "lucide-react";
import { AlertSeverity, severityColorMap } from "@/types/alerts";
import { useBrandTheme } from "@/themes/brand-theme-provider";
import { useTheme } from "@/lib/theme-provider";
import { ThemeFunctionCode } from "@/themes/types";
import { useRef, useState } from "react";

// Base color map for consistent alert styling
const BASE_SEVERITY_COLORS = {
  critical: {
    light: {
      border: '#DC2626', // red-600
      bg: '#FEE2E2',     // red-50
      text: '#991B1B',   // red-800
      action: '#DC2626', // red-600
      cardBg: '#FFFFFF', // white
      titleText: '#111827', // gray-900
      descText: '#6B7280', // gray-500
      timeText: '#9CA3AF'  // gray-400
    },
    dark: {
      border: '#EF4444', // red-500
      bg: '#7F1D1D',     // red-900
      text: '#FECACA',   // red-200
      action: '#F87171', // red-400
      cardBg: '#1F2937', // gray-800
      titleText: '#F9FAFB', // gray-50
      descText: '#D1D5DB', // gray-300
      timeText: '#9CA3AF'  // gray-400
    }
  },
  high: {
    light: {
      border: '#16A34A', // green-600
      bg: '#F0FDF4',     // green-50
      text: '#166534',   // green-800
      action: '#16A34A', // green-600
      cardBg: '#FFFFFF', // white
      titleText: '#111827', // gray-900
      descText: '#6B7280', // gray-500
      timeText: '#9CA3AF'  // gray-400
    },
    dark: {
      border: '#22C55E', // green-500
      bg: '#14532D',     // green-900
      text: '#BBF7D0',   // green-200
      action: '#4ADE80', // green-400
      cardBg: '#1F2937', // gray-800
      titleText: '#F9FAFB', // gray-50
      descText: '#D1D5DB', // gray-300
      timeText: '#9CA3AF'  // gray-400
    }
  },
  medium: {
    light: {
      border: '#EAB308', // yellow-500
      bg: '#FFFBEB',     // yellow-50
      text: '#92400E',   // yellow-800
      action: '#EAB308', // yellow-500
      cardBg: '#FFFFFF', // white
      titleText: '#111827', // gray-900
      descText: '#6B7280', // gray-500
      timeText: '#9CA3AF'  // gray-400
    },
    dark: {
      border: '#FACC15', // yellow-400
      bg: '#713F12',     // yellow-900
      text: '#FEF08A',   // yellow-200
      action: '#FACC15', // yellow-400
      cardBg: '#1F2937', // gray-800
      titleText: '#F9FAFB', // gray-50
      descText: '#D1D5DB', // gray-300
      timeText: '#9CA3AF'  // gray-400
    }
  },
  low: {
    light: {
      border: '#2563EB', // blue-600
      bg: '#EFF6FF',     // blue-50
      text: '#1E40AF',   // blue-800
      action: '#2563EB', // blue-600
      cardBg: '#FFFFFF', // white
      titleText: '#111827', // gray-900
      descText: '#6B7280', // gray-500
      timeText: '#9CA3AF'  // gray-400
    },
    dark: {
      border: '#3B82F6', // blue-500
      bg: '#172554',     // blue-900
      text: '#BFDBFE',   // blue-200
      action: '#60A5FA', // blue-400
      cardBg: '#1F2937', // gray-800
      titleText: '#F9FAFB', // gray-50
      descText: '#D1D5DB', // gray-300
      timeText: '#9CA3AF'  // gray-400
    }
  }
};

// Configuration for which severities can use theme accent colors
// true = use theme accent colors, false = use base colors
const THEMEABLE_SEVERITIES = {
  // Google can theme all severities
  google: {
    critical: true,
    high: true,
    medium: true,
    low: true
  },
  // For all other themes, only allow theming everything except medium severity
  default: {
    critical: true,
    high: true,
    medium: false, // Medium Impact always uses yellow exactly as shown in image
    low: true
  }
};

interface AlertCardProps {
  id: string;
  title: string;
  description: string;
  category: string;
  severity: AlertSeverity;
  time: string;
  action: string;
}

export function AlertCard({
  id,
  title,
  description,
  category,
  severity,
  time,
  action,
}: AlertCardProps) {
  const navigate = useNavigate();
  const { currentTheme } = useBrandTheme();
  const { theme } = useTheme(); // Access the current color mode
  const accentColors = currentTheme?.colors?.accent;
  const themeId = currentTheme?.id || 'default';
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  
  // Determine if the color mode is dark or light
  const colorMode = theme === 'system' 
    ? window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    : theme;
  
  // Get severity color scheme based on theme, severity, and color mode
  const getSeverityColors = () => {
    // Map severity to accent color key
    const severityToAccentKey = {
      critical: "red",
      high: "green",
      medium: "yellow",
      low: "blue"
    };
    
    // Determine if this severity level should use theme accent colors
    // Get the theme-specific settings or fall back to default settings
    const themeSettings = 
      THEMEABLE_SEVERITIES[themeId as keyof typeof THEMEABLE_SEVERITIES] || 
      THEMEABLE_SEVERITIES.default;
    
    // Check if this severity should use theme accent colors
    const canUseThemeColors = themeSettings[severity];
    
    // Only use accent colors if:
    // 1. This severity can use theme colors for this theme
    // 2. Theme accent colors are available
    // 3. We have a mapping for this severity
    const useAccentColors = 
      canUseThemeColors && 
      accentColors && 
      severityToAccentKey[severity] in accentColors;
    
    if (useAccentColors) {
      // Theme-specific styling using theme's accent colors
      const colorKey = severityToAccentKey[severity];
      const baseColors = BASE_SEVERITY_COLORS[severity][colorMode];
      return {
        border: accentColors[colorKey as keyof typeof accentColors],
        bg: baseColors.bg,
        text: baseColors.text,
        action: accentColors[colorKey as keyof typeof accentColors],
        cardBg: baseColors.cardBg,
        titleText: baseColors.titleText,
        descText: baseColors.descText,
        timeText: baseColors.timeText
      };
    }
    
    // Default to base severity colors for current color mode
    return BASE_SEVERITY_COLORS[severity][colorMode];
  };
  
  // Get the appropriate colors for this alert
  const colors = getSeverityColors();

  // Get theme primary color for hover effects
  const getHoverEffectColor = () => {
    // If we have a current theme with primary color
    if (currentTheme?.colors?.primary?.[500]) {
      const primaryColor = currentTheme.colors.primary[500];
      return {
        borderColor: primaryColor,
        boxShadow: `0 8px 16px ${colorMode === 'dark' 
          ? `rgba(${hexToRgb(primaryColor)}, 0.4)`
          : `rgba(${hexToRgb(primaryColor)}, 0.2)`}`,
        // Add a subtle background color shift based on theme
        bgGlow: colorMode === 'dark'
          ? `rgba(${hexToRgb(primaryColor)}, 0.05)`
          : `rgba(${hexToRgb(primaryColor)}, 0.03)`,
        // Color for a subtle border glow effect
        borderGlow: colorMode === 'dark'
          ? `rgba(${hexToRgb(primaryColor)}, 0.3)`
          : `rgba(${hexToRgb(primaryColor)}, 0.15)`
      };
    }
    
    // Otherwise, fall back to severity colors
    return {
      borderColor: colors.border,
      boxShadow: `0 8px 16px ${colorMode === 'dark' 
        ? `rgba(${hexToRgb(colors.border)}, 0.4)`
        : `rgba(${hexToRgb(colors.border)}, 0.2)`}`,
      // Add a subtle background color shift based on severity
      bgGlow: colorMode === 'dark'
        ? `rgba(${hexToRgb(colors.border)}, 0.05)`
        : `rgba(${hexToRgb(colors.border)}, 0.03)`,
      // Color for a subtle border glow effect
      borderGlow: colorMode === 'dark'
        ? `rgba(${hexToRgb(colors.border)}, 0.3)`
        : `rgba(${hexToRgb(colors.border)}, 0.15)`
    };
  };
  
  // Convert hex to RGB for shadow effects
  const hexToRgb = (hex: string): string => {
    // Remove # if present
    hex = hex.replace('#', '');
    
    // Parse hex
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    
    return `${r}, ${g}, ${b}`;
  };
  
  // Handle navigation when action is clicked
  const handleActionClick = () => {
    navigate(`/conversation/action/${action}/${id}`);
  };

  const capitalizeFirstLetter = (string: string) => {
    return string.charAt(0).toUpperCase() + string.slice(1);
  };

  // Apply specific rounded corners for the badge to match the image
  const getBadgeStyle = () => {
    const style = {
      backgroundColor: colors.bg,
      color: colors.text,
      borderRadius: '16px', // More rounded corners to match image
      padding: '6px 16px'   // Slightly more horizontal padding
    };
    
    return style;
  };

  // Get the hover effect styles
  const hoverEffectStyles = getHoverEffectColor();

  // Get dynamic card styles including hover effects
  const getCardStyles = () => {
    const baseStyles = { 
      borderLeftColor: colors.border,
      backgroundColor: colors.cardBg,
      transition: 'all 0.3s ease'
    };
    
    if (isHovered) {
      const hoverEffects = getHoverEffectColor();
      return {
        ...baseStyles,
        boxShadow: hoverEffects.boxShadow,
        transform: 'translateY(-4px)',
        // Remove the borderLeftColor change to keep the original color
        backgroundColor: hoverEffects.bgGlow,
        // Add a subtle border on all sides with theme color, except the left
        outline: `1px solid ${hoverEffects.borderGlow}`,
        outlineOffset: '-1px',
      };
    }
    
    return baseStyles;
  };

  return (
    <Card 
      ref={cardRef}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={cn(
        "w-full overflow-hidden border-l-4 flex flex-col relative rounded-lg",
        colorMode === 'dark' ? 'border-t-0 border-r-0 border-b-0' : '',
        isHovered ? 'z-10' : 'z-0' // Add z-index when hovered to make sure shadow is visible
      )}
      style={getCardStyles()}
    >
      {severity === 'critical' && (
        <div className="critical-dot" style={{ backgroundColor: colors.border }}></div>
      )}
      <div className="p-5 flex-grow">
        <div className="mb-3">
          <div
            className="inline-block mb-3 text-xs font-medium"
            style={getBadgeStyle()}
          >
            {category}
          </div>
          <h3 className="text-lg font-medium mb-2" style={{ color: colors.titleText }}>{title}</h3>
          <p className="text-sm" style={{ color: colors.descText }}>{description}</p>
        </div>
      </div>
      <div className="px-5 py-3 border-t flex items-center justify-between" 
        style={{ borderColor: colorMode === 'dark' ? 'rgba(75, 85, 99, 0.5)' : 'rgba(229, 231, 235, 1)' }}>
        <p className="text-xs" style={{ color: colors.timeText }}>{time}</p>
        <div 
          className="flex items-center cursor-pointer"
          onClick={handleActionClick}
        >
          <span 
            className="text-sm font-medium"
            style={{ color: colors.action }}
          >
            {capitalizeFirstLetter(action)}
          </span>
          <ArrowRight 
            className="ml-1 h-4 w-4" 
            style={{ color: colors.action }}
          />
        </div>
      </div>
    </Card>
  );
}
