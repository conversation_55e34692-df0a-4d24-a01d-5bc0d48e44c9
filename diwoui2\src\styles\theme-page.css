/* Theme page visual enhancements */

/* Background decorative elements */
.theme-page-container {
  position: relative;
  z-index: 1;
}

.theme-page-container::before {
  content: '';
  position: absolute;
  top: -200px;
  right: -200px;
  width: 500px;
  height: 500px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(234, 56, 76, 0.05) 0%, rgba(234, 56, 76, 0.02) 30%, transparent 70%);
  z-index: -1;
  animation: float 10s ease-in-out infinite;
}

.theme-page-container::after {
  content: '';
  position: absolute;
  bottom: -100px;
  left: -100px;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(66, 153, 225, 0.05) 0%, rgba(66, 153, 225, 0.02) 40%, transparent 70%);
  z-index: -1;
  animation: float 14s ease-in-out infinite reverse;
}

/* Theme heading enhancement */
.theme-page-title {
  background: linear-gradient(90deg, hsl(var(--primary)) 0%, hsl(217, 91%, 60%) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
  position: relative;
}

.theme-page-title::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -4px;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, hsl(var(--primary)) 0%, hsl(217, 91%, 60%) 100%);
  border-radius: 3px;
}

/* Theme settings card enhancement */
.theme-settings-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
}

.theme-settings-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, hsl(var(--primary)) 0%, hsl(217, 91%, 60%) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.theme-settings-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.theme-settings-card:hover::before {
  opacity: 1;
}

/* Theme preview cards */
.theme-preview-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.dark .theme-preview-card {
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.theme-preview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

/* Current theme indicator */
.current-theme-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  padding: 4px 8px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background-color: hsl(var(--primary) / 0.1);
  color: hsl(var(--primary));
  border-radius: 4px;
  z-index: 10;
}

/* Apply button enhancement */
.theme-apply-button {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.theme-apply-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
}

.theme-apply-button:hover::before {
  animation: button-shine 1.5s;
}

.theme-current-button {
  background-color: #22c55e !important;
}

.theme-current-button:hover {
  background-color: #16a34a !important;
}

/* Tabs enhancement */
.theme-mode-tabs {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.02);
}

.dark .theme-mode-tabs {
  background-color: rgba(255, 255, 255, 0.02);
}

.theme-mode-tab[data-state="active"] {
  background-color: hsl(var(--primary) / 0.1);
  position: relative;
}

/* Animations */
@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(2deg); }
}

@keyframes button-shine {
  100% {
    transform: translateX(100%);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .theme-page-container::before,
  .theme-page-container::after {
    width: 200px;
    height: 200px;
  }
} 