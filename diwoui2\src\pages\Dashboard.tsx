import { GreetingSection } from "@/components/dashboard/greeting-section";
import { MetricsSection } from "@/components/dashboard/metrics-section";
import { SuggestionsSection } from "@/components/dashboard/suggestions-section";
import { useBrandTheme } from "@/themes/brand-theme-provider";
import { useEffect } from "react";

export default function Dashboard() {
  const { currentTheme, refreshTheme } = useBrandTheme();
  
  // Ensure theme is refreshed when dashboard mounts
  useEffect(() => {
    // Small delay to ensure theme is applied after navigation
    const timeoutId = setTimeout(() => {
      refreshTheme();
      console.log("[Dashboard] Theme refreshed:", currentTheme?.name);
    }, 50);
    
    return () => clearTimeout(timeoutId);
  }, [refreshTheme, currentTheme]);

  return (
    <div className="container max-w-7xl py-6">
      {currentTheme && (
        <div className="hidden">
          {/* This div forces theme variables to be used, even if not visible */}
          <div style={{ 
            color: `hsl(var(--primary))`,
            backgroundColor: `hsl(var(--background))`,
            borderColor: `hsl(var(--border))`
          }}></div>
        </div>
      )}
      <GreetingSection />
      <MetricsSection />
      <SuggestionsSection />
    </div>
  );
}
