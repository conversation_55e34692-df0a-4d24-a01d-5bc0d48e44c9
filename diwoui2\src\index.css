@import './styles/animations.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 98%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 0 70% 50.6%; /* Darker Red accent color #D32F2F */
    --primary-foreground: 0 0% 98%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 70% 50.6%; /* Match the darker red primary */
    --destructive-foreground: 0 0% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 0 70% 50.6%; /* Darker Red accent for sidebar */
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 240 5.9% 10%;

    /* Animation durations */
    --animation-fast: 150ms;
    --animation-normal: 250ms;
    --animation-slow: 350ms;
    
    /* Custom card hover colors */
    --card-hover-bg: 0 100% 98%;
    /* Integrated scrollbar colors - light theme */
    --scrollbar-track: 0 0% 98%; /* Match background */
    --scrollbar-thumb: 0 0% 85%; /* Subtle light gray */
    --scrollbar-thumb-hover: 0 0% 75%; /* Slightly darker on hover */
  }

  .dark {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 13%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 0 70% 50.6%; /* Keep the darker red for dark mode */
    --primary-foreground: 210 40% 98%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    
    --sidebar-background: 222 47% 10%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 0 70% 50.6%; /* Darker Red accent for sidebar in dark mode */
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 212.7 26.8% 83.9%;
    
    /* Enhanced text contrast for dark buttons */
    --outline-button-text: 0 0% 100%; /* Pure white text for outline buttons */
    --outline-button-border: 210 20% 35%; /* More visible border in dark mode */
    
    /* Custom card colors for dark mode */
    --card: 216 33% 23%; /* #252d3d */
    --card-hover-bg: 216 33% 28%; /* Slightly lighter for hover */
    /* Integrated scrollbar colors - dark theme */
    --scrollbar-track: 222 47% 11%; /* Match background */
    --scrollbar-thumb: 222 47% 20%; /* Subtle dark gray */
    --scrollbar-thumb-hover: 222 47% 25%; /* Slightly lighter on hover */    
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Improve focus states for accessibility */
  :focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }
  
  /* Custom scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background-color: hsl(var(--scrollbar-track));
    border-radius: 10px;
  }
  
  ::-webkit-scrollbar-thumb {
    background-color: hsl(var(--scrollbar-thumb));
    border-radius: 10px;
    transition: background-color 0.2s ease;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--scrollbar-thumb-hover));
  }
  
  /* Firefox scrollbar styling */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--scrollbar-thumb)) hsl(var(--scrollbar-track));
  }
}

/* Animation utility classes */
@layer utilities {
  .transition-standard {
    @apply transition-all duration-normal ease-in-out;
  }

  .transition-fast {
    @apply transition-all duration-fast ease-in-out;
  }

  .transition-slow {
    @apply transition-all duration-slow ease-in-out;
  }
  
  /* Define custom rounded-inherit utility class */
  .rounded-inherit {
    border-radius: inherit;
  }
  
  /* Custom gradient for the user's name */
  .name-gradient {
    @apply bg-gradient-to-r from-red-500 via-purple-500 to-blue-500 bg-clip-text text-transparent;
  }
  
  /* Scrollbar styling */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-hidden {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hidden::-webkit-scrollbar {
    display: none;
  }
  
  /* Card hover effects for dashboard */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-md hover:-translate-y-1;
  }
}

/* Custom card styles for alert and metric cards */
@layer components {
  /* Common card hover styles - removed transition for metric-card */
  .alert-card {
    @apply transition-all duration-300;
  }
  
  .metric-card {
    /* Removed hover transition effects */
  }
  
  /* Light mode hover styles - removed for metric-card */
  .light .alert-card:hover {
    @apply bg-gradient-to-br from-white to-red-50 shadow-md -translate-y-1;
  }
  
  /* Dark mode styles with specific background color */
  .dark .alert-card,
  .dark .metric-card {
    background-color: #252d3d;
  }
  
  /* Dark mode hover styles - removed for metric-card */
  .dark .alert-card:hover {
    @apply bg-gradient-to-br from-[#252d3d] to-[#303b52] shadow-md -translate-y-1;
  }

  /* Button visibility fixes for different theme combinations */
  /* For Dark color theme + Light Mode */
  .dark [data-theme="light"] .btn-ghost,
  .dark [data-theme="light"] [variant="ghost"] {
    color: white !important;
  }

  /* For Light color theme + Dark Mode */
  [data-theme="dark"] .btn-ghost,
  [data-theme="dark"] [variant="ghost"],
  [data-theme="dark"] .btn-outline,
  [data-theme="dark"] [variant="outline"] {
    color: white !important;
  }

  /* Force outline button text to be visible in all combinations */
  .btn-outline, 
  [variant="outline"] {
    color: black;
  }
  .dark .btn-outline,
  .dark [variant="outline"] {
    color: white;
  }

  /* Force ghost button text to be visible in all combinations */
  .btn-ghost,
  [variant="ghost"] {
    color: black;
  }
  .dark .btn-ghost,
  .dark [variant="ghost"] {
    color: white;
  }
  
  /* Force visibility class for critical buttons */
  .force-visible {
    @apply text-black dark:text-white;
  }
  
  /* Handle specific theme combinations */
  .dark [data-theme="light"] .force-visible {
    @apply text-black;
  }
  
  [data-theme="dark"] .force-visible {
    @apply text-white;
  }
  
  /* Add contrast background for better visibility in mixed themes */
  .dark [data-theme="light"] .btn-outline.force-visible {
    @apply bg-gray-200 border-gray-600 text-black;
  }
  
  .dark [data-theme="light"] .btn-ghost.force-visible {
    @apply text-black hover:bg-gray-200;
  }
  
  [data-theme="dark"] .btn-outline.force-visible {
    @apply bg-gray-700 border-gray-400 text-white;
  }
  
  [data-theme="dark"] .btn-ghost.force-visible {
    @apply text-white hover:bg-gray-700;
  }
  
  /* Special fix for ghost button in all combinations */
  .ghost-button-fix {
    color: inherit !important;
    font-weight: 500 !important;
  }
  
  /* Override for specific theme combinations */
  .dark .ghost-button-fix {
    color: white !important;
  }
  
  .light .ghost-button-fix {
    color: black !important;
  }
  
  /* Target with data-mode for more specific control */
  [data-mode="light"] .ghost-button-fix {
    color: black !important;
  }
  
  [data-mode="dark"] .ghost-button-fix {
    color: white !important;
  }
  
  /* Handle theme + mode combinations */
  .dark [data-mode="light"] .ghost-button-fix {
    color: black !important;
  }
  
  .light [data-mode="dark"] .ghost-button-fix {
    color: white !important;
  }
}

/* Theme-specific overrides */
.theme-green .bg-red-600 {
  background-color: var(--theme-primary, #22c55e) !important;
}

.theme-green .hover\:bg-red-700:hover {
  background-color: var(--theme-primary-dark, #16a34a) !important;
}

.theme-green .text-red-500 {
  color: var(--theme-primary, #22c55e) !important;
}

.theme-green .bg-red-50 {
  background-color: var(--override-red-50, #f0fdf4) !important;
}

.theme-green .name-gradient {
  background-image: linear-gradient(to right, #22c55e, #4ade80, #86efac) !important;
  background-clip: text !important;
  color: transparent !important;
}

.theme-green .from-red-500 {
  --tw-gradient-from: var(--theme-primary, #22c55e) !important;
}

.theme-green .pulse-animation {
  --pulse-color: rgba(34, 197, 94, 0.4);
}

/* Add additional theme overrides for other themes as needed */
.theme-purple .bg-red-600,
.theme-purple .bg-primary {
  background-color: var(--theme-primary, #9333ea) !important;
}

.theme-purple .hover\:bg-red-700:hover,
.theme-purple .hover\:bg-primary:hover,
.theme-purple .hover\:bg-primary\/90:hover {
  background-color: var(--theme-primary-dark, #7e22ce) !important;
}

.theme-purple .text-red-500,
.theme-purple .text-primary {
  color: var(--theme-primary, #9333ea) !important;
}

.theme-purple .bg-red-50,
.theme-purple .bg-primary\/10 {
  background-color: var(--override-red-50, #faf5ff) !important;
}

.theme-purple .border-l-primary,
.theme-purple .border-l-red-600 {
  border-left-color: var(--theme-primary, #9333ea) !important;
}

.theme-purple .name-gradient {
  background-image: linear-gradient(to right, #9333ea, #a855f7, #c084fc) !important;
  background-clip: text !important;
  color: transparent !important;
}

.theme-purple .from-red-500,
.theme-purple .from-primary {
  --tw-gradient-from: var(--theme-primary, #9333ea) !important;
}

.theme-purple .pulse-animation {
  --pulse-color: rgba(147, 51, 234, 0.4);
}

/* Ensure theme variables are used wherever possible */
[data-theme="green"] .btn-primary,
[data-theme="green"] .bg-primary {
  background-color: var(--theme-primary, #22c55e) !important;
}

[data-theme="green"] .text-primary {
  color: var(--theme-primary, #22c55e) !important;
}

/* Themes for hover states in sidebar */
.theme-purple .hover\:bg-gray-100:hover,
.theme-purple .hover\:bg-red-50:hover {
  background-color: rgba(147, 51, 234, 0.1) !important; /* Purple-based hover */
}

.theme-purple .dark .hover\:bg-gray-800:hover,
.theme-purple.dark .hover\:bg-gray-800:hover,
.theme-purple .dark .hover\:bg-red-900\/20:hover {
  background-color: rgba(147, 51, 234, 0.2) !important; /* Dark mode purple hover */
}

.theme-green .hover\:bg-gray-100:hover,
.theme-green .hover\:bg-red-50:hover {
  background-color: rgba(34, 197, 94, 0.1) !important; /* Green-based hover */
}

.theme-green .dark .hover\:bg-gray-800:hover,
.theme-green.dark .hover\:bg-gray-800:hover,
.theme-green .dark .hover\:bg-red-900\/20:hover {
  background-color: rgba(34, 197, 94, 0.2) !important; /* Dark mode green hover */
}

/* Ensure theme variables are used wherever possible */
[data-theme="green"] .btn-primary,
[data-theme="green"] .bg-primary {
  background-color: var(--theme-primary, #22c55e) !important;
}

[data-theme="green"] .text-primary {
  color: var(--theme-primary, #22c55e) !important;
}

/* Default theme overrides for red */
.theme-default .bg-red-600 {
  background-color: var(--theme-primary, #D32F2F) !important;
}

.theme-default .hover\:bg-red-700:hover {
  background-color: var(--theme-primary-dark, #B71C1C) !important;
}

.theme-default .text-red-500 {
  color: var(--theme-primary, #D32F2F) !important;
}

.theme-default .bg-red-50 {
  background-color: var(--override-red-50, #FFEBEE) !important;
}

.theme-default .name-gradient {
  background-image: linear-gradient(to right, #D32F2F, #F44336, #FFCDD2) !important;
  background-clip: text !important;
  color: transparent !important;
}

.theme-default .from-red-500 {
  --tw-gradient-from: var(--theme-primary, #D32F2F) !important;
}

.theme-default .pulse-animation {
  --pulse-color: rgba(211, 47, 47, 0.4);
}

/* Themes for hover states in sidebar */
.theme-default .hover\:bg-gray-100:hover {
  background-color: rgba(211, 47, 47, 0.1) !important; /* Darker Red-based hover */
}

.theme-default .dark .hover\:bg-gray-800:hover,
.theme-default.dark .hover\:bg-gray-800:hover {
  background-color: rgba(211, 47, 47, 0.2) !important; /* Dark mode darker red hover */
}

/* Google theme overrides */
.theme-google .bg-red-600,
.theme-google .bg-primary {
  background-color: var(--theme-primary, #4285F4) !important;
}

.theme-google .hover\:bg-red-700:hover,
.theme-google .hover\:bg-primary:hover,
.theme-google .hover\:bg-primary\/90:hover {
  background-color: var(--theme-primary-dark, #1A73E8) !important;
}

.theme-google .text-red-500,
.theme-google .text-primary {
  color: var(--theme-primary, #4285F4) !important;
}

.theme-google .bg-red-50,
.theme-google .bg-primary\/10 {
  background-color: var(--override-red-50, #E8F0FE) !important;
}

.theme-google .border-l-primary,
.theme-google .border-l-red-600 {
  border-left-color: var(--theme-primary, #4285F4) !important;
}

.theme-google .name-gradient {
  background-image: linear-gradient(to right, #4285F4, #1A73E8, #174EA6) !important;
  background-clip: text !important;
  color: transparent !important;
}

.theme-google .from-red-500,
.theme-google .from-primary {
  --tw-gradient-from: var(--theme-primary, #4285F4) !important;
}

.theme-google .pulse-animation {
  --pulse-color: rgba(66, 133, 244, 0.4);
}

/* Google theme hover states */
.theme-google .hover\:bg-gray-100:hover,
.theme-google .hover\:bg-red-50:hover {
  background-color: rgba(66, 133, 244, 0.1) !important;
}

.theme-google .dark .hover\:bg-gray-800:hover,
.theme-google.dark .hover\:bg-gray-800:hover,
.theme-google .dark .hover\:bg-red-900\/20:hover {
  background-color: rgba(66, 133, 244, 0.2) !important;
}

