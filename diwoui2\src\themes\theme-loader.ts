import { Theme } from './types';

// Define default themes as a configurable constant that can be extended
export const DEFAULT_THEMES = ['default', 'green', 'purple', 'google'];

/**
 * Loads a theme configuration by its ID
 * @param themeId The ID of the theme to load
 * @returns Promise that resolves to the theme configuration
 */
export async function loadTheme(themeId: string): Promise<Theme> {
  try {
    const themeModule = await import(`./json/${themeId}.json`);
    return themeModule as Theme;
  } catch (error) {
    console.error(`Failed to load theme ${themeId}:`, error);
    // Fall back to default theme if requested theme doesn't exist
    const defaultModule = await import('./json/default.json');
    return defaultModule as Theme;
  }
}

/**
 * Loads all available theme configurations
 * @returns Promise that resolves to an array of theme configurations
 */
export async function loadAllThemes(): Promise<Theme[]> {
  try {
    const themePromises = DEFAULT_THEMES.map(loadTheme);
    
    // Add custom themes from localStorage if any
    const customThemeIds = getCustomThemeIds();
    if (customThemeIds.length > 0) {
      const customThemePromises = customThemeIds.map(loadTheme);
      return [...await Promise.all(themePromises), ...await Promise.all(customThemePromises)];
    }
    
    return await Promise.all(themePromises);
  } catch (error) {
    console.error('Failed to load themes:', error);
    // Fall back to default theme if all else fails
    const defaultTheme = await loadTheme('default');
    return [defaultTheme];
  }
}

/**
 * Registers a new custom theme in the application
 * @param theme The theme configuration to register
 * @returns Promise that resolves when the theme is registered
 */
export function registerCustomTheme(theme: Theme): void {
  try {
    // Store in localStorage to persist across sessions
    const customThemes = getCustomThemes();
    customThemes[theme.id] = theme;
    localStorage.setItem('catalyst-custom-themes', JSON.stringify(customThemes));
  } catch (error) {
    console.error('Failed to register custom theme:', error);
  }
}

/**
 * Gets all custom theme IDs from localStorage
 * @returns Array of custom theme IDs
 */
export function getCustomThemeIds(): string[] {
  try {
    const customThemes = getCustomThemes();
    return Object.keys(customThemes);
  } catch (error) {
    console.error('Failed to get custom theme IDs:', error);
    return [];
  }
}

/**
 * Gets all custom themes from localStorage
 * @returns Object containing custom themes
 */
export function getCustomThemes(): Record<string, Theme> {
  try {
    const themes = localStorage.getItem('catalyst-custom-themes');
    return themes ? JSON.parse(themes) : {};
  } catch (error) {
    console.error('Failed to get custom themes:', error);
    return {};
  }
}

/**
 * Loads a custom theme from localStorage
 * @param themeId The ID of the custom theme to load
 * @returns The custom theme configuration or null if not found
 */
export function loadCustomTheme(themeId: string): Theme | null {
  try {
    const customThemes = getCustomThemes();
    return customThemes[themeId] || null;
  } catch (error) {
    console.error(`Failed to load custom theme ${themeId}:`, error);
    return null;
  }
}
