import React, { useEffect, useState } from 'react';
import { readExcelData } from '@/services/excelData';
import { KPICards } from '@/components/KPICards';
import { DataTable } from '@/components/DataTable';

interface ExcelData {
  userData: any[];
  actionData: any[];
  stats: {
    totalUsers: number;
    activeUsers: number;
    inactiveUsers: number;
    totalReports: number;
    reportsRunLastYear: number;
    reportsNeverRunLastTwoYears: number;
  };
}

// KPI filter types
type KPIFilter = 'all' | 'totalReports' | 'reportsRunLastYear' | 'reportsNeverRunLastTwoYears';

const Index = () => {
  const [excelData, setExcelData] = useState<ExcelData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeKPIFilter, setActiveKPIFilter] = useState<KPIFilter>('all');
  const [tableFilteredData, setTableFilteredData] = useState<any[]>([]);

  useEffect(() => {
    const loadData = async () => {
      try {
        const data = await readExcelData();
        setExcelData(data);
        setTableFilteredData(data.actionData);
      } catch (error) {
        console.error('Error loading Excel data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Handle KPI filter changes
  const handleKPIFilterChange = (filter: KPIFilter) => {
    setActiveKPIFilter(prevFilter => prevFilter === filter ? 'all' : filter);
  };

  // Filter action data based on active KPI
  const filteredActionData = React.useMemo(() => {
    if (!excelData || activeKPIFilter === 'all') {
      return excelData?.actionData;
    }

    const now = new Date();
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(now.getFullYear() - 1);
    const twoYearsAgo = new Date();
    twoYearsAgo.setFullYear(now.getFullYear() - 2);

    // Get only reports
    const reports = excelData.actionData.filter(item => 
      item['Object Type'] && item['Object Type'].toLowerCase().includes('report')
    );

    switch (activeKPIFilter) {
      case 'totalReports':
        return reports;
      
      case 'reportsRunLastYear':
        return reports.filter(report => {
          if (!report['Last Action Timestamp (UTC)']) return false;
          
          try {
            const lastAction = new Date(report['Last Action Timestamp (UTC)']);
            return lastAction >= oneYearAgo && lastAction <= now;
          } catch (error) {
            return false;
          }
        });
      
      case 'reportsNeverRunLastTwoYears':
        return reports.filter(report => {
          if (!report['Last Action Timestamp (UTC)']) return true; // No timestamp means never run
          
          try {
            const lastAction = new Date(report['Last Action Timestamp (UTC)']);
            return lastAction < twoYearsAgo;
          } catch (error) {
            return true; // In case of error parsing the date, count as never run
          }
        });
      
      default:
        return excelData.actionData;
    }
  }, [excelData, activeKPIFilter]);

  // Calculate dynamic KPI values based on table filtered data
  const dynamicKPIValues = React.useMemo(() => {
    if (!excelData) {
      return {
        totalUsers: 0,
        activeUsers: 0,
        inactiveUsers: 0,
        totalReports: 0,
        reportsRunLastYear: 0,
        reportsNeverRunLastTwoYears: 0
      };
    }

    // Even if tableFilteredData is empty, we'll calculate values based on it
    const now = new Date();
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(now.getFullYear() - 1);
    const twoYearsAgo = new Date();
    twoYearsAgo.setFullYear(now.getFullYear() - 2);

    // Filter reports from filtered data
    const filteredReports = tableFilteredData.filter(item => 
      item['Object Type'] && item['Object Type'].toLowerCase().includes('report')
    );
    
    // Calculate report statistics based on filtered data
    const totalReports = filteredReports.length;
    
    // Reports run in the last 12 months
    const reportsRunLastYear = filteredReports.filter(report => {
      if (!report['Last Action Timestamp (UTC)']) return false;
      
      try {
        const lastAction = new Date(report['Last Action Timestamp (UTC)']);
        return lastAction >= oneYearAgo && lastAction <= now;
      } catch (error) {
        return false;
      }
    }).length;
    
    // Reports never run in the last 2 years
    const reportsNeverRunLastTwoYears = filteredReports.filter(report => {
      if (!report['Last Action Timestamp (UTC)']) return true; // No timestamp means never run
      
      try {
        const lastAction = new Date(report['Last Action Timestamp (UTC)']);
        return lastAction < twoYearsAgo;
      } catch (error) {
        return true; // In case of error parsing the date, count as never run
      }
    }).length;

    return {
      // Keep original user stats
      totalUsers: excelData.stats.totalUsers,
      activeUsers: excelData.stats.activeUsers,
      inactiveUsers: excelData.stats.inactiveUsers,
      // Dynamic report stats based on filtered data
      totalReports,
      reportsRunLastYear,
      reportsNeverRunLastTwoYears
    };
  }, [tableFilteredData, excelData]);

  // Handle filtered data from the DataTable
  const handleFilteredDataChange = (filteredData: any[]) => {
    setTableFilteredData(filteredData);
  };

  if (isLoading) {
    return (
      <div className="container max-w-7xl mx-auto p-8">
        <div className="flex items-center justify-center h-64">
          <p className="text-lg text-muted-foreground">Loading data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-7xl mx-auto p-8">
      <div className="space-y-8">
        {/* KPIs */}
        {excelData && (
          <KPICards
            totalUsers={dynamicKPIValues.totalUsers}
            activeUsers={dynamicKPIValues.activeUsers}
            inactiveUsers={dynamicKPIValues.inactiveUsers}
            totalReports={dynamicKPIValues.totalReports}
            reportsRunLastYear={dynamicKPIValues.reportsRunLastYear}
            reportsNeverRunLastTwoYears={dynamicKPIValues.reportsNeverRunLastTwoYears}
            activeKPIFilter={activeKPIFilter}
            onKPIFilterChange={handleKPIFilterChange}
          />
        )}

        {/* Data Table */}
        {excelData && (
          <div className="space-y-4">
            <h2 className="text-2xl font-semibold tracking-tight">
              {activeKPIFilter === 'all' ? 'Object Actions' : 'Filtered Reports'}
              {activeKPIFilter !== 'all' && (
                <span className="ml-2 text-sm text-muted-foreground font-normal">
                  {filteredActionData?.length || 0} records
                </span>
              )}
            </h2>
            <DataTable 
              data={filteredActionData || []} 
              onFilteredDataChange={handleFilteredDataChange}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default Index;
