from fastapi import APIRouter, HTTPException, Request, Body
import logging
import traceback
import os
import openai
from pydantic import BaseModel, <PERSON>
from typing import Optional

# Configure logging
logger = logging.getLogger("bi_accelerator")

# Initialize router
router = APIRouter(prefix="/api", tags=["sql_analysis"])

# Define the SQL JSON request model
class SQLRequest(BaseModel):
    sql: str = Field(..., description="The SQL query to analyze", min_length=1)
    
    class Config:
        json_schema_extra = {
            "example": {
                "sql": """select a14.[SUBCAT_ID] AS SUBCAT_ID,
                         a13.[MONTH_ID] AS MONTH_ID,
                         sum((a11.[QTY_SOLD] * (a11.[UNIT_PRICE] - a11.[DISCOUNT]))) AS Revenue
                         from [ORDER_DETAIL] a11
                         join [LU_EMPLOYEE] a12 on a11.[EMP_ID] = a12.[EMP_ID]""",
            }
        }

@router.post("/analyze-cube-sql")
async def analyze_cube_sql(sql_request: SQLRequest):
    """
    Analyze cube SQL and return analysis results.
    """
    try:
        # Extract SQL from the request
        cube_sql = sql_request.sql
        
        # Prepare the prompt for the LLM
        prompt = f"""The above is the cube SQL:

```sql
{cube_sql}
```

Now from the above cube SQL I need to fetch the below:
1. main tables
2. relationships
3. model structure

Format the response in a structured way, with clear sections for tables, relationships, and PowerBI implementation recommendations.
"""
        
        # Call the OpenAI API
        completion = openai.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a helpful assistant specialized in database and BI analytics."},
                {"role": "user", "content": prompt}
            ]
        )
        
        # Extract the analysis result
        analysis_result = completion.choices[0].message.content
        
        logger.info("Successfully analyzed cube SQL")
        return {"analysis": analysis_result}
    
    except Exception as e:
        logger.error(f"Error analyzing cube SQL: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to analyze SQL: {str(e)}")

@router.post("/analyze-sql")
async def analyze_sql(sql_request: SQLRequest = Body(...)):
    """
    Analyze SQL provided in JSON format and return structured relationship data.
    Temporary tables are processed to establish relationships between main tables but are not included in output.
    
    The SQL query should be provided in the request body as a JSON object:
    ```json
    {
        "sql": "your SQL query here"
    }
    ```
    
    Returns structured JSON data for visualizing table relationships or tabular format.
    """
    try:
        # Log the received SQL for debugging
        logger.debug(f"Received SQL query for analysis: {sql_request.sql[:100]}...")
        
        # Extract SQL from the JSON request
        sql = sql_request.sql
        
        # Prepare the prompt for the LLM with structured output instructions
        prompt = f"""Analyze the following SQL schema:

        ```sql
        {sql}
        ```
        Extract the following information in JSON format:
        1. All main tables (PERMANENT TABLES ONLY - completely exclude any temporary tables from your analysis)
        2. Fields for each main table (name and data type)
        3. Direct relationships between main tables only
        4. Data model structure (fact tables, dimension tables, hierarchies)

        IMPORTANT: Focus EXCLUSIVELY on permanent tables and direct relationships between them. 
        DO NOT include any temporary tables (tables with names containing 'ZZ', 'tmp', 'interim', 'staging', or similar indicators).
        DO NOT derive relationships through temporary tables.
        DO NOT mention temporary tables in any part of your response.

        Return ONLY a JSON object with this exact structure:
        {{
          "main_tables": [
            {{
              "id": "table_name",
              "name": "Table Name",
              "type": "fact/dimension/lookup/other",
              "fields": [
                {{"name": "field_name", "type": "data_type", "description": "field description"}}
              ]
            }}
          ],
          "relationships": [
            {{
              "source": "source_table_id",
              "target": "target_table_id",
              "relationship": "one-to-many",
              "sourceField": "source_field",
              "targetField": "target_field",
              "description": "Describes the relationship"
            }}
          ],
          "model_structure": {{
            "fact_tables": ["table_id1", "table_id2"],
            "dimension_tables": ["table_id3", "table_id4"],
            "hierarchies": [
              {{
                "name": "hierarchy_name",
                "levels": ["dimension1", "dimension2", "dimension3"],
                "description": "Describes the hierarchy"
              }}
            ],
            "model_type": "star/snowflake/galaxy/other",
            "description": "Overall description of the data model"
          }}
        }}
        
        Critical instructions:
        1. PERMANENT TABLES ONLY - completely exclude any temporary tables from your analysis
        2. Include ONLY direct relationships between permanent tables
        3. If a table name contains 'temp', 'tmp', 'interim', 'staging', or similar indicators, treat it as temporary and exclude it
        4. Determine the correct relationship direction (source/target) based on the SQL query logic
        5. Do not reference temporary tables in any part of your response

        Do not include any explanations, markdown, or text outside of the JSON object.
        """
        
        # Call the OpenAI API with JSON response format
        completion = openai.chat.completions.create(
            model="o3-mini",
            messages=[
                {"role": "system", "content": "You are a database schema analyzer with expertise in SQL and data modeling. Return only valid JSON conforming to the requested structure. Focus only on permanent tables and direct relationships between them. Do not analyze temporary tables."},
                {"role": "user", "content": prompt}
            ],
            response_format={"type": "json_object"}
        )
        
        # Extract the analysis result as JSON
        analysis_json = completion.choices[0].message.content
        
        # Parse the JSON string to ensure it's valid
        import json
        structured_data = json.loads(analysis_json)

        # Log success before returning
        logger.info("Successfully analyzed SQL and returned structured data with only main tables and direct relationships")
        
        # Return the structured data directly
        return structured_data
    except ValueError as ve:
        logger.error(f"Invalid SQL request format: {str(ve)}")
        raise HTTPException(status_code=422, detail=f"Invalid SQL request format: {str(ve)}")
    except Exception as e:
        logger.error(f"Error analyzing SQL: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to analyze SQL: {str(e)}")