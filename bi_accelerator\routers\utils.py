import os
import json
import traceback
import logging
from fastapi import HTTPException
from mstrio.connection import Connection
from mstrio.helpers import IServerError

logger = logging.getLogger("bi_accelerator")

# File to store credentials
CREDENTIALS_FILE = "credentials.json"

# Helper function to get credentials
def get_credentials(environment_name=None):
    try:
        if os.path.exists(CREDENTIALS_FILE):
            with open(CREDENTIALS_FILE, "r") as f:
                data = json.load(f)
                
                # Handle both old format (single environment) and new format (multiple environments)
                if "environments" in data:
                    # If environment name is provided, use that one
                    if environment_name:
                        for env in data["environments"]:
                            if env["name"] == environment_name:
                                return env["credentials"]
                        logger.warning(f"Environment '{environment_name}' not found, falling back to active environment")
                    
                    # Otherwise use the active environment
                    active_env = data.get("active_environment")
                    if active_env:
                        for env in data["environments"]:
                            if env["name"] == active_env:
                                return env["credentials"]
                    
                    # If no active environment, try to use the default environment
                    for env in data["environments"]:
                        if env.get("is_default"):
                            return env["credentials"]
                    
                    # If no default, use the first environment if any exist
                    if data["environments"]:
                        return data["environments"][0]["credentials"]
                else:
                    # Old format - single environment
                    return data
        return None
    except Exception as e:
        logger.error(f"Failed to load credentials: {str(e)}")
        return None

# Helper function to get available environments
def get_available_environments():
    try:
        if os.path.exists(CREDENTIALS_FILE):
            with open(CREDENTIALS_FILE, "r") as f:
                data = json.load(f)
                
                # Handle both old format (single environment) and new format (multiple environments)
                if "environments" in data:
                    return {
                        "environments": [env["name"] for env in data["environments"]],
                        "active_environment": data.get("active_environment")
                    }
                else:
                    # Old format - single environment
                    return {
                        "environments": ["default"],
                        "active_environment": "default"
                    }
        return {"environments": [], "active_environment": None}
    except Exception as e:
        logger.error(f"Failed to load environments: {str(e)}")
        return {"environments": [], "active_environment": None}

# Helper function to establish connection
def get_connection(project_id=None, environment_name=None):
    logger.debug(f"Establishing connection to MicroStrategy with project_id={project_id}, environment={environment_name}")
    
    # Trim any whitespace from project_id if it exists
    if project_id:
        project_id = project_id.strip()
    
    try:
        # Get credentials from file for the specified environment or active environment
        credentials = get_credentials(environment_name)
        
        if credentials:
            URL = credentials.get("url")
            USER = credentials.get("username")
            PASSWORD = credentials.get("password")
            
            # Log which environment we're using (but don't expose sensitive details)
            env_info = f"environment '{environment_name}'" if environment_name else "active environment"
            logger.info(f"Using credentials from {env_info}")
        else:
            # Fallback to environment variables if no credentials file
            logger.warning("No credentials found, falling back to environment variables")
            URL = os.getenv('URL')
            USER = os.getenv('USER')
            PASSWORD = os.getenv('PASSWORD') or os.getenv('MSTR_PASSWORD') or ''
        
        # Check if we have a password
        if not PASSWORD:
            logger.warning("No password provided in credentials or environment variables")
            
        # Standard authentication only (mode 1)
        logger.info(f"Attempting connection with Standard authentication")
        
        conn = Connection(
            base_url=URL,
            username=USER,
            password=PASSWORD,
            project_id=project_id,
            login_mode=1
        )
        
        conn.connect()
        logger.info(f"Successfully connected to MicroStrategy{' with project ID: ' + project_id if project_id else ''}")
        return conn
            
    except IServerError as e:
        error_msg = f"Authentication failed: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        
        if 'ERR003' in str(e) or 'Login failure' in str(e):
            raise HTTPException(
                status_code=401,
                detail=(
                    "Authentication failed. Please check your credentials and try again."
                )
            )
        
        raise HTTPException(
            status_code=500,
            detail=f"Failed to connect to MicroStrategy: {str(e)}"
        )
        
    except Exception as e:
        logger.error(f"Failed to connect to MicroStrategy: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"Failed to connect to MicroStrategy: {str(e)}"
        ) 