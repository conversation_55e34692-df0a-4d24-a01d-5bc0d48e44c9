import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import TitleBar from './TitleBar';
import { cn } from '@/lib/utils';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const location = useLocation();
  
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className="flex h-screen overflow-hidden bg-background">
      <Sidebar isOpen={sidebarOpen} toggleSidebar={toggleSidebar} />
      <div className="flex flex-col flex-1 w-0 overflow-hidden">
        <main 
          className={cn(
            "flex-1 relative overflow-y-auto overflow-x-hidden focus:outline-none",
            "transition-all duration-300 ease-apple pb-8",
            sidebarOpen ? "md:pl-64" : "md:pl-20"
          )}
        >
          <TitleBar />
          <div className={cn(
            "py-6 mx-auto animate-fade-in",
            "px-8"
          )}>
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;
