import * as XLSX from 'xlsx';

interface UserData {
  'S.no': number;
  'Intelligence Server Machine': string;
  'User': string;
  'Project': string;
  'Account Status': string;
  'Last Connect Timestamp (UTC)': string;
}

interface ActionData {
  'Project': string;
  'Object': string;
  'GUID': string;
  'Creation Time': string;
  'Modification Time': string;
  'Object Type': string;
  'Object Owner': string;
  'Last Action Timestamp (UTC)': string;
  'No of Action': number;
}

export async function readExcelData() {
  try {
    const response = await fetch('public/server_overview.xlsx');
    const arrayBuffer = await response.arrayBuffer();
    
    // Configure XLSX to parse dates properly
    const workbook = XLSX.read(arrayBuffer, {
      cellDates: true,  // Parse dates as Date objects
      dateNF: 'yyyy-mm-dd'  // Date number format
    });

    // Read Sheet1 (User Info)
    const userSheet = workbook.Sheets['Sheet1'];
    const userData: UserData[] = XLSX.utils.sheet_to_json(userSheet);

    // Read Sheet2 (Action Info)
    const actionSheet = workbook.Sheets['Sheet2'];
    const actionData: ActionData[] = XLSX.utils.sheet_to_json(actionSheet);

    // Calculate user statistics
    const now = new Date();
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(now.getFullYear() - 1);
    const twoYearsAgo = new Date();
    twoYearsAgo.setFullYear(now.getFullYear() - 2);

    const totalUsers = userData.length;
    const activeUsers = userData.filter(user => user['Account Status'] === 'Enabled').length;
    const inactiveUsers = userData.filter(user => user['Account Status'] === 'Disabled').length;

    // Filter reports from actionData (assuming 'Report' is one of the Object Types)
    const reports = actionData.filter(item => 
      item['Object Type'] && item['Object Type'].toLowerCase().includes('report')
    );
    
    // Calculate report statistics
    const totalReports = reports.length;
    
    // Reports run in the last 12 months
    const reportsRunLastYear = reports.filter(report => {
      if (!report['Last Action Timestamp (UTC)']) return false;
      
      try {
        const lastAction = new Date(report['Last Action Timestamp (UTC)']);
        return lastAction >= oneYearAgo && lastAction <= now;
      } catch (error) {
        return false;
      }
    }).length;
    
    // Reports never run in the last 2 years
    const reportsNeverRunLastTwoYears = reports.filter(report => {
      if (!report['Last Action Timestamp (UTC)']) return true; // No timestamp means never run
      
      try {
        const lastAction = new Date(report['Last Action Timestamp (UTC)']);
        return lastAction < twoYearsAgo;
      } catch (error) {
        return true; // In case of error parsing the date, count as never run
      }
    }).length;

    return {
      userData,
      actionData,
      stats: {
        totalUsers,
        activeUsers,
        inactiveUsers,
        totalReports,
        reportsRunLastYear,
        reportsNeverRunLastTwoYears
      }
    };
  } catch (error) {
    console.error('Error reading Excel file:', error);
    throw error;
  }
} 