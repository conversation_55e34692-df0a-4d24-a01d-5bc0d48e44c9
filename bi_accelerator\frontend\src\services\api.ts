export interface Project {
  id: string;
  name: string;
  has_access: boolean;
}

export interface Report {
  id: string;
  name: string;
  type: string;
}

export interface Cube {
  id: string;
  name: string;
  type: string;
}

export interface Dashboard {
  id: string;
  name: string;
}

export interface CubeDetail extends Cube {
  properties?: {
    description?: string;
    date_created?: string;
    date_modified?: string;
    owner?: string;
    version?: string;
    ancestors?: Array<{id: string; name: string; level: number}>;
  };
  attributes?: Array<{id: string; name: string}>;
  metrics?: Array<{id: string; name: string}>;
}

export interface ReportDetail {
  id: string;
  name: string;
  type?: string;
  description?: string;
  dimensions?: string;
  template?: string;
  date_created?: string;
  date_modified?: string;
  owner?: string | { name: string };
  version?: string;
  ext_type?: string;
  subtype?: string;
  ancestors?: Array<{ id: string; name: string }>;
  attributes?: Array<{ 
    id: string; 
    name: string;
    mapped_name?: string;
    power_bi_field?: string;
    data_type?: string;
    status?: string;
  }>;
  metrics?: Array<{ 
    id: string; 
    name: string;
    mapped_name?: string;
    power_bi_field?: string;
    data_type?: string;
    status?: string;
  }>;
  sql?: string;
  data?: string;
}

export interface DashboardDetail extends Dashboard {
  properties?: {
    id?: string;
    name?: string;
    description?: string;
    type?: string;
    subtype?: number;
    ext_type?: string;
    ancestors?: Array<{id: string; name: string; level: number}>;
    certified_info?: string;
    comments?: string | null;
    date_created?: string;
    date_modified?: string;
    instance_id?: string;
    owner?: string;
    recipients?: Array<any>;
    version?: string;
    template_info?: {
      template: boolean;
      last_modified_by: any;
    };
    acg?: number;
    acl?: Array<string>;
    chapters?: Array<any>;
    current_chapter?: string;
  };
  chapters?: Array<{
    key: string;
    name: string;
    pages: Array<{
      key: string;
      name: string;
      visualizations: Array<{
        key: string;
        name: string;
        type: string;
      }>;
    }>;
  }>;
  connected_cubes?: Array<Cube>;
}

// Types for SQL analysis response
export interface SQLAnalysisField {
  name: string;
  type: string;
  description?: string;
}

export interface SQLAnalysisTable {
  id: string;
  name: string;
  type: 'fact' | 'dimension' | 'lookup' | 'other';
  fields: SQLAnalysisField[];
}

export interface SQLAnalysisTempTable {
  id: string;
  name: string;
  fields: SQLAnalysisField[];
}

export interface SQLAnalysisLink {
  source: string;
  target: string;
  relationship: string;
  sourceField: string;
  targetField: string;
  description: string;
  via_temp_table?: string;
}

export interface SQLAnalysisHierarchy {
  name: string;
  levels: string[];
  description: string;
}

export interface SQLModelStructure {
  fact_tables: string[];
  dimension_tables: string[];
  hierarchies: SQLAnalysisHierarchy[];
  model_type: 'star' | 'snowflake' | 'galaxy' | 'other';
  description: string;
}

// Raw response from the API
export interface SQLAnalysisRawResponse {
  main_tables: SQLAnalysisTable[];
  relationships: SQLAnalysisLink[];
  model_structure: SQLModelStructure;
}

// Formatted response for visual display
export interface SQLAnalysisVisualResponse {
  nodes: (SQLAnalysisTable | SQLAnalysisTempTable)[];
  links: SQLAnalysisLink[];
  model_structure: SQLModelStructure;
}

// For tabular display - updated to match your formatting code
export interface SQLAnalysisTabularTableField {
  field: string;
  type: string;
  description: string;
}

export interface SQLAnalysisTabularTable {
  table_name: string;
  table_id: string;
  table_type: string;
  fields: SQLAnalysisTabularTableField[];
}

export interface SQLAnalysisTabularRelationship {
  source_table: string;
  target_table: string;
  type: string;
  source_field: string;
  target_field: string;
  description: string;
}

export interface SQLAnalysisTabularResponse {
  tables: SQLAnalysisTabularTable[];
  relationships: SQLAnalysisTabularRelationship[];
  model_structure: SQLModelStructure;
}

// Power BI dataset interfaces
export interface PowerBIDatasetCreateResponse {
  task_id: string;
  dataset_name: string;
  status: string;
  message: string;
}

export interface PowerBIDatasetStatusResponse {
  task_id: string;
  dataset_name: string;
  dataset_id?: string;
  status: string;
  updates_count: number;
}

export interface PowerBIDatasetUpdate {
  type: 'status' | 'info' | 'success' | 'warning' | 'error';
  status?: 'started' | 'processing' | 'completed' | 'error';
  message: string;
  timestamp: string;
  dataset_id?: string;
}

// Add types for expression responses
export interface ExpressionData {
  text: string;
  tokens: string | null;
  tree: any | null;
}

export interface MetricExpressionResponse {
  id: string;
  name: string;
  expression: ExpressionData;
}

export interface AttributeFormExpression {
  id: string;
  expression: ExpressionData;
}

export interface AttributeForm {
  id: string;
  name: string;
  expressions: AttributeFormExpression[];
}

export interface AttributeExpressionsResponse {
  id: string;
  name: string;
  forms: AttributeForm[];
}

export interface DaxConversionResponse {
  dax: string;
}

export interface AttributeTable {
  id: string;
  name: string;
}

export interface RelatedAttribute {
  id: string;
  name: string;
}

export interface AttributeRelationship {
  table: AttributeTable;
  related_attribute: RelatedAttribute;
  relationship_type: string;
}

export interface AttributeDependency {
  id: string;
  name: string;
}

export interface ReportAttributeDetail {
  id: string;
  name: string;
  tables: AttributeTable[];
  relationships: AttributeRelationship[];
  dependencies: AttributeDependency[];
}

// Batch SQL Analysis interfaces
export interface BatchAnalysisRequest {
  project_id: string;
  report_ids: string[];
}

export interface BatchAnalysisReportResult {
  report_id: string;
  report_name: string;
  status: 'success' | 'error';
  analysis?: SQLAnalysisRawResponse;
  error?: string;
}

export interface BatchAnalysisResponse {
  project_id: string;
  analyses_count: number;
  analyses: BatchAnalysisReportResult[];
}

export interface ProjectAnalysisResponse {
  project_id: string;
  analyses: {
    [report_id: string]: {
      report_name: string;
      analysis: SQLAnalysisRawResponse;
    }
  }
}

// Add new types for environment management
export interface EnvironmentCredentials {
  name: string;
  is_default: boolean;
  credentials: {
    url: string;
    username: string;
    password?: string;
    description?: string;
  }
}

export interface Environment {
  name: string;
  is_active: boolean;
  is_default: boolean;
}

export interface EnvironmentsResponse {
  environments: Environment[];
  active_environment: string | null;
}

const BASE_URL = 'http://localhost:8001/api';

export const apiService = {
  checkHealth: async () => {
    try {
      const response = await fetch(`${BASE_URL}/health`);
      return await response.json();
    } catch (error) {
      console.error('Health check failed:', error);
      return { status: 'error', message: 'API is unreachable' };
    }
  },

  getProjects: async (): Promise<Project[]> => {
    try {
      const response = await fetch(`${BASE_URL}/projects`);
      if (!response.ok) throw new Error('Failed to fetch projects');
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch projects:', error);
      throw error;
    }
  },

  getProject: async (projectId: string): Promise<Project> => {
    try {
      const response = await fetch(`${BASE_URL}/projects/${projectId}`);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: 'Failed to fetch project' }));
        throw new Error(errorData.detail || 'Failed to fetch project');
      }
      return await response.json();
    } catch (error) {
      console.error(`Failed to fetch project ${projectId}:`, error);
      throw error;
    }
  },

  getReports: async (projectId: string): Promise<Report[]> => {
    try {
      const response = await fetch(`${BASE_URL}/reports?project_id=${projectId}`);
      if (!response.ok) throw new Error('Failed to fetch reports');
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch reports:', error);
      throw error;
    }
  },

  getFFSQLReports: async (projectId: string): Promise<Report[]> => {
    try {
      const response = await fetch(`${BASE_URL}/reports?project_id=${projectId}`);
      if (!response.ok) throw new Error('Failed to fetch FFSQL reports');
      const reports = await response.json();
      // Filter for reports with ext_type of "CUSTOM_SQL_FREE_FORM"
      return reports.filter((report: any) => report.ext_type === "CUSTOM_SQL_FREE_FORM");
    } catch (error) {
      console.error('Failed to fetch FFSQL reports:', error);
      throw error;
    }
  },

  getReport: async (reportId: string, projectId: string): Promise<ReportDetail> => {
    try {
      const response = await fetch(`${BASE_URL}/report/${reportId}?project_id=${projectId}`);
      if (!response.ok) throw new Error('Failed to fetch report details');
      const data = await response.json();
      
      // Handle the API response to match the interface
      return {
        ...data,
        id: data.id,
        name: data.name,
        type: data.type,
        // Extract SQL and data fields that might be in different format
        sql: data["Report SQL View:"] || data.sql,
        data: data["Report Data (first few rows):"] || data.data
      };
    } catch (error) {
      console.error('Failed to fetch report details:', error);
      throw error;
    }
  },

  getCubes: async (projectId: string): Promise<Cube[]> => {
    try {
      const response = await fetch(`${BASE_URL}/cubes?project_id=${projectId}`);
      if (!response.ok) throw new Error('Failed to fetch cubes');
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch cubes:', error);
      throw error;
    }
  },

  getCube: async (cubeId: string, projectId: string): Promise<CubeDetail> => {
    try {
      const response = await fetch(`${BASE_URL}/cube/${cubeId}?project_id=${projectId}`);
      if (!response.ok) throw new Error('Failed to fetch cube details');
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch cube details:', error);
      throw error;
    }
  },

  getCubeSql: async (cubeId: string, projectId: string) => {
    try {
      const response = await fetch(`${BASE_URL}/cube/${cubeId}/sql?project_id=${projectId}`);
      if (!response.ok) throw new Error('Failed to fetch cube SQL');
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch cube SQL:', error);
      throw error;
    }
  },

  getDashboards: async (projectId: string): Promise<Dashboard[]> => {
    try {
      const response = await fetch(`${BASE_URL}/dashboards?project_id=${projectId}`);
      if (!response.ok) throw new Error('Failed to fetch dashboards');
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch dashboards:', error);
      throw error;
    }
  },

  getDashboard: async (dashboardId: string, projectId: string): Promise<DashboardDetail> => {
    try {
      const response = await fetch(`${BASE_URL}/dashboard/${dashboardId}?project_id=${projectId}`);
      if (!response.ok) throw new Error('Failed to fetch dashboard details');
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch dashboard details:', error);
      throw error;
    }
  },
  
  analyzeSql: async (sql: string, format: 'visual' | 'tabular' = 'visual'): Promise<SQLAnalysisVisualResponse | SQLAnalysisTabularResponse> => {
    try {
      const response = await fetch(`${BASE_URL}/analyze-sql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sql }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to analyze SQL');
      }
      
      const rawData: SQLAnalysisRawResponse = await response.json();
      
      // Format the data based on the requested format
      if (format === 'tabular') {
        // Convert to tabular format
        const tabularData: SQLAnalysisTabularResponse = {
          tables: [],
          relationships: [],
          model_structure: rawData.model_structure,
        };
        
        // Process main tables
        for (const table of rawData.main_tables) {
          tabularData.tables.push({
            table_name: table.name,
            table_id: table.id,
            table_type: table.type,
            fields: table.fields.map(f => ({
              field: f.name,
              type: f.type,
              description: f.description || '',
            })),
          });
        }
        
        // Process relationships
        tabularData.relationships = rawData.relationships.map(rel => ({
          source_table: rel.source,
          target_table: rel.target,
          type: rel.relationship,
          source_field: rel.sourceField,
          target_field: rel.targetField,
          description: rel.description || '',
        }));
        
        return tabularData;
      } else {
        // Convert to visual format
        const visualData: SQLAnalysisVisualResponse = {
          nodes: rawData.main_tables,
          links: rawData.relationships,
          model_structure: rawData.model_structure,
        };
        
        return visualData;
      }
    } catch (error) {
      console.error('Failed to analyze SQL:', error);
      throw error;
    }
  },

  // Legacy API methods - DEPRECATED
  // These methods are kept for backwards compatibility but should not be used in new code
  // Use the environment-based API methods instead
  getCredentials: async () => {
    try {
      const response = await fetch(`${BASE_URL}/credentials`);
      if (!response.ok) throw new Error('Failed to fetch credentials');
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch credentials:', error);
      throw error;
    }
  },

  // DEPRECATED - Use saveEnvironment instead
  saveCredentials: async (credentials: { 
    url: string; 
    username?: string; 
    password?: string;
    storePassword?: boolean;
  }) => {
    try {
      // Create a copy of credentials to modify
      const credentialsToSend = { ...credentials };
      
      // If storePassword is false, remove the password from stored credentials
      // but still use it for the current session
      if (credentials.storePassword === false) {
        // Don't store the password in the backend
        delete credentialsToSend.password;
      }
      
      // Remove the storePassword flag as it's not needed by the API
      delete credentialsToSend.storePassword;
      
      const response = await fetch(`${BASE_URL}/credentials`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentialsToSend),
      });
      if (!response.ok) throw new Error('Failed to save credentials');
      return await response.json();
    } catch (error) {
      console.error('Failed to save credentials:', error);
      throw error;
    }
  },

  // DEPRECATED - Use deleteEnvironment instead
  deleteCredentials: async () => {
    try {
      const response = await fetch(`${BASE_URL}/credentials`, {
        method: 'DELETE',
      });
      if (!response.ok) throw new Error('Failed to delete credentials');
      return await response.json();
    } catch (error) {
      console.error('Failed to delete credentials:', error);
      throw error;
    }
  },

  // Get visualization data from a dashboard
  getVisualizationData: async (
    dashboardId: string,
    chapterKey: string,
    visualizationKey: string,
    projectId: string,
    options?: {
      offset?: number;
      limit?: number;
    }
  ) => {
    try {
      const queryParams = new URLSearchParams({
        project_id: projectId,
        ...(options?.offset !== undefined && { offset: options.offset.toString() }),
        ...(options?.limit !== undefined && { limit: options.limit.toString() })
      });

      const response = await fetch(
        `${BASE_URL}/dashboard/${dashboardId}/chapters/${chapterKey}/visualizations/${visualizationKey}/data?${queryParams}`
      );
      
      if (!response.ok) throw new Error('Failed to fetch visualization data');
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch visualization data:', error);
      throw error;
    }
  },

  // Power BI Integration APIs
  createPowerBIDataset: async (reportId: string, datasetName: string, projectId: string, workspaceId: string, includeData: boolean = true): Promise<{ task_id: string; dataset_name: string; status: string; message: string }> => {
    try {
      const response = await fetch(`${BASE_URL}/power-bi/create-dataset`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          report_id: reportId,
          dataset_name: datasetName,
          include_data: includeData,
          project_id: projectId,
          workspace_id: workspaceId
        }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to create Power BI dataset');
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to create Power BI dataset:', error);
      throw error;
    }
  },
  
  checkPowerBIDatasetStatus: async (taskId: string): Promise<{ task_id: string; dataset_name: string; dataset_id?: string; status: string; updates_count: number }> => {
    try {
      const response = await fetch(`${BASE_URL}/power-bi/dataset-status/${taskId}`);
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to check dataset status');
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to check Power BI dataset status:', error);
      throw error;
    }
  },
  
  // This function returns the URL for SSE connection rather than making the request directly
  getPowerBIDatasetUpdatesUrl: (taskId: string): string => {
    return `${BASE_URL}/power-bi/dataset-updates/${taskId}`;
  },

  // New methods for expressions and DAX conversion
  getMetricExpression: async (metricId: string, projectId: string): Promise<MetricExpressionResponse> => {
    try {
      const response = await fetch(`${BASE_URL}/metric/${metricId}/expression?project_id=${projectId}`);
      if (!response.ok) throw new Error('Failed to fetch metric expression');
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch metric expression:', error);
      throw error;
    }
  },
  
  getAttributeExpressions: async (attributeId: string, projectId: string): Promise<AttributeExpressionsResponse> => {
    try {
      const response = await fetch(`${BASE_URL}/attribute/${attributeId}/expressions?project_id=${projectId}`);
      if (!response.ok) throw new Error('Failed to fetch attribute expressions');
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch attribute expressions:', error);
      throw error;
    }
  },
  
  getMetricToDax: async (metricId: string, projectId: string): Promise<DaxConversionResponse> => {
    try {
      const response = await fetch(`${BASE_URL}/metric/${metricId}/to_dax?project_id=${projectId}`);
      if (!response.ok) throw new Error('Failed to convert metric to DAX');
      return await response.json();
    } catch (error) {
      console.error('Failed to convert metric to DAX:', error);
      throw error;
    }
  },
  
  getAttributeToDax: async (attributeId: string, projectId: string): Promise<DaxConversionResponse> => {
    try {
      const response = await fetch(`${BASE_URL}/attribute/${attributeId}/to_dax?project_id=${projectId}`);
      if (!response.ok) throw new Error('Failed to convert attribute to DAX');
      return await response.json();
    } catch (error) {
      console.error('Failed to convert attribute to DAX:', error);
      throw error;
    }
  },

  getReportAttribute: async (attributeId: string, projectId: string): Promise<ReportAttributeDetail> => {
    try {
      const response = await fetch(`${BASE_URL}/report-attribute/${attributeId}?project_id=${projectId}`);
      if (!response.ok) throw new Error('Failed to fetch report attribute details');
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch report attribute details:', error);
      throw error;
    }
  },

  // Add these new methods to the apiService object
  getFilteredAttributes: async (projectId: string) => {
    const response = await fetch(`${BASE_URL}/attributes/filter_by_dependency?project_id=${projectId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch filtered attributes');
    }
    return response.json();
  },

  getFilteredMetrics: async (projectId: string) => {
    const response = await fetch(`${BASE_URL}/metrics/filter_by_dependency?project_id=${projectId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch filtered metrics');
    }
    return response.json();
  },

  // Analyze multiple reports' SQL in batch
  analyzeReports: async (projectId: string, reportIds: string[]): Promise<BatchAnalysisResponse> => {
    try {
      const response = await fetch(`${BASE_URL}/analyze-reports`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          project_id: projectId,
          report_ids: reportIds
        }),
      });
      
      if (!response.ok) throw new Error('Failed to analyze reports');
      return await response.json();
    } catch (error) {
      console.error('Failed to analyze reports:', error);
      throw error;
    }
  },

  // Get stored analysis for a project
  getProjectAnalysis: async (projectId: string): Promise<ProjectAnalysisResponse> => {
    try {
      const response = await fetch(`${BASE_URL}/get-project-analysis`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ project_id: projectId }),
      });
      if (!response.ok) throw new Error('Failed to fetch project analysis');
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch project analysis:', error);
      throw error;
    }
  },

  // Delete stored analysis for a project
  deleteProjectAnalysis: async (projectId: string): Promise<any> => {
    try {
      const response = await fetch(`${BASE_URL}/delete-project-analysis`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ project_id: projectId }),
      });
      if (!response.ok) throw new Error('Failed to delete project analysis');
      return await response.json();
    } catch (error) {
      console.error('Failed to delete project analysis:', error);
      throw error;
    }
  },

  // New methods for cached apply functions
  getCachedAttributes: async (projectId: string) => {
    const response = await fetch(`${BASE_URL}/attributes/cached_apply_functions/${projectId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch cached attributes');
    }
    return response.json();
  },

  getCachedMetrics: async (projectId: string) => {
    const response = await fetch(`${BASE_URL}/metrics/cached_apply_functions/${projectId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch cached metrics');
    }
    return response.json();
  },

  startAttributesBackgroundProcess: async (projectId: string) => {
    const response = await fetch(`${BASE_URL}/attributes/process_apply_functions?project_id=${projectId}`, {
      method: 'POST',
    });
    if (!response.ok) {
      throw new Error('Failed to start background process for attributes');
    }
    return response.json();
  },

  startMetricsBackgroundProcess: async (projectId: string) => {
    const response = await fetch(`${BASE_URL}/metrics/process_apply_functions?project_id=${projectId}`, {
      method: 'POST',
    });
    if (!response.ok) {
      throw new Error('Failed to start background process for metrics');
    }
    return response.json();
  },

  // DAX Conversion APIs
  async batchConvertAttributesToDax(projectId: string, attributeIds: string[]): Promise<any> {
    const response = await fetch(`${BASE_URL}/attributes/batch_convert_to_dax?project_id=${projectId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(attributeIds),
    });
    return response.json();
  },

  async batchConvertMetricsToDax(projectId: string, metricIds: string[]): Promise<any> {
    const response = await fetch(`${BASE_URL}/metrics/batch_convert_to_dax?project_id=${projectId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(metricIds),
    });
    return response.json();
  },

  async getConvertedAttributes(projectId: string): Promise<any> {
    const response = await fetch(`${BASE_URL}/attributes/converted_expressions/${projectId}`);
    return response.json();
  },

  async getConvertedMetrics(projectId: string): Promise<any> {
    const response = await fetch(`${BASE_URL}/metrics/converted_expressions/${projectId}`);
    return response.json();
  },

  downloadProjectAnalysis: async (projectId: string, format: 'pdf' | 'docx' = 'docx'): Promise<void> => {
    try {
      console.log(`Initiating download for project ${projectId} as Word document`);
      
      // Create the URL with query parameters
      const url = new URL(`${BASE_URL}/download-project-analysis`);
      
      // Open the download in a new tab/window to avoid any UI freezing
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = url.toString();
      form.target = '_blank';
      
      // Create hidden fields for the request body
      const projectIdField = document.createElement('input');
      projectIdField.type = 'hidden';
      projectIdField.name = 'project_id';
      projectIdField.value = projectId;
      form.appendChild(projectIdField);
      
      const formatField = document.createElement('input');
      formatField.type = 'hidden';
      formatField.name = 'format';
      formatField.value = 'docx'; // Always use docx format
      form.appendChild(formatField);
      
      // Append form to body, submit it, and then remove it
      document.body.appendChild(form);
      form.submit();
      document.body.removeChild(form);
      
      return Promise.resolve();
    } catch (error) {
      console.error('Failed to download project analysis:', error);
      throw error;
    }
  },
  
  downloadReportsSql: async (projectId: string, reportIds: string[]): Promise<void> => {
    try {
      console.log(`Initiating SQL download for ${reportIds.length} reports in project ${projectId}`);
      
      // Create the URL for the download endpoint
      const url = new URL(`${BASE_URL}/download-reports-sql`);
      
      // Create a form to submit with the report IDs
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = url.toString();
      form.target = '_blank'; // Open in new window to avoid UI freezing
      
      // Add project ID field
      const projectIdField = document.createElement('input');
      projectIdField.type = 'hidden';
      projectIdField.name = 'project_id';
      projectIdField.value = projectId;
      form.appendChild(projectIdField);
      
      // Add report IDs fields
      reportIds.forEach((reportId, index) => {
        const reportIdField = document.createElement('input');
        reportIdField.type = 'hidden';
        reportIdField.name = 'report_ids';
        reportIdField.value = reportId;
        form.appendChild(reportIdField);
      });
      
      // Append form to body, submit it, and then remove it
      document.body.appendChild(form);
      form.submit();
      document.body.removeChild(form);
      
      return Promise.resolve();
    } catch (error) {
      console.error('Failed to download reports SQL:', error);
      throw error;
    }
  },

  async downloadDaxExpressions(projectId: string, includeMetrics = true, includeAttributes = true): Promise<void> {
    const response = await fetch(`${BASE_URL}/power-bi/download-dax-expressions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        project_id: projectId,
        include_metrics: includeMetrics,
        include_attributes: includeAttributes
      }),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to download DAX expressions');
    }
    
    // Get the blob from the response
    const blob = await response.blob();
    
    // Create a temporary URL for the blob
    const url = window.URL.createObjectURL(blob);
    
    // Create a temporary link element
    const a = document.createElement('a');
    a.href = url;
    
    // Set the filename from the Content-Disposition header if available
    const contentDisposition = response.headers.get('Content-Disposition');
    let filename = 'DAX_Expressions.xlsx';
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="?([^"]*)"?/);
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1];
      }
    }
    
    a.download = filename;
    
    // Append to the document, click, and clean up
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  },

  // Environment API methods
  getEnvironments: async (): Promise<EnvironmentsResponse> => {
    try {
      const response = await fetch(`${BASE_URL}/environments`);
      if (!response.ok) throw new Error('Failed to fetch environments');
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch environments:', error);
      throw error;
    }
  },

  testEnvironmentConnection: async (environmentName: string): Promise<any> => {
    try {
      const response = await fetch(`${BASE_URL}/environments/test-connection?environment_name=${environmentName}`, {
        method: 'POST',
      });
      if (!response.ok) throw new Error('Failed to test connection');
      return await response.json();
    } catch (error) {
      console.error('Failed to test connection:', error);
      throw error;
    }
  },

  setActiveEnvironment: async (environmentName: string): Promise<any> => {
    try {
      const response = await fetch(`${BASE_URL}/credentials/active/${environmentName}`, {
        method: 'PUT',
      });
      if (!response.ok) throw new Error('Failed to set active environment');
      return await response.json();
    } catch (error) {
      console.error('Failed to set active environment:', error);
      throw error;
    }
  },

  saveEnvironment: async (environmentData: EnvironmentCredentials): Promise<any> => {
    try {
      const response = await fetch(`${BASE_URL}/credentials`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(environmentData),
      });
      if (!response.ok) throw new Error('Failed to save environment');
      return await response.json();
    } catch (error) {
      console.error('Failed to save environment:', error);
      throw error;
    }
  },

  deleteEnvironment: async (environmentName: string): Promise<any> => {
    try {
      const response = await fetch(`${BASE_URL}/credentials/${environmentName}`, {
        method: 'DELETE',
      });
      if (!response.ok) throw new Error('Failed to delete environment');
      return await response.json();
    } catch (error) {
      console.error('Failed to delete environment:', error);
      throw error;
    }
  },

  getActiveEnvironment: async (): Promise<{ active_environment: string | null }> => {
    try {
      const response = await fetch(`${BASE_URL}/environments/active`);
      if (!response.ok) throw new Error('Failed to get active environment');
      return await response.json();
    } catch (error) {
      console.error('Failed to get active environment:', error);
      throw error;
    }
  },
};

