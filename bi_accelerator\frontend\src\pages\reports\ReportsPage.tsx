import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { BarChart3, Search, Info, ChevronRight, FileText, Grid, List, BarChart4 } from 'lucide-react';
import { apiService, Report, Project } from '@/services/api';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import Card, { CardHeader, CardTitle, CardContent, CardFooter } from '@/components/shared/Card';
import Button from '@/components/shared/Button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader } from '@/components/shared/Loader';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { useViewMode } from '@/lib/ViewModeContext';
import BulkPowerBIExportModal from '@/components/BulkPowerBIExportModal';

const ReportsPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const { viewMode, setViewMode } = useViewMode();
  const [isBulkExportOpen, setIsBulkExportOpen] = useState(false);
  
  const { data: allProjects } = useQuery({
    queryKey: ['projects'],
    queryFn: () => apiService.getProjects(),
  });

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const projectId = params.get('projectId');

    if (projectId) {
      if (allProjects) {
        const projectFromUrl = allProjects.find(p => p.id === projectId);
        if (projectFromUrl) {
          setSelectedProject(projectFromUrl);
          localStorage.setItem('selectedProject', JSON.stringify(projectFromUrl));
        } else {
          console.error(`Project with ID ${projectId} not found.`);
          navigate('/projects');
        }
      }
    } else {
      const storedProject = localStorage.getItem('selectedProject');
      if (storedProject) {
        setSelectedProject(JSON.parse(storedProject));
      }
    }
  }, [location.search, allProjects, navigate]);
  
  const { data: reports, isLoading, error } = useQuery({
    queryKey: ['reports', selectedProject?.id],
    queryFn: () => selectedProject ? apiService.getReports(selectedProject.id) : Promise.resolve([]),
    enabled: !!selectedProject,
  });
  
  const filteredReports = reports?.filter(report => 
    report.name.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const handleReportClick = (report: Report) => {
    navigate(`/reports/${report.id}`);
  };

  const handleBulkExport = () => {
    setIsBulkExportOpen(true);
  };
  
  if (!selectedProject) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <div className="text-center py-12">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
            <BarChart3 className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-medium mb-2">No Reports Available</h3>
          <p className="text-muted-foreground">
            There are no reports to display
          </p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container px-4 mx-auto animate-fade-in">
      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/')}>
          Home
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/projects')}>
          Projects
        </Button>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground truncate max-w-xs">
          Reports
        </span>
      </div>
      
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div className="relative max-w-md w-full">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search reports..."
            className="pl-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            disabled={isLoading}
          />
        </div>
        <div className="flex items-center gap-4">
          <Button
            onClick={handleBulkExport}
            className="flex items-center gap-2"
            disabled={isLoading || !reports?.length}
          >
            <BarChart4 className="h-4 w-4" />
            Bulk Export to Power BI
          </Button>
          <ToggleGroup type="single" value={viewMode} onValueChange={(value) => value && setViewMode(value as 'card' | 'list')}>
            <ToggleGroupItem value="card" aria-label="Card view" disabled={isLoading}>
              <Grid className="h-4 w-4" />
            </ToggleGroupItem>
            <ToggleGroupItem value="list" aria-label="List view" disabled={isLoading}>
              <List className="h-4 w-4" />
            </ToggleGroupItem>
          </ToggleGroup>
        </div>
      </div>
      
      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader className="text-brand-600" />
        </div>
      ) : error ? (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-700 flex items-center">
              <Info className="mr-2 h-5 w-5" />
              Error Loading Reports
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">
              There was an error loading the reports. Please check your connection and try again.
            </p>
          </CardContent>
          <CardFooter>
            <Button 
              onClick={() => window.location.reload()}
              variant="outline"
              className="text-red-600 border-red-200 hover:bg-red-50"
            >
              Retry
            </Button>
          </CardFooter>
        </Card>
      ) : !filteredReports?.length ? (
        <div className="text-center py-12">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
            <BarChart3 className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-medium mb-2">No Reports Found</h3>
          <p className="text-muted-foreground">
            {searchTerm 
              ? `No reports match "${searchTerm}"`
              : "There are no reports available"
            }
          </p>
        </div>
      ) : viewMode === 'card' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredReports?.map((report, index) => (
            <Card 
              key={report.id} 
              hoverable 
              className={`group transition-all duration-300 animate-slide-up delay-${index * 50}`}
              onClick={() => handleReportClick(report)}
            >
              <CardHeader>
                <div className="flex items-center">
                  <div className="p-2 rounded-lg bg-purple-100 mr-3">
                    <BarChart3 className="h-5 w-5 text-purple-600" />
                  </div>
                  <CardTitle className="truncate">{report.name}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-2">
                  Type: <span className="font-medium">{report.ext_type}</span>
                </p>
                <p className="text-sm text-muted-foreground mb-2">
                  ID: <span className="font-mono text-xs bg-muted rounded px-1 py-0.5">{report.id}</span>
                </p>
              </CardContent>
              <CardFooter>
                <Button 
                  variant="default" 
                  fullWidth
                  className="group-hover:bg-brand-600 transition-colors"
                  icon={<FileText className="h-4 w-4" />}
                  iconPosition="right"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleReportClick(report);
                  }}
                >
                  View Report
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="flex flex-col gap-3">
          {filteredReports?.map((report, index) => (
            <div 
              key={report.id} 
              className={`group flex items-center justify-between p-4 rounded-lg border border-border bg-card transition-all duration-300 animate-slide-up delay-${index * 50} cursor-pointer hover:shadow-md hover:bg-accent/5`}
              onClick={() => handleReportClick(report)}
            >
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-purple-100 mr-3">
                  <BarChart3 className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-semibold">{report.name}</h3>
                  <div className="flex gap-4">
                    <p className="text-sm text-muted-foreground">
                      Type: <span className="font-medium">{report.ext_type}</span>
                    </p>
                    <p className="text-sm text-muted-foreground">
                      ID: <span className="font-mono text-xs bg-muted rounded px-1 py-0.5">{report.id}</span>
                    </p>
                  </div>
                </div>
              </div>
              <Button 
                variant="default" 
                size="sm"
                className="group-hover:bg-brand-600 transition-colors"
                icon={<FileText className="h-4 w-4" />}
                iconPosition="right"
                onClick={(e) => {
                  e.stopPropagation();
                  handleReportClick(report);
                }}
              >
                View Report
              </Button>
            </div>
          ))}
        </div>
      )}
      
      {/* Bulk Export Modal */}
      {selectedProject && reports && (
        <BulkPowerBIExportModal
          isOpen={isBulkExportOpen}
          onClose={() => setIsBulkExportOpen(false)}
          reports={reports}
          projectId={selectedProject.id}
        />
      )}
    </div>
  );
};

export default ReportsPage;
