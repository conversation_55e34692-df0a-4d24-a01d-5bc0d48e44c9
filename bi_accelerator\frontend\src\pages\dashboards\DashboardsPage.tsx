import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { LayoutDashboard, Search, Info, ChevronRight, PanelTop, Grid, List } from 'lucide-react';
import { apiService, Dashboard, Project } from '@/services/api';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import Card, { CardHeader, CardTitle, CardContent, CardFooter } from '@/components/shared/Card';
import Button from '@/components/shared/Button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader } from '@/components/shared/Loader';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { useViewMode } from '@/lib/ViewModeContext';

const DashboardsPage = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const { viewMode, setViewMode } = useViewMode();
  
  useEffect(() => {
    const storedProject = localStorage.getItem('selectedProject');
    if (storedProject) {
      setSelectedProject(JSON.parse(storedProject));
    } else {
      navigate('/projects');
    }
  }, [navigate]);
  
  const { data: dashboards, isLoading, error } = useQuery({
    queryKey: ['dashboards', selectedProject?.id],
    queryFn: () => selectedProject ? apiService.getDashboards(selectedProject.id) : Promise.resolve([]),
    enabled: !!selectedProject,
  });
  
  const filteredDashboards = dashboards?.filter(dashboard => 
    dashboard.name.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const handleDashboardClick = (dashboard: Dashboard) => {
    navigate(`/dashboards/${dashboard.id}`);
  };
  
  if (!selectedProject) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <Alert>
          <AlertTitle>No project selected</AlertTitle>
          <AlertDescription>
            Please select a project first to view dashboards.
            <Button
              variant="link"
              className="p-0 ml-2"
              onClick={() => navigate('/projects')}
            >
              Go to Projects
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }
  
  return (
    <div className="container px-4 mx-auto animate-fade-in">
      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/')}>
          Home
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/projects')}>
          Projects
        </Button>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground truncate max-w-xs">
          Dashboards
        </span>
      </div>
      
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div className="relative max-w-md w-full">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search dashboards..."
            className="pl-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-4">
          <ToggleGroup type="single" value={viewMode} onValueChange={(value) => value && setViewMode(value as 'card' | 'list')}>
            <ToggleGroupItem value="card" aria-label="Card view">
              <Grid className="h-4 w-4" />
            </ToggleGroupItem>
            <ToggleGroupItem value="list" aria-label="List view">
              <List className="h-4 w-4" />
            </ToggleGroupItem>
          </ToggleGroup>
        </div>
      </div>
      
      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader />
        </div>
      ) : error ? (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-700 flex items-center">
              <Info className="mr-2 h-5 w-5" />
              Error Loading Dashboards
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">
              There was an error loading the dashboards. Please check your connection and try again.
            </p>
          </CardContent>
          <CardFooter>
            <Button 
              onClick={() => window.location.reload()}
              variant="outline"
              className="text-red-600 border-red-200 hover:bg-red-50"
            >
              Retry
            </Button>
          </CardFooter>
        </Card>
      ) : filteredDashboards?.length === 0 ? (
        <div className="text-center py-12">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
            <LayoutDashboard className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-medium mb-2">No Dashboards Found</h3>
          <p className="text-muted-foreground">
            {searchTerm 
              ? `No dashboards match "${searchTerm}"`
              : "There are no dashboards available for this project"
            }
          </p>
        </div>
      ) : viewMode === 'card' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredDashboards?.map((dashboard, index) => (
            <Card 
              key={dashboard.id} 
              hoverable 
              className={`group transition-all duration-300 animate-slide-up delay-${index * 50}`}
              onClick={() => handleDashboardClick(dashboard)}
            >
              <CardHeader>
                <div className="flex items-center">
                  <div className="p-2 rounded-lg bg-orange-100 mr-3">
                    <LayoutDashboard className="h-5 w-5 text-orange-600" />
                  </div>
                  <CardTitle className="truncate">{dashboard.name}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-2">
                  ID: <span className="font-mono text-xs bg-muted rounded px-1 py-0.5">{dashboard.id}</span>
                </p>
              </CardContent>
              <CardFooter>
                <Button 
                  variant="default" 
                  fullWidth
                  className="group-hover:bg-brand-600 transition-colors"
                  icon={<PanelTop className="h-4 w-4" />}
                  iconPosition="right"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDashboardClick(dashboard);
                  }}
                >
                  View Dashboard
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="flex flex-col gap-3">
          {filteredDashboards?.map((dashboard, index) => (
            <div 
              key={dashboard.id} 
              className={`group flex items-center justify-between p-4 rounded-lg border border-border bg-card transition-all duration-300 animate-slide-up delay-${index * 50} cursor-pointer hover:shadow-md hover:bg-accent/5`}
              onClick={() => handleDashboardClick(dashboard)}
            >
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-orange-100 mr-3">
                  <LayoutDashboard className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <h3 className="font-semibold">{dashboard.name}</h3>
                  <p className="text-sm text-muted-foreground">
                    ID: <span className="font-mono text-xs bg-muted rounded px-1 py-0.5">{dashboard.id}</span>
                  </p>
                </div>
              </div>
              <Button 
                variant="default" 
                size="sm"
                className="group-hover:bg-brand-600 transition-colors"
                icon={<PanelTop className="h-4 w-4" />}
                iconPosition="right"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDashboardClick(dashboard);
                }}
              >
                View Dashboard
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DashboardsPage;
