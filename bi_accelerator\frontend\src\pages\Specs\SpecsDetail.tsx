import React, { useState, useEffect } from 'react';
import { useNavigate, Link, useParams } from 'react-router-dom';
import { ChevronRight, BarChart3, Database } from 'lucide-react';
import { Project } from '@/services/api';
import Card, { <PERSON><PERSON><PERSON><PERSON>, <PERSON>T<PERSON><PERSON>, Card<PERSON>ontent, CardFooter } from '@/components/shared/Card';
import Button from '@/components/shared/Button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

const SpecsDetail = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  
  useEffect(() => {
    const storedProject = localStorage.getItem('selectedProject');
    if (storedProject) {
      const project = JSON.parse(storedProject);
      // Ensure the project from storage matches the ID from the URL
      if (project.id === id) {
        setSelectedProject(project);
      } else {
        // If not, it's possible the user navigated directly
        // or the stored project is stale. For now, we'll clear it.
        localStorage.removeItem('selectedProject');
        // Potentially fetch project details here if needed, for now navigate to projects
        navigate('/specs');
      }
    } else {
      navigate('/specs');
    }
  }, [id, navigate]);
  
  if (!selectedProject) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <Alert>
          <AlertTitle>No project selected</AlertTitle>
          <AlertDescription>
            Please select a project from the specs page first.
            <Button
              variant="link"
              className="p-0 ml-2"
              onClick={() => navigate('/specs')}
            >
              Go to Specs
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const specModules = [
    { 
      title: "Cubes", 
      description: "Browse and explore data cubes in this project",
      icon: Database,
      path: `/cubes` // This will navigate to the cubes page, filtered by the project
    },
    { 
      title: "Reports", 
      description: "View and analyze reports in this project",
      icon: BarChart3,
      path: `/reports` // This will navigate to the reports page, filtered by the project
    },
  ];

  return (
    <div className="container px-4 mx-auto animate-fade-in">
      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/')}>
          Home
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/specs')}>
          Specs
        </Button>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground truncate max-w-xs">
          {selectedProject.name}
        </span>
      </div>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Project Resources</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {specModules.map((module) => (
            <Card key={module.title} className="hover:shadow-md transition-shadow group">
              <Link to={`/specs/${selectedProject.id}${module.path}`}>
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center gap-2">
                    <module.icon className="h-5 w-5 text-brand-600" />
                    <span>{module.title}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">{module.description}</p>
                </CardContent>
                <CardFooter>
                  <Button
                    variant="outline"
                    className="w-full group-hover:bg-brand-50 group-hover:text-brand-600 group-hover:border-brand-200 transition-colors flex justify-center items-center h-10"
                  >
                    View
                  </Button>
                </CardFooter>
              </Link>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SpecsDetail; 
