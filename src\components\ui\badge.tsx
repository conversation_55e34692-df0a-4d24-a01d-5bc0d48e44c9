import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"
import { useBrandTheme } from "@/themes/brand-theme-provider"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  color?: "blue" | "red" | "yellow" | "green"
}

function Badge({ className, variant, color, ...props }: BadgeProps) {
  const { currentTheme } = useBrandTheme();
  let style = {};

  // If Google theme and color prop is set, use accent color
  if (currentTheme?.id === "google" && color && currentTheme.colors?.accent?.[color]) {
    style = {
      backgroundColor: currentTheme.colors.accent[color],
      color: "#fff",
      border: "none"
    };
  }

  return (
    <div
      className={cn(badgeVariants({ variant }), className)}
      style={style}
      {...props}
    />
  )
}

export { Badge } 