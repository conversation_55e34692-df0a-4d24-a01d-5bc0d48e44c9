import React, { useState, useEffect, useRef } from 'react';
import { ExternalLink, Check, AlertCircle, Info, Clock, BarChart4 } from 'lucide-react';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { apiService, PowerBIDatasetUpdate } from '@/services/api';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader } from '@/components/shared/Loader';

interface PowerBIExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  reportId: string;
  reportName: string;
  projectId: string;
}

export const PowerBIExportModal: React.FC<PowerBIExportModalProps> = ({
  isOpen,
  onClose,
  reportId,
  reportName,
  projectId
}) => {
  const { toast } = useToast();
  const [workspaceId, setWorkspaceId] = useState('');
  const [datasetName, setDatasetName] = useState(reportName || '');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [taskId, setTaskId] = useState<string | null>(null);
  const [updates, setUpdates] = useState<PowerBIDatasetUpdate[]>([]);
  const [isComplete, setIsComplete] = useState(false);
  const [datasetId, setDatasetId] = useState<string | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);
  const updatesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom of updates when new ones arrive
  useEffect(() => {
    if (updatesEndRef.current) {
      updatesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [updates]);

  // Clean up event source on unmount
  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!datasetName.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter a dataset name",
        variant: "destructive",
      });
      return;
    }

    if (!workspaceId.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter a workspace ID",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await apiService.createPowerBIDataset(
        reportId, 
        datasetName, 
        projectId,
        workspaceId
      );
      
      setTaskId(response.task_id);
      setIsExporting(true);
      
      // Connect to SSE endpoint
      connectToEventSource(response.task_id);
      
    } catch (error) {
      console.error('Failed to create Power BI dataset:', error);
      toast({
        title: "Export Error",
        description: error instanceof Error ? error.message : "Failed to start export process",
        variant: "destructive",
      });
      setIsSubmitting(false);
    }
  };

  const connectToEventSource = (taskId: string) => {
    const url = apiService.getPowerBIDatasetUpdatesUrl(taskId);
    const eventSource = new EventSource(url);
    eventSourceRef.current = eventSource;

    eventSource.onmessage = (event) => {
      try {
        const update = JSON.parse(event.data) as PowerBIDatasetUpdate;
        
        setUpdates(prev => [...prev, update]);
        
        // Check if process is completed or errored
        if (update.type === 'status' && ['completed', 'error'].includes(update.status || '')) {
          setIsComplete(true);
          if (update.dataset_id) {
            setDatasetId(update.dataset_id);
          }
          eventSource.close();
        }
      } catch (error) {
        console.error('Error parsing SSE update:', error);
      }
    };

    eventSource.onerror = () => {
      console.error('SSE connection error');
      toast({
        title: "Connection Error",
        description: "Lost connection to the server. The export process may still be running.",
        variant: "destructive",
      });
      setIsComplete(true); // Allow closing the modal
      eventSource.close();
    };
  };

  const handleClose = () => {
    if (isComplete || !isExporting) {
      // Only allow closing if process is complete or we haven't started exporting
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
      onClose();
    }
  };

  const renderUpdateIcon = (type: string, status?: string) => {
    switch (type) {
      case 'success':
        return <Check className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-amber-500" />;
      case 'status':
        if (status === 'completed') return <Check className="h-4 w-4 text-green-500" />;
        if (status === 'error') return <AlertCircle className="h-4 w-4 text-red-500" />;
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'info':
      default:
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="w-full sm:max-w-md md:max-w-lg lg:max-w-xl" onInteractOutside={(e) => {
        // Prevent closing by clicking outside while export is in progress
        if (isExporting && !isComplete) {
          e.preventDefault();
        }
      }} onEscapeKeyDown={(e) => {
        // Prevent closing with ESC key while export is in progress
        if (isExporting && !isComplete) {
          e.preventDefault();
        }
      }}>
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <BarChart4 className="h-5 w-5 mr-2" />
            {isExporting ? "Exporting to Power BI" : "Export to Power BI"}
          </DialogTitle>
        </DialogHeader>
        
        {!isExporting ? (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="workspace-id">Workspace ID</Label>
              <Input
                id="workspace-id"
                placeholder="Enter Power BI workspace ID"
                value={workspaceId}
                onChange={(e) => setWorkspaceId(e.target.value)}
                disabled={isSubmitting}
              />
              <p className="text-xs text-muted-foreground">
                Specify the Power BI workspace where the dataset will be created.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="dataset-name">Dataset name</Label>
              <Input
                id="dataset-name"
                placeholder="Enter Power BI dataset name"
                value={datasetName}
                onChange={(e) => setDatasetName(e.target.value)}
                disabled={isSubmitting}
              />
              <p className="text-xs text-muted-foreground">
                Specify the name for the Power BI dataset that will be created.
              </p>
            </div>
            
            {isSubmitting && (
              <div className="flex justify-center py-4">
                <Loader className="scale-75" />
              </div>
            )}
            
            <DialogFooter>
              <Button
                variant="outline"
                type="button"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Creating...' : 'Create Dataset'}
              </Button>
            </DialogFooter>
          </form>
        ) : (
          <div className="space-y-4">
            <div className="p-2 border rounded-md bg-muted/30">
              <p className="text-sm font-medium">Task ID: {taskId}</p>
              <p className="text-sm">Dataset: {datasetName}</p>
            </div>
            
            <div className="border rounded-md">
              <div className="p-2 border-b bg-muted/30">
                <p className="text-sm font-medium">Live Updates</p>
              </div>
              <ScrollArea className="h-60 w-full">
                <div className="p-3 space-y-2">
                  {updates.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-6">
                      <Loader className="scale-90" />
                      <p className="text-sm text-muted-foreground italic mt-2">Waiting for updates...</p>
                    </div>
                  ) : (
                    updates.map((update, index) => (
                      <div key={index} className="flex items-start space-x-2 text-sm">
                        <div className="mt-0.5">
                          {renderUpdateIcon(update.type, update.status)}
                        </div>
                        <div>
                          <p className={`${update.type === 'error' ? 'text-red-500' : ''}`}>
                            {update.message}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {new Date(update.timestamp).toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                    ))
                  )}
                  <div ref={updatesEndRef} />
                </div>
              </ScrollArea>
            </div>
            
            <DialogFooter>
              {isComplete ? (
                <>
                  {datasetId && (
                    <Button 
                      className="mr-auto" 
                      variant="outline" 
                      onClick={() => window.open(`https://app.powerbi.com/groups/${workspaceId}`, '_blank')}
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      View in Power BI
                    </Button>
                  )}
                  <Button onClick={handleClose}>
                    Close
                  </Button>
                </>
              ) : (
                <div className="flex items-center justify-center w-full gap-2">
                  <Loader className="scale-75" />
                  
                </div>
              )}
            </DialogFooter>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default PowerBIExportModal; 