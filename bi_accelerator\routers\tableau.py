from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import os
import json
import logging
import re
import pyodbc
import pandas as pd
from datetime import datetime
from dotenv import load_dotenv
from tableauhyperapi import HyperProcess, Telemetry, Connection as HyperConnection, CreateMode, TableDefinition, SqlType, TableName, Inserter
import tempfile
import tableauserverclient as TSC
from tableau_tools import *
from tableau_tools.tableau_documents import *
import zipfile
import shutil

# MicroStrategy Imports
from mstrio.connection import Connection
from mstrio.project_objects import Report

# OpenAI Imports
import openai

# Import MicroStrategy connection utility
from routers.utils import get_connection

# Initialize router
router = APIRouter(prefix="/api/mstr-schema", tags=["mstr_schema"])

# Configure logging
logger = logging.getLogger("mstr_schema_extractor")

# Load environment variables
load_dotenv()

# Tableau Cloud credentials from .env file
TOKEN_NAME = os.getenv('TOKEN_NAME')
TOKEN_SECRET = os.getenv('TOKEN_SECRET')
SITE_ID = os.getenv('SITE_ID', 'default')  # Default to 'default' if not specified
SERVER_URL = os.getenv('SERVER_URL')
PROJECT_NAME = os.getenv('PROJECT_NAME', 'My Project')

# Response models
class TableInfo(BaseModel):
    name: str
    columns: List[str]

class SchemaResponse(BaseModel):
    project_id: str
    report_id: str
    report_name: str
    report_type: str
    tables: List[TableInfo]
    all_table_names: List[str]
    all_column_names: List[str]

class HyperFileResponse(BaseModel):
    success: bool
    message: str
    filename: str
    tables_processed: List[str]
    tableau_upload_success: bool
    datasource_id: Optional[str] = None

# Helper Functions
def connect_to_microstrategy(project_id: str = None):
    """Establish connection to MicroStrategy using credentials utility"""
    try:
        mstr_conn = get_connection(project_id)
        logger.info("Connected to MicroStrategy Project successfully!")
        return mstr_conn
    except Exception as e:
        logger.error(f"Error connecting to MicroStrategy: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to connect to MicroStrategy: {str(e)}")

def extract_table_column_names_from_sql(sql_text: str) -> dict:
    """Extract only table and column names from SQL using OpenAI"""
    try:
        openai.api_key = os.getenv("OPENAI_API_KEY")
        
        prompt = f"""
Given the following SQL query from MicroStrategy, extract and output a JSON with ONLY the table names and column names:
{{
  "tables": [
      {{
         "name": "<table_name>",
         "columns": ["<column_name_1>", "<column_name_2>", ...]
      }}
  ]
}}

Rules:
1. Extract only the actual table names (without schema prefixes if possible)
2. Extract only the column names (clean names without brackets, quotes, or prefixes)
3. If a table name starts with '#' (temporary table), ignore it
4. Focus on base tables from FROM and JOIN clauses
5. Include all columns mentioned in SELECT, WHERE, GROUP BY, ORDER BY clauses

Here is the SQL:
\"\"\"{sql_text}\"\"\"

Output only valid JSON with no additional text or markdown.
"""
        
        response = openai.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a helpful assistant that extracts table and column names from SQL queries."},
                {"role": "user", "content": prompt}
            ],
            temperature=0
        )
        
        schema_str = response.choices[0].message.content.strip()
        
        # Remove markdown code fences if present
        if schema_str.startswith("```"):
            lines = schema_str.splitlines()
            if lines[0].startswith("```"):
                lines = lines[1:]
            if lines and lines[-1].startswith("```"):
                lines = lines[:-1]
            schema_str = "\n".join(lines).strip()
            
        schema_json = json.loads(schema_str)
        return schema_json
    except Exception as e:
        logger.error(f"Error extracting table/column names from SQL via OpenAI: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error extracting names from SQL: {str(e)}")

def clean_column_name(col_name: str) -> str:
    """Clean MicroStrategy column names by removing XML-like tags and special formatting"""
    # Remove XML-like tags (e.g., <pi>From_Date1</pi>)
    cleaned = re.sub(r'<[^>]+>', '', col_name)
    
    # Remove square brackets if present
    cleaned = cleaned.replace('[', '').replace(']', '')
    
    # Remove any leading/trailing whitespace
    cleaned = cleaned.strip()
    
    return cleaned

def extract_column_names_from_freeform_sql(sql_query: str) -> List[str]:
    """Extract column names from freeform SQL by analyzing the SELECT clause"""
    try:
        # Find the SELECT clause - everything between SELECT and the first FROM
        select_match = re.search(r'SELECT\s+(.*?)\s+FROM', sql_query, re.IGNORECASE | re.DOTALL)
        if not select_match:
            return []
        
        select_clause = select_match.group(1)
        
        # Split by comma and clean each column
        column_parts = select_clause.split(',')
        column_names = []
        
        for part in column_parts:
            part = part.strip()
            
            # Handle AS aliases
            if ' AS ' in part.upper():
                # Take the alias part
                alias_part = part.upper().split(' AS ')[-1].strip()
                column_names.append(clean_column_name(alias_part))
            else:
                # Take the last part after dots (for table.column references)
                if '.' in part:
                    col_part = part.split('.')[-1].strip()
                else:
                    col_part = part
                
                # Remove function calls like SUM(), COUNT(), etc.
                col_part = re.sub(r'^[A-Z]+\s*\(\s*', '', col_part)
                col_part = re.sub(r'\s*\)\s*$', '', col_part)
                
                column_names.append(clean_column_name(col_part))
        
        # Filter out empty names
        return [name for name in column_names if name]
        
    except Exception as e:
        logger.error(f"Error extracting column names from freeform SQL: {str(e)}")
        return []

# Database and Hyper File Functions
def connect_to_database():
    """Establish connection to Azure SQL Database"""
    try:
        server = os.getenv('SERVER')
        db_user = os.getenv('DB_USER')
        password = os.getenv('PASS')
        database = os.getenv('DATABASE')
        
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={db_user};PWD={password}'
        conn = pyodbc.connect(conn_str)
        logger.info("Successfully connected to the database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to database: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database connection failed: {str(e)}")

def get_table_schema(conn, table_name):
    """Get the schema name for a table"""
    try:
        cursor = conn.cursor()
        query = """
        SELECT SCHEMA_NAME(schema_id) as schema_name
        FROM sys.tables
        WHERE name = ?
        """
        cursor.execute(query, (table_name,))
        result = cursor.fetchone()
        return result[0] if result else 'dbo'  # Default to 'dbo' if not found
    except Exception as e:
        logger.error(f"Error getting schema for table {table_name}: {str(e)}")
        return 'dbo'

def verify_tables_exist(conn, table_names):
    """Verify that required tables exist in the database"""
    try:
        cursor = conn.cursor()
        existing_tables = []
        missing_tables = []
        
        for table_name in table_names:
            schema = get_table_schema(conn, table_name)
            query = """
            SELECT COUNT(*)
            FROM sys.tables t
            JOIN sys.schemas s ON t.schema_id = s.schema_id
            WHERE t.name = ? AND s.name = ?
            """
            cursor.execute(query, (table_name, schema))
            if cursor.fetchone()[0] > 0:
                existing_tables.append(f"[{schema}].[{table_name}]")
            else:
                missing_tables.append(table_name)
        
        if missing_tables:
            logger.warning(f"Missing tables: {', '.join(missing_tables)}")
            return existing_tables, missing_tables
            
        logger.info(f"Found all required tables: {', '.join(existing_tables)}")
        return existing_tables, []
    except Exception as e:
        logger.error(f"Error verifying tables: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error verifying tables: {str(e)}")

def fetch_table_data(conn, table_name):
    """Fetch data from specified table"""
    try:
        schema = get_table_schema(conn, table_name)
        query = f"SELECT * FROM [{schema}].[{table_name}]"
        df = pd.read_sql(query, conn)
        # Convert all columns to string type
        df = df.astype(str)
        logger.info(f"Successfully fetched data from [{schema}].[{table_name}] - {len(df)} rows")
        return df
    except Exception as e:
        logger.error(f"Error fetching data from table {table_name}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching data from table {table_name}: {str(e)}")

def find_common_columns(tables_dict):
    """Find common columns across tables for potential joins"""
    if len(tables_dict) < 2:
        return []
    
    table_columns = [set(df.columns) for df in tables_dict.values()]
    common_columns = set.intersection(*table_columns)
    
    # Filter out common columns that are likely join keys
    join_candidates = []
    for col in common_columns:
        col_lower = col.lower()
        if any(keyword in col_lower for keyword in ['id', 'key', 'code', 'number']):
            join_candidates.append(col)
    
    return list(join_candidates)

def merge_tables_into_one(table_data_dict, report_name):
    """Merge multiple tables into one denormalized table for Tableau Cloud"""
    try:
        if len(table_data_dict) == 1:
            # Only one table, return as is
            table_name = list(table_data_dict.keys())[0]
            return table_name, list(table_data_dict.values())[0]
        
        logger.info(f"Multiple tables detected ({len(table_data_dict)}), attempting to merge...")
        
        # Find potential join columns
        common_columns = find_common_columns(table_data_dict)
        logger.info(f"Common columns found: {common_columns}")
        
        tables_list = list(table_data_dict.items())
        merged_df = None
        merge_info = []
        
        if common_columns:
            # Attempt to join tables on common columns
            merged_df = tables_list[0][1].copy()  # Start with first table
            merge_info.append(f"Base table: {tables_list[0][0]} ({len(merged_df)} rows)")
            
            for table_name, df in tables_list[1:]:
                try:
                    # Use the first common column as join key
                    join_key = common_columns[0]
                    
                    # Add table name prefix to avoid column conflicts
                    df_prefixed = df.copy()
                    for col in df_prefixed.columns:
                        if col != join_key:
                            df_prefixed.rename(columns={col: f"{table_name}_{col}"}, inplace=True)
                    
                    # Perform left join
                    merged_df = merged_df.merge(df_prefixed, on=join_key, how='left')
                    merge_info.append(f"Joined: {table_name} on '{join_key}' ({len(df)} rows)")
                    
                except Exception as e:
                    logger.warning(f"Failed to join table {table_name}: {str(e)}")
                    # If join fails, concatenate as separate rows instead
                    df_aligned = df.reindex(columns=merged_df.columns, fill_value='')
                    merged_df = pd.concat([merged_df, df_aligned], ignore_index=True)
                    merge_info.append(f"Concatenated: {table_name} ({len(df)} rows)")
        else:
            # No common columns, concatenate all tables vertically
            logger.info("No common join columns found, concatenating tables vertically...")
            
            # Get all unique columns across all tables
            all_columns = set()
            for df in table_data_dict.values():
                all_columns.update(df.columns)
            all_columns = sorted(list(all_columns))
            
            # Align all tables to have same columns
            aligned_dfs = []
            for table_name, df in table_data_dict.items():
                df_aligned = df.reindex(columns=all_columns, fill_value='')
                # Add a source table column to track origin
                df_aligned['source_table'] = table_name
                aligned_dfs.append(df_aligned)
                merge_info.append(f"Added: {table_name} ({len(df)} rows)")
            
            merged_df = pd.concat(aligned_dfs, ignore_index=True)
        
        # Generate a clean table name
        clean_report_name = re.sub(r'[^a-zA-Z0-9_]', '_', report_name)
        final_table_name = f"{clean_report_name}_Combined"
        
        logger.info(f"Successfully merged {len(table_data_dict)} tables into '{final_table_name}' with {len(merged_df)} total rows")
        logger.info(f"Merge summary: {'; '.join(merge_info)}")
        
        return final_table_name, merged_df
        
    except Exception as e:
        logger.error(f"Error merging tables: {str(e)}")
        # Fallback: return the largest table
        largest_table = max(table_data_dict.items(), key=lambda x: len(x[1]))
        logger.info(f"Fallback: Using largest table '{largest_table[0]}' with {len(largest_table[1])} rows")
        return largest_table[0], largest_table[1]

def detect_join_columns(table_data_dict):
    """Detect potential join columns between tables for foreign key relationships"""
    join_relationships = []
    table_names = list(table_data_dict.keys())
    
    # Common patterns for join columns
    join_patterns = ['id', 'key', 'code', 'number', '_id', '_key']
    
    for i, table1 in enumerate(table_names):
        df1 = table_data_dict[table1]
        for j, table2 in enumerate(table_names):
            if i >= j:  # Avoid duplicate pairs and self-joins
                continue
                
            df2 = table_data_dict[table2]
            
            # Find common columns
            common_cols = set(df1.columns) & set(df2.columns)
            
            for col in common_cols:
                col_lower = col.lower()
                # Check if it looks like a join column
                if any(pattern in col_lower for pattern in join_patterns):
                    # Verify data overlap (should have some matching values)
                    df1_values = set(df1[col].dropna().unique())
                    df2_values = set(df2[col].dropna().unique())
                    overlap = len(df1_values & df2_values)
                    
                    if overlap > 0:  # Has matching values
                        join_relationships.append({
                            'from_table': table1,
                            'from_column': col,
                            'to_table': table2,
                            'to_column': col,
                            'overlap_count': overlap
                        })
                        logger.info(f"Detected potential relationship: {table1}.{col} -> {table2}.{col} (overlap: {overlap})")
    
    return join_relationships

def create_hyper_file_with_relationships(table_data_dict, output_file, report_name="Report"):
    """Create Hyper file with multiple tables and proper foreign key relationships for Tableau Cloud"""
    try:
        if os.path.exists(output_file):
            try:
                os.remove(output_file)
                logger.info(f"Removed existing file: {output_file}")
            except Exception as e:
                logger.warning(f"Could not remove existing file: {str(e)}")

        # Detect relationships between tables
        relationships = detect_join_columns(table_data_dict)
        logger.info(f"Found {len(relationships)} potential relationships")

        # Use file format version 2 for better compatibility
        with HyperProcess(Telemetry.SEND_USAGE_DATA_TO_TABLEAU, 
                         parameters={"default_database_version": "2"}) as hyper:
            with HyperConnection(hyper.endpoint, output_file, CreateMode.CREATE_AND_REPLACE) as connection:
                
                table_definitions = {}
                
                # Create all tables in the default schema (not Extract schema for multi-table)
                for table_name, df in table_data_dict.items():
                    if df.empty:
                        logger.warning(f"Skipping empty table: {table_name}")
                        continue
                    
                    # Create table definition without schema (default to 'public')
                    table_def = TableDefinition(
                        TableName(table_name),
                        [TableDefinition.Column(column, SqlType.text()) for column in df.columns]
                    )
                    
                    # Create table
                    connection.catalog.create_table(table_def)
                    table_definitions[table_name] = table_def
                    
                    # Insert data
                    with Inserter(connection, table_def) as inserter:
                        for _, row in df.iterrows():
                            row_values = [str(val) for val in row.values]
                            inserter.add_row(row_values)
                        inserter.execute()
                    
                    logger.info(f"Added table '{table_name}' with {len(df)} rows")
                
                # Create foreign key relationships if detected
                for rel in relationships:
                    try:
                        # Create foreign key constraint
                        fk_sql = f'''
                        ALTER TABLE "{rel['to_table']}" 
                        ADD CONSTRAINT "fk_{rel['from_table']}_{rel['to_table']}_{rel['from_column']}"
                        FOREIGN KEY ("{rel['to_column']}") 
                        REFERENCES "{rel['from_table']}" ("{rel['from_column']}")
                        '''
                        connection.execute_command(fk_sql)
                        logger.info(f"Created foreign key: {rel['from_table']}.{rel['from_column']} -> {rel['to_table']}.{rel['to_column']}")
                    except Exception as e:
                        logger.warning(f"Could not create foreign key relationship: {str(e)}")
                        # Continue without the relationship
                
        logger.info(f"Successfully created multi-table Hyper file: {output_file}")
        return True
    except Exception as e:
        logger.error(f"Error creating multi-table Hyper file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating Hyper file: {str(e)}")

def create_basic_tds_content(hyper_filename, datasource_name, table_data_dict):
    """Create basic TDS XML content for multi-table datasource"""
    
    # Get the first table as the primary table
    primary_table = list(table_data_dict.keys())[0]
    
    # Build column definitions from the primary table
    columns_xml = ""
    df = table_data_dict[primary_table]
    for col in df.columns:
        columns_xml += f'''
    <column caption='{col}' datatype='string' name='[{col}]' role='dimension' type='nominal' />'''
    
    # Basic TDS template for multi-table datasource
    tds_content = f'''<?xml version='1.0' encoding='utf-8' ?>
<datasource formatted-name='{datasource_name}' inline='true' source-platform='win' version='18.1' xmlns:user='http://www.tableausoftware.com/xml/user'>
  <connection class='hyper' dbname='{hyper_filename}' schema='' tablename=''>
    <relation type='table' table='[{primary_table}]' />
  </connection>
  <aliases enabled='yes' />
  {columns_xml}
  <column datatype='integer' name='[Number of Records]' role='measure' type='quantitative' user:auto-column='numrec'>
    <calculation class='tableau' formula='1' />
  </column>
  <layout dim-ordering='alphabetic' dim-percentage='0.5' measure-ordering='alphabetic' measure-percentage='0.4' show-structure='true' />
  <semantic-values>
    <semantic-value key='[Country].[Name]' value='&quot;United States&quot;' />
  </semantic-values>
</datasource>'''
    
    return tds_content

def create_tdsx_file(hyper_file_path, datasource_name, table_data_dict, output_tdsx_path):
    """Create a TDSX file containing the multi-table Hyper file"""
    try:
        # Create temporary directory for TDSX contents
        temp_dir = tempfile.mkdtemp()
        logger.info(f"Creating TDSX in temp directory: {temp_dir}")
        
        # Get just the filename without path
        hyper_filename = os.path.basename(hyper_file_path)
        
        # Create the Data/Extracts directory structure
        data_dir = os.path.join(temp_dir, "Data")
        extracts_dir = os.path.join(data_dir, "Extracts")
        os.makedirs(extracts_dir, exist_ok=True)
        
        # Copy Hyper file to the correct location in TDSX structure
        hyper_dest_path = os.path.join(extracts_dir, hyper_filename)
        shutil.copy2(hyper_file_path, hyper_dest_path)
        logger.info(f"Copied Hyper file to: {hyper_dest_path}")
        
        # Verify the Hyper file was copied correctly
        if not os.path.exists(hyper_dest_path):
            raise Exception(f"Failed to copy Hyper file to TDSX structure")
        
        # Create TDS file content with proper reference to the Hyper file
        tds_content = create_basic_tds_content(hyper_filename, datasource_name, table_data_dict)
        
        # Clean datasource name for filename
        clean_name = re.sub(r'[^a-zA-Z0-9_\-]', '_', datasource_name)
        tds_filename = f"{clean_name}.tds"
        tds_path = os.path.join(temp_dir, tds_filename)
        
        # Write TDS file
        with open(tds_path, 'w', encoding='utf-8') as f:
            f.write(tds_content)
        logger.info(f"Created TDS file: {tds_path}")
        
        # Create TDSX (zip file) with proper structure
        with zipfile.ZipFile(output_tdsx_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Add TDS file at root level
            zipf.write(tds_path, tds_filename)
            logger.info(f"Added TDS to TDSX: {tds_filename}")
            
            # Add Hyper file with correct path structure
            arcname = f"Data/Extracts/{hyper_filename}"
            zipf.write(hyper_dest_path, arcname)
            logger.info(f"Added Hyper to TDSX: {arcname}")
        
        # Verify TDSX was created
        if not os.path.exists(output_tdsx_path):
            raise Exception("TDSX file was not created")
            
        # Log TDSX contents for debugging
        with zipfile.ZipFile(output_tdsx_path, 'r') as zipf:
            logger.info(f"TDSX contents: {zipf.namelist()}")
        
        # Cleanup temp directory
        shutil.rmtree(temp_dir)
        
        logger.info(f"Successfully created TDSX file: {output_tdsx_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error creating TDSX file: {str(e)}")
        # Cleanup on error
        if 'temp_dir' in locals() and os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        raise HTTPException(status_code=500, detail=f"Error creating TDSX file: {str(e)}")

def upload_tdsx_to_tableau_cloud(tdsx_file_path, datasource_name):
    """Upload TDSX file to Tableau Cloud (supports multi-table)"""
    try:
        # Set up authentication
        tableau_auth = TSC.PersonalAccessTokenAuth(
            TOKEN_NAME,
            TOKEN_SECRET,
            SITE_ID
        )
        
        # Create server object
        server = TSC.Server(SERVER_URL)
        
        # Sign in to server
        logger.info("Signing in to Tableau Cloud...")
        server.auth.sign_in(tableau_auth)
        
        # Find the project
        logger.info(f"Looking for project: {PROJECT_NAME}")
        all_projects, pagination_item = server.projects.get()
        project = next((p for p in all_projects if p.name == PROJECT_NAME), None)
        
        if not project:
            logger.error(f"Project '{PROJECT_NAME}' not found")
            raise HTTPException(status_code=404, detail=f"Tableau project '{PROJECT_NAME}' not found")
        
        # Create datasource item
        datasource = TSC.DatasourceItem(project.id, datasource_name)
        
        # Upload the TDSX file (this supports multi-table!)
        logger.info(f"Uploading {tdsx_file_path} to Tableau Cloud...")
        datasource = server.datasources.publish(
            datasource,
            tdsx_file_path,
            mode=TSC.Server.PublishMode.CreateNew
        )
        
        logger.info(f"Successfully uploaded datasource: {datasource.name}")
        logger.info(f"Datasource ID: {datasource.id}")
        
        # Sign out
        server.auth.sign_out()
        
        return datasource.id
        
    except Exception as e:
        logger.error(f"Error uploading TDSX to Tableau Cloud: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to upload TDSX to Tableau Cloud: {str(e)}")

def update_hyper_file(hyper_file_path, table_data_dict, operation="append"):
    """Update existing Hyper file with new data"""
    try:
        if not os.path.exists(hyper_file_path):
            raise HTTPException(status_code=404, detail=f"Hyper file not found: {hyper_file_path}")
        
        with HyperProcess(Telemetry.SEND_USAGE_DATA_TO_TABLEAU,
                         parameters={"default_database_version": "2"}) as hyper:
            with HyperConnection(hyper.endpoint, hyper_file_path, CreateMode.NONE) as connection:
                
                for table_name, df in table_data_dict.items():
                    if df.empty:
                        logger.warning(f"Skipping empty table: {table_name}")
                        continue
                    
                    table_full_name = f'"Extract"."{table_name}"'
                    
                    # Check if table exists
                    try:
                        result = connection.execute_scalar_query(f'SELECT COUNT(*) FROM {table_full_name}')
                        existing_rows = result
                        logger.info(f"Table {table_name} exists with {existing_rows} rows")
                    except:
                        logger.warning(f"Table {table_name} does not exist in Hyper file")
                        continue
                    
                    if operation == "replace":
                        # Delete all existing data
                        connection.execute_command(f'DELETE FROM {table_full_name}')
                        logger.info(f"Cleared all data from table {table_name}")
                    elif operation == "update_recent":
                        # Delete recent data (last 7 days worth if date column exists)
                        # This is a placeholder - would need date column detection
                        pass
                    
                    # Insert new data
                    with Inserter(connection, TableName('Extract', table_name)) as inserter:
                        for _, row in df.iterrows():
                            row_values = [str(val) for val in row.values]
                            inserter.add_row(row_values)
                        inserter.execute()
                    
                    logger.info(f"Updated table '{table_name}' with {len(df)} rows (operation: {operation})")
                
        logger.info(f"Successfully updated Hyper file: {hyper_file_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error updating Hyper file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error updating Hyper file: {str(e)}")

def create_hyper_file_single_table(table_data_dict, output_file, report_name="Report"):
    """Create Hyper file with single merged table for maximum compatibility"""
    try:
        if os.path.exists(output_file):
            try:
                os.remove(output_file)
                logger.info(f"Removed existing file: {output_file}")
            except Exception as e:
                logger.warning(f"Could not remove existing file: {str(e)}")

        # Merge multiple tables into one for maximum compatibility
        final_table_name, final_df = merge_tables_into_one(table_data_dict, report_name)
        
        if final_df.empty:
            raise HTTPException(status_code=404, detail="No data available to create Hyper file")

        # Use file format version 2 for better compatibility
        with HyperProcess(Telemetry.SEND_USAGE_DATA_TO_TABLEAU,
                         parameters={"default_database_version": "2"}) as hyper:
            with HyperConnection(hyper.endpoint, output_file, CreateMode.CREATE_AND_REPLACE) as connection:
                
                # Create Extract schema for Tableau compatibility
                connection.catalog.create_schema('Extract')
                
                # Create table definition in Extract schema
                table_def = TableDefinition(
                    TableName('Extract', final_table_name),
                    [TableDefinition.Column(column, SqlType.text()) for column in final_df.columns]
                )
                
                # Create table
                connection.catalog.create_table(table_def)
                
                # Insert data using Inserter
                with Inserter(connection, table_def) as inserter:
                    for _, row in final_df.iterrows():
                        # Convert all values to strings
                        row_values = [str(val) for val in row.values]
                        inserter.add_row(row_values)
                    inserter.execute()
                
                logger.info(f"Added table 'Extract.{final_table_name}' to Hyper file with {len(final_df)} rows")

        logger.info(f"Successfully created single-table Hyper file: {output_file}")
        return True
    except Exception as e:
        logger.error(f"Error creating single-table Hyper file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating Hyper file: {str(e)}")

# Keep the original function name but make it configurable
def create_hyper_file(table_data_dict, output_file, report_name="Report", multi_table=False):
    """Create Hyper file - single table (default) or multi-table mode"""
    if multi_table and len(table_data_dict) > 1:
        return create_hyper_file_with_relationships(table_data_dict, output_file, report_name)
    else:
        return create_hyper_file_single_table(table_data_dict, output_file, report_name)

def upload_hyper_to_tableau_cloud(hyper_file_path, datasource_name):
    """Upload single-table Hyper file directly to Tableau Cloud"""
    try:
        # Set up authentication
        tableau_auth = TSC.PersonalAccessTokenAuth(
            TOKEN_NAME,
            TOKEN_SECRET,
            SITE_ID
        )
        
        # Create server object
        server = TSC.Server(SERVER_URL)
        
        # Sign in to server
        logger.info("Signing in to Tableau Cloud...")
        server.auth.sign_in(tableau_auth)
        
        # Find the project
        logger.info(f"Looking for project: {PROJECT_NAME}")
        all_projects, pagination_item = server.projects.get()
        project = next((p for p in all_projects if p.name == PROJECT_NAME), None)
        
        if not project:
            logger.error(f"Project '{PROJECT_NAME}' not found")
            raise HTTPException(status_code=404, detail=f"Tableau project '{PROJECT_NAME}' not found")
        
        # Create datasource item
        datasource = TSC.DatasourceItem(project.id, datasource_name)
        
        # Upload the single-table Hyper file directly
        logger.info(f"Uploading single-table Hyper file {hyper_file_path} to Tableau Cloud...")
        datasource = server.datasources.publish(
            datasource,
            hyper_file_path,
            mode=TSC.Server.PublishMode.CreateNew
        )
        
        logger.info(f"Successfully uploaded datasource: {datasource.name}")
        logger.info(f"Datasource ID: {datasource.id}")
        
        # Sign out
        server.auth.sign_out()
        
        return datasource.id
        
    except Exception as e:
        logger.error(f"Error uploading single-table Hyper to Tableau Cloud: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to upload single-table Hyper to Tableau Cloud: {str(e)}")

# API Endpoints
@router.get("/table-column-names", response_model=SchemaResponse)
async def get_table_column_names(
    project_id: str = Query(..., description="MicroStrategy Project ID"),
    report_id: str = Query(..., description="MicroStrategy Report ID")
):
    """
    Get table names and column names from a MicroStrategy report
    
    This endpoint extracts table and column names from a specific MicroStrategy report.
    Both project_id and report_id are required because:
    - project_id: Identifies which MicroStrategy project to connect to
    - report_id: Identifies which specific report to extract schema from
    
    The extraction is done by analyzing the SQL query generated by the report.
    
    Query Parameters:
    - project_id: MicroStrategy Project ID (required)
    - report_id: MicroStrategy Report ID (required)
    
    Returns:
    - Complete schema with tables and their columns
    - Flattened lists of all table names and column names
    - Report metadata (name, type)
    """
    try:
        # Validate inputs
        if not project_id or not report_id:
            raise HTTPException(
                status_code=400, 
                detail="Both project_id and report_id are required. project_id identifies the MicroStrategy project, and report_id identifies the specific report to extract schema from."
            )
        
        # Connect to MicroStrategy
        logger.info(f"Connecting to MicroStrategy project: {project_id}")
        mstr_conn = connect_to_microstrategy(project_id)
        
        # Load the report
        logger.info(f"Loading report: {report_id}")
        report_obj = Report(connection=mstr_conn, id=report_id)
        mstr_sql = report_obj.sql
        
        # Check if this is a freeform SQL report
        is_freeform_sql = str(report_obj.ext_type) == "ExtendedType.CUSTOM_SQL_FREE_FORM"
        report_type = "Free Form SQL Report" if is_freeform_sql else "Normal Report"
        
        logger.info(f"Processing {report_type} - Report: {report_obj.name}")
        
        if is_freeform_sql:
            # For freeform SQL, extract column names directly from SELECT clause
            raw_column_names = extract_column_names_from_freeform_sql(mstr_sql)
            
            # Create a single table entry with the report name
            table_name = report_obj.name.replace(" ", "_").replace("-", "_")
            table_name = re.sub(r'[^a-zA-Z0-9_]', '', table_name)
            
            tables = [TableInfo(name=table_name, columns=raw_column_names)]
            all_table_names = [table_name]
            all_column_names = raw_column_names
            
        else:
            # For normal reports, extract schema from SQL
            schema = extract_table_column_names_from_sql(mstr_sql)
            
            # Convert to response format
            tables = []
            all_table_names = []
            all_column_names = []
            
            for table in schema.get("tables", []):
                table_name = table.get("name", "").split(".")[-1]  # Remove schema prefix
                columns = table.get("columns", [])
                
                tables.append(TableInfo(name=table_name, columns=columns))
                all_table_names.append(table_name)
                all_column_names.extend(columns)
            
            # Remove duplicate column names while preserving order
            seen = set()
            all_column_names = [col for col in all_column_names if not (col in seen or seen.add(col))]
        
        return SchemaResponse(
            project_id=project_id,
            report_id=report_id,
            report_name=report_obj.name,
            report_type=report_type,
            tables=tables,
            all_table_names=all_table_names,
            all_column_names=all_column_names
        )
            
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error extracting schema names: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to extract schema names: {str(e)}")

@router.get("/create-hyper-file")
async def create_hyper_file_from_report(
    project_id: str = Query(..., description="MicroStrategy Project ID"),
    report_id: str = Query(..., description="MicroStrategy Report ID"),
    upload_to_tableau: bool = Query(True, description="Whether to upload to Tableau Cloud after creating Hyper file"),
    multi_table: bool = Query(False, description="Create multi-table Hyper file (experimental: may require manual upload)")
):
    """
    Create a Tableau Hyper file from MicroStrategy report and optionally upload to Tableau Cloud
    
    This endpoint:
    1. Extracts table names from a MicroStrategy report
    2. Queries those tables from the SQL database
    3. Creates a Tableau Hyper file with the report name as filename
    4. Optionally uploads the file to Tableau Cloud
    5. Returns the file for download
    
    Query Parameters:
    - project_id: MicroStrategy Project ID (required)
    - report_id: MicroStrategy Report ID (required)
    - upload_to_tableau: Whether to upload to Tableau Cloud (default: true)
    - multi_table: Create multi-table Hyper file (default: false)
    
    Table modes:
    - False (DEFAULT): Merges all tables into one denormalized table (reliable upload)
    - True: Creates separate tables with relationships (may require manual upload)
    
    Recommendation: Use default single-table mode for reliable Tableau Cloud uploads.
    For multi-table preservation, use multi_table=true but upload manually via Tableau Desktop.
    
    Returns:
    - Hyper file download with filename based on report name
    - Upload status and Tableau datasource ID if uploaded
    """
    temp_file_path = None
    temp_tdsx_path = None
    conn = None
    
    try:
        # Validate inputs
        if not project_id or not report_id:
            raise HTTPException(
                status_code=400, 
                detail="Both project_id and report_id are required"
            )
        
        # Step 1: Extract table names from MicroStrategy report
        logger.info(f"Extracting schema from MicroStrategy report: {report_id}")
        
        # Connect to MicroStrategy
        mstr_conn = connect_to_microstrategy(project_id)
        
        # Load the report
        report_obj = Report(connection=mstr_conn, id=report_id)
        mstr_sql = report_obj.sql
        
        # Check if this is a freeform SQL report
        is_freeform_sql = str(report_obj.ext_type) == "ExtendedType.CUSTOM_SQL_FREE_FORM"
        report_type = "Free Form SQL Report" if is_freeform_sql else "Normal Report"
        
        logger.info(f"Processing {report_type} - Report: {report_obj.name}")
        
        # Extract table names
        table_names = []
        if is_freeform_sql:
            # For freeform SQL, we can't extract individual tables
            # We'll create a single "table" with the report name
            logger.info("Freeform SQL report detected - will execute the SQL directly")
            table_names = []  # Will handle this differently
        else:
            # For normal reports, extract schema from SQL
            schema = extract_table_column_names_from_sql(mstr_sql)
            table_names = [table.get("name", "").split(".")[-1] for table in schema.get("tables", [])]
        
        if not table_names and not is_freeform_sql:
            raise HTTPException(
                status_code=404, 
                detail="No tables found in the MicroStrategy report"
            )
        
        # Step 2: Connect to SQL database
        logger.info("Connecting to SQL database")
        conn = connect_to_database()
        
        # Step 3: Handle data extraction
        table_data = {}
        processed_tables = []
        
        if is_freeform_sql:
            # For freeform SQL, execute the SQL directly
            logger.info("Executing freeform SQL query")
            try:
                # Clean the SQL (remove MicroStrategy metadata)
                clean_sql = mstr_sql
                # Remove analytical engine comments if present
                if "[Analytical engine calculation steps:" in clean_sql:
                    clean_sql = clean_sql.split("[Analytical engine calculation steps:")[0].strip()
                
                df = pd.read_sql(clean_sql, conn)
                df = df.astype(str)  # Convert all to string
                
                # Use report name as table name (cleaned)
                table_name = report_obj.name.replace(" ", "_").replace("-", "_")
                table_name = re.sub(r'[^a-zA-Z0-9_]', '', table_name)
                table_data[table_name] = df
                processed_tables.append(table_name)
                
                logger.info(f"Executed freeform SQL - {len(df)} rows retrieved")
                
            except Exception as e:
                logger.error(f"Error executing freeform SQL: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Error executing freeform SQL: {str(e)}")
        else:
            # For normal reports, verify tables exist and fetch data
            existing_tables, missing_tables = verify_tables_exist(conn, table_names)
            
            if missing_tables:
                logger.warning(f"Some tables not found: {missing_tables}")
                # Continue with existing tables only
                table_names = [t.split('.')[-1].strip('[]') for t in existing_tables]
            
            if not table_names:
                raise HTTPException(
                    status_code=404, 
                    detail="None of the required tables exist in the database"
                )
            
            # Fetch data from all existing tables
            for table_name in table_names:
                logger.info(f"Fetching data from table: {table_name}")
                table_data[table_name] = fetch_table_data(conn, table_name)
                processed_tables.append(table_name)
        
        if not table_data:
            raise HTTPException(
                status_code=404, 
                detail="No data could be retrieved from any tables"
            )
        
        # Step 4: Create Hyper file
        # Generate filename based on report name
        safe_report_name = re.sub(r'[^a-zA-Z0-9_\-]', '_', report_obj.name)
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{safe_report_name}_{timestamp}.hyper"
        
        # Generate filename based on report name
        safe_report_name = re.sub(r'[^a-zA-Z0-9_\-]', '_', report_obj.name)
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{safe_report_name}_{timestamp}.hyper"
        
        # Create temporary file
        temp_dir = tempfile.gettempdir()
        temp_file_path = os.path.join(temp_dir, filename)
        
        logger.info(f"Creating Hyper file: {filename}")
        create_hyper_file(table_data, temp_file_path, report_obj.name, multi_table)
        
        # Step 5: Handle upload and file preparation
        tableau_upload_success = False
        datasource_id = None
        final_download_path = temp_file_path
        final_filename = filename
        
        if upload_to_tableau:
            try:
                datasource_name = f"{report_obj.name} - {timestamp}"
                
                if multi_table and len(table_data) > 1:
                    # Multi-table mode: Download only (upload manually)
                    logger.warning("Multi-table mode: Skipping automatic upload. Download file and upload manually via Tableau Desktop.")
                    tableau_upload_success = False
                else:
                    # Single table - upload Hyper directly
                    datasource_id = upload_hyper_to_tableau_cloud(temp_file_path, datasource_name)
                    tableau_upload_success = True
                    logger.info(f"Successfully uploaded to Tableau Cloud with datasource ID: {datasource_id}")
                
            except Exception as e:
                logger.error(f"Failed to upload to Tableau Cloud: {str(e)}")
                # Continue with file download even if upload fails
                tableau_upload_success = False
        
        # Step 6: Verify file creation
        if not os.path.exists(final_download_path):
            raise HTTPException(
                status_code=500, 
                detail="File was not created successfully"
            )
        
        logger.info(f"File created successfully: {final_filename}")
        
        # Add custom headers with processing info
        headers = {
            "Content-Disposition": f"attachment; filename={final_filename}",
            "X-Tables-Processed": ','.join(processed_tables),
            "X-Tableau-Upload": str(tableau_upload_success),
            "X-Multi-Table-Mode": str(multi_table),
            "X-File-Type": "HYPER",
        }
        
        if datasource_id:
            headers["X-Tableau-Datasource-ID"] = datasource_id
        
        if multi_table and len(table_data) > 1 and upload_to_tableau:
            headers["X-Upload-Note"] = "Multi-table mode: Manual upload required"
        
        # Return the file for download
        return FileResponse(
            path=final_download_path,
            media_type='application/octet-stream',
            filename=final_filename,
            headers=headers
        )
        
    except HTTPException as he:
        # Clean up temp files on error
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
            except:
                pass
        if temp_tdsx_path and os.path.exists(temp_tdsx_path):
            try:
                os.remove(temp_tdsx_path)
            except:
                pass
        raise he
    except Exception as e:
        # Clean up temp files on error
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
            except:
                pass
        if temp_tdsx_path and os.path.exists(temp_tdsx_path):
            try:
                os.remove(temp_tdsx_path)
            except:
                pass
        
        logger.error(f"Error creating Hyper file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create Hyper file: {str(e)}")
    finally:
        # Close database connection
        if conn:
            try:
                conn.close()
            except:
                pass

@router.post("/update-hyper-file")
async def update_existing_hyper_file(
    project_id: str = Query(..., description="MicroStrategy Project ID"),
    report_id: str = Query(..., description="MicroStrategy Report ID"), 
    hyper_file_path: str = Query(..., description="Path to existing Hyper file to update"),
    operation: str = Query("append", description="Update operation: 'append', 'replace', or 'update_recent'")
):
    """
    Update an existing Hyper file with fresh data from MicroStrategy report
    
    This endpoint:
    1. Extracts table names from a MicroStrategy report
    2. Queries those tables from the SQL database
    3. Updates the existing Hyper file with new data
    4. Supports different update operations
    
    Query Parameters:
    - project_id: MicroStrategy Project ID (required)
    - report_id: MicroStrategy Report ID (required)
    - hyper_file_path: Full path to existing Hyper file (required)
    - operation: How to update the data (default: "append")
      * "append": Add new data to existing tables
      * "replace": Clear existing data and replace with new data
      * "update_recent": Replace recent data (implementation depends on date columns)
    
    Returns:
    - JSON response with update status and details
    """
    conn = None
    
    try:
        # Validate inputs
        if not project_id or not report_id or not hyper_file_path:
            raise HTTPException(
                status_code=400, 
                detail="project_id, report_id, and hyper_file_path are all required"
            )
        
        if operation not in ["append", "replace", "update_recent"]:
            raise HTTPException(
                status_code=400,
                detail="operation must be one of: 'append', 'replace', 'update_recent'"
            )
        
        # Check if Hyper file exists
        if not os.path.exists(hyper_file_path):
            raise HTTPException(
                status_code=404,
                detail=f"Hyper file not found: {hyper_file_path}"
            )
        
        # Step 1: Extract table names from MicroStrategy report
        logger.info(f"Extracting schema from MicroStrategy report: {report_id}")
        
        # Connect to MicroStrategy
        mstr_conn = connect_to_microstrategy(project_id)
        
        # Load the report
        report_obj = Report(connection=mstr_conn, id=report_id)
        mstr_sql = report_obj.sql
        
        # Check if this is a freeform SQL report
        is_freeform_sql = str(report_obj.ext_type) == "ExtendedType.CUSTOM_SQL_FREE_FORM"
        report_type = "Free Form SQL Report" if is_freeform_sql else "Normal Report"
        
        logger.info(f"Processing {report_type} - Report: {report_obj.name}")
        
        # Extract table names
        table_names = []
        if is_freeform_sql:
            # For freeform SQL, we can't extract individual tables for updates
            raise HTTPException(
                status_code=400,
                detail="Update operation not supported for freeform SQL reports. Use create-hyper-file instead."
            )
        else:
            # For normal reports, extract schema from SQL
            schema = extract_table_column_names_from_sql(mstr_sql)
            table_names = [table.get("name", "").split(".")[-1] for table in schema.get("tables", [])]
        
        if not table_names:
            raise HTTPException(
                status_code=404, 
                detail="No tables found in the MicroStrategy report"
            )
        
        # Step 2: Connect to SQL database
        logger.info("Connecting to SQL database")
        conn = connect_to_database()
        
        # Step 3: Fetch fresh data from tables
        existing_tables, missing_tables = verify_tables_exist(conn, table_names)
        
        if missing_tables:
            logger.warning(f"Some tables not found: {missing_tables}")
            # Continue with existing tables only
            table_names = [t.split('.')[-1].strip('[]') for t in existing_tables]
        
        if not table_names:
            raise HTTPException(
                status_code=404, 
                detail="None of the required tables exist in the database"
            )
        
        # Fetch data from all existing tables
        table_data = {}
        processed_tables = []
        
        for table_name in table_names:
            logger.info(f"Fetching data from table: {table_name}")
            table_data[table_name] = fetch_table_data(conn, table_name)
            processed_tables.append(table_name)
        
        if not table_data:
            raise HTTPException(
                status_code=404, 
                detail="No data could be retrieved from any tables"
            )
        
        # Step 4: Update the Hyper file
        logger.info(f"Updating Hyper file: {hyper_file_path} (operation: {operation})")
        update_hyper_file(hyper_file_path, table_data, operation)
        
        # Step 5: Return success response
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": f"Successfully updated Hyper file with {operation} operation",
                "hyper_file_path": hyper_file_path,
                "report_name": report_obj.name,
                "operation": operation,
                "tables_processed": processed_tables,
                "tables_skipped": missing_tables if missing_tables else []
            }
        )
        
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error updating Hyper file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update Hyper file: {str(e)}")
    finally:
        # Close database connection
        if conn:
            try:
                conn.close()
            except:
                pass