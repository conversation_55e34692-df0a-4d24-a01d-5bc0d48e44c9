import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useBrandTheme } from '@/themes/brand-theme-provider';
import { useTheme } from '@/lib/theme-provider';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { ThemeSwitcher } from '@/components/ui/theme-switcher';
import { Theme } from '@/themes/types';

interface ComponentDisplayProps {
  theme: Theme;
  activeTab: string;
}

function ComponentDisplay({ theme, activeTab }: ComponentDisplayProps) {
  return (
    <div className={`p-6 rounded-lg border`} 
      data-mode={activeTab}
      style={{
        backgroundColor: activeTab === 'light' ? theme.colors.background.light : theme.colors.background.dark,
        color: activeTab === 'light' ? theme.colors.text.light : theme.colors.text.dark,
        borderColor: activeTab === 'light' ? theme.colors.border.light : theme.colors.border.dark,
      }}>
      <h3 className="text-xl font-bold mb-4">{theme.name}</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="space-y-6">
          <div>
            <h4 className="text-lg font-semibold mb-2">Buttons</h4>
            <div className="flex flex-wrap gap-2">
              <Button variant="default">Default</Button>
              <Button variant="destructive">Destructive</Button>
              <Button variant="outline" className="btn-outline force-visible">Outline</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="ghost" className="ghost-button-fix" style={{color: activeTab === 'light' ? '#000000' : '#ffffff'}}>Ghost</Button>
              <Button variant="link">Link</Button>
            </div>
          </div>

          <div>
            <h4 className="text-lg font-semibold mb-2">Inputs</h4>
            <div className="space-y-2">
              <Input placeholder="Regular input" />
              <div className="flex items-center space-x-2">
                <Checkbox id="terms" />
                <label
                  htmlFor="terms"
                  className="text-sm font-medium leading-none"
                >
                  Accept terms and conditions
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="airplane-mode" />
                <Label htmlFor="airplane-mode">Airplane Mode</Label>
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-lg font-semibold mb-2">Selection</h4>
            <div className="space-y-2">
              <RadioGroup defaultValue="comfortable">
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="default" id="r1" />
                  <Label htmlFor="r1">Default</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="comfortable" id="r2" />
                  <Label htmlFor="r2">Comfortable</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="compact" id="r3" />
                  <Label htmlFor="r3">Compact</Label>
                </div>
              </RadioGroup>
              
              <Select>
                <SelectTrigger className="w-full force-visible">
                  <SelectValue placeholder="Select a fruit" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="apple">Apple</SelectItem>
                  <SelectItem value="banana">Banana</SelectItem>
                  <SelectItem value="orange">Orange</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="space-y-6">
          <div>
            <h4 className="text-lg font-semibold mb-2">Card</h4>
            <Card>
              <CardHeader>
                <CardTitle>Card Title</CardTitle>
              </CardHeader>
              <CardContent>
                <p>This is sample content inside a card.</p>
              </CardContent>
              <CardFooter>
                <Button>Action</Button>
              </CardFooter>
            </Card>
          </div>

          <div>
            <h4 className="text-lg font-semibold mb-2">Accordion</h4>
            <Accordion type="single" collapsible>
              <AccordionItem value="item-1">
                <AccordionTrigger>Section 1</AccordionTrigger>
                <AccordionContent>
                  Content for section 1 goes here. You can put any components or
                  text in this area.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-2">
                <AccordionTrigger>Section 2</AccordionTrigger>
                <AccordionContent>
                  Content for section 2 goes here. This accordion saves space by
                  showing only one section at a time.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-2">Tabs</h4>
            <Tabs defaultValue="account">
              <TabsList>
                <TabsTrigger value="account">Account</TabsTrigger>
                <TabsTrigger value="password">Password</TabsTrigger>
              </TabsList>
              <TabsContent value="account">Account settings here.</TabsContent>
              <TabsContent value="password">Password settings here.</TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
      
      <div className="mt-6">
        <img 
          src={activeTab === 'light' ? theme.branding.logo.light : theme.branding.logo.dark} 
          alt={`${theme.name} logo`} 
          className="h-10 object-contain" 
          onError={(e) => {
            // Fallback if the logo doesn't exist
            e.currentTarget.src = activeTab === 'light' 
              ? '/icons/logo-light.svg' 
              : '/icons/logo-dark.svg';
          }}
        />
      </div>
    </div>
  );
}

export default function ThemePreview() {
  const { allThemes, setCurrentBrandTheme } = useBrandTheme();
  const { theme, setTheme } = useTheme();
  const [activeTab, setActiveTab] = useState('light');
  const [appliedTheme, setAppliedTheme] = useState<string>('');
  const [showAppliedMessage, setShowAppliedMessage] = useState(false);
  
  // Get current theme ID from localStorage for comparison
  const currentThemeId = localStorage.getItem('catalyst-brand-theme') || 'default';

  // Show feedback message when theme is applied
  useEffect(() => {
    if (appliedTheme) {
      setShowAppliedMessage(true);
      const timer = setTimeout(() => {
        setShowAppliedMessage(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [appliedTheme]);

  return (
    <div className="container mx-auto p-4 theme-page-container">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold theme-page-title">Theme Preview</h1>
          <p className="text-muted-foreground">
            Preview and select a theme for the entire application
          </p>
        </div>
        <Button variant="outline" asChild className="mt-4 md:mt-0 flex items-center gap-2 transition-all hover:bg-primary/10">
          <Link to="/">
            <span>Return to Dashboard</span>
            <small className="text-xs block text-muted-foreground">Your theme will be applied</small>
          </Link>
        </Button>
      </div>

      {showAppliedMessage && (
        <div className="mb-4 p-3 bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100 rounded-md transition-all">
          Theme applied successfully! All pages including the Dashboard will use this theme.
        </div>
      )}

      <div className="mb-8">
        <Card className="theme-settings-card">
          <CardHeader>
            <CardTitle>Theme Settings</CardTitle>
          </CardHeader>
          <CardContent>
            <ThemeSwitcher />
          </CardContent>
        </Card>
      </div>

      <div className="mb-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4 theme-mode-tabs">
            <TabsTrigger value="light" className="theme-mode-tab">Light Mode</TabsTrigger>
            <TabsTrigger value="dark" className="theme-mode-tab">Dark Mode</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid grid-cols-1 gap-8">
        {allThemes.map((theme) => (
          <div key={theme.id} className="relative theme-preview-card">
            {theme.id === currentThemeId && (
              <div className="current-theme-badge">Current Theme</div>
            )}
            <ComponentDisplay theme={theme} activeTab={activeTab} />
            <Button 
              className={`absolute top-4 right-4 theme-apply-button ${theme.id === currentThemeId ? 'theme-current-button' : ''}`}
              onClick={() => {
                setCurrentBrandTheme(theme.id);
                setAppliedTheme(theme.id);
                // Optionally set light/dark mode based on the current preview
                if (activeTab !== 'system') {
                  setTheme(activeTab as 'light' | 'dark');
                }
              }}
            >
              {theme.id === currentThemeId ? 'Applied' : 'Apply Theme'}
            </Button>
          </div>
        ))}
      </div>

      {allThemes.length > 0 && (
        <div className="mt-8 p-4 border rounded-md bg-card">
          <h3 className="text-lg font-semibold mb-2">How themes work</h3>
          <p className="text-muted-foreground mb-2">
            When you select a theme, it will be applied to all pages of the application, including the Dashboard.
          </p>
          <p className="text-muted-foreground">
            Your theme selection is saved and will persist across sessions until you change it again.
          </p>
        </div>
      )}
    </div>
  );
}
