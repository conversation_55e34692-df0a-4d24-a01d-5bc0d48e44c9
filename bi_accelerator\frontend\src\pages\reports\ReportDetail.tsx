import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { ChevronLeft, FileBarChart, Database, List, Code, ChevronRight, Search, Play, BarChart3, BarChart4 } from 'lucide-react';
import { 
  apiService, 
  Project, 
  ReportDetail as IReportDetail,
  SQLAnalysisVisualResponse,
  SQLAnalysisTabularResponse,
  SQLAnalysisRawResponse,
  SQLAnalysisTable
} from '@/services/api';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import Card, { CardHeader, CardTitle, CardContent, CardFooter } from '@/components/shared/Card';
import Button from '@/components/shared/Button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Textarea } from '@/components/ui/textarea';
import { Loader } from '@/components/shared/Loader';
import SQLVisualization from '@/components/SQLVisualization';
import PowerBIExportModal from '@/components/PowerBIExportModal';
import ExpressionModal from '@/components/ExpressionModal';

const BASE_URL = 'http://localhost:8001/api';

// Define the report type based on usage
interface Report {
  id: string;
  name: string;
  type?: string;
  date_created?: string;
  date_modified?: string;
  owner?: string | { name: string };
  version?: string;
  ext_type?: string;
  subtype?: string;
  description?: string;
  ancestors?: Array<{ id: string; name: string }>;
  attributes?: Array<{ id: string; name: string }>;
  metrics?: Array<{ id: string; name: string }>;
  sql?: string;
  data?: string;
}

const ReportDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const tabParam = searchParams.get('tab');
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [activeTab, setActiveTab] = useState(tabParam || 'info');
  const [shouldAnalyze, setShouldAnalyze] = useState(false);
  
  // New state for imported SQL
  const [importedSql, setImportedSql] = useState<string | null>(null);
  
  // New state for analysis format
  const [analysisFormat, setAnalysisFormat] = useState<'visual' | 'tabular'>('visual');
  const [rawAnalysisData, setRawAnalysisData] = useState<SQLAnalysisRawResponse | null>(null);
  
  // Power BI export modal state
  const [isPowerBIExportOpen, setIsPowerBIExportOpen] = useState(false);
  
  const [selectedItem, setSelectedItem] = useState<{ id: string; name: string; type: 'attribute' | 'metric' } | null>(null);
  const [isExpressionModalOpen, setIsExpressionModalOpen] = useState(false);
  
  useEffect(() => {
    const storedProject = localStorage.getItem('selectedProject');
    if (storedProject) {
      setSelectedProject(JSON.parse(storedProject));
    } else {
      navigate('/projects');
    }
  }, [navigate]);
  
  // Check if there's any SQL in session storage to analyze
  useEffect(() => {
    if (activeTab === 'analyze') {
      const storedSql = sessionStorage.getItem('analyze_sql');
      if (storedSql) {
        setImportedSql(storedSql);
        // Only set shouldAnalyze to true if we don't already have analysis data
        if (!rawAnalysisData) {
          setShouldAnalyze(true);
        }
        sessionStorage.removeItem('analyze_sql'); // Clear after use
      }
    }
  }, [activeTab, rawAnalysisData]);
  
  // Update the URL when tab changes
  useEffect(() => {
    const currentParams = new URLSearchParams(location.search);
    if (activeTab !== 'info') {
      currentParams.set('tab', activeTab);
    } else {
      currentParams.delete('tab');
    }
    
    const newSearch = currentParams.toString();
    const newPath = location.pathname + (newSearch ? `?${newSearch}` : '');
    
    // Only update if different to avoid loops
    if (location.search !== (newSearch ? `?${newSearch}` : '')) {
      navigate(newPath, { replace: true });
    }
  }, [activeTab, location, navigate]);
  
  const { data: report, isLoading, error } = useQuery<Report>({
    queryKey: ['report', id, selectedProject?.id],
    queryFn: () => selectedProject && id ? apiService.getReport(id, selectedProject.id) : Promise.resolve(null),
    enabled: !!selectedProject && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes cache before considering data stale
    gcTime: 10 * 60 * 1000, // 10 minutes cache before garbage collection
  });
  
  // Use either the imported SQL or the report SQL for analysis
  const sqlToAnalyze = importedSql || (report?.sql || '');
  
  // Query for raw data only
  const { data: rawData, isLoading: isAnalysisLoading } = useQuery<SQLAnalysisRawResponse>({
    queryKey: ['analyze-sql-raw', sqlToAnalyze],
    queryFn: async () => {
      const response = await fetch(`${BASE_URL}/analyze-sql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sql: sqlToAnalyze }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to analyze SQL');
      }
      
      const data = await response.json() as SQLAnalysisRawResponse;
      setRawAnalysisData(data);
      setShouldAnalyze(false); // Reset shouldAnalyze after successful analysis
      return data;
    },
    enabled: shouldAnalyze && !!sqlToAnalyze && activeTab === 'analyze',
    staleTime: Infinity, // Don't consider analysis data stale
    gcTime: 30 * 60 * 1000, // 30 minutes cache before garbage collection
  });
  
  // Set raw analysis data whenever rawData is available
  useEffect(() => {
    if (rawData) {
      setRawAnalysisData(rawData);
    }
  }, [rawData]);
  
  // Transform the raw data based on the selected format
  const formattedAnalysisData = useMemo(() => {
    // Use either rawData from query or rawAnalysisData from state
    const analysisData = rawData || rawAnalysisData;
    if (!analysisData) return null;
    
    if (analysisFormat === 'tabular') {
      // Convert to tabular format
      const tabularData: SQLAnalysisTabularResponse = {
        tables: [],
        relationships: [],
        model_structure: analysisData.model_structure,
      };
      
      // Process main tables
      for (const table of analysisData.main_tables) {
        tabularData.tables.push({
          table_name: table.name,
          table_id: table.id,
          table_type: table.type,
          fields: table.fields.map(f => ({
            field: f.name,
            type: f.type,
            description: f.description || '',
          })),
        });
      }
      
      // Process relationships
      tabularData.relationships = analysisData.relationships.map(rel => ({
        source_table: rel.source,
        target_table: rel.target,
        type: rel.relationship,
        source_field: rel.sourceField,
        target_field: rel.targetField,
        description: rel.description || '',
      }));
      
      return tabularData;
    } else {
      // Convert to visual format
      const visualData: SQLAnalysisVisualResponse = {
        nodes: analysisData.main_tables,
        links: analysisData.relationships,
        model_structure: analysisData.model_structure,
      };
      
      return visualData;
    }
  }, [rawData, rawAnalysisData, analysisFormat]);
  
  // Function to trigger analysis
  const handleAnalyze = () => {
    if (sqlToAnalyze) {
      setShouldAnalyze(true);
      // Clear any existing analysis data to ensure fresh analysis
      setRawAnalysisData(null);
    }
  };
  
  // Handle attribute or metric click
  const handleItemClick = (id: string, name: string, type: 'attribute' | 'metric') => {
    setSelectedItem({ id, name, type });
    setIsExpressionModalOpen(true);
  };
  
  if (!selectedProject) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <Alert>
          <AlertTitle>No project selected</AlertTitle>
          <AlertDescription>
            Please select a project first to view report details.
            <Button
              variant="link"
              className="p-0 ml-2"
              onClick={() => navigate('/projects')}
            >
              Go to Projects
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }
  
  if (!id) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <Alert>
          <AlertTitle>No report selected</AlertTitle>
          <AlertDescription>
            Please select a report to view details.
            <Button
              variant="link"
              className="p-0 ml-2"
              onClick={() => navigate('/reports')}
            >
              Go to Reports
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }
  
  return (
    <div className="container px-4 mx-auto animate-fade-in">
      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
      <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/')}>
          Home
        </Button>
        <ChevronRight className="h-4 w-4" />        
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/projects')}>
          Projects
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/reports')}>
          Reports
        </Button>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground truncate max-w-xs">
          {isLoading ? 'Loading...' : report?.name || 'Report Details'}
        </span>
      </div>
      
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div className="flex items-center">
          <Button
            variant="outline"
            size="icon"
            className="mr-4"
            onClick={() => navigate('/reports')}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center">
            <div className="p-2 rounded-lg bg-brand-100 mr-3">
              <FileBarChart className="h-6 w-6 text-brand-600" />
            </div>
            {isLoading ? (
              <div className="flex items-center">
                <Skeleton className="h-8 w-40 hidden" />
                <span className="text-2xl font-bold">Loading report...</span>
              </div>
            ) : (
              <h1 className="text-2xl font-bold">{report?.name || 'Report Not Found'}</h1>
            )}
          </div>
        </div>
        
        {/* Add Export to Power BI button */}
        {!isLoading && report && (
          <Button
            onClick={() => setIsPowerBIExportOpen(true)}
            className="flex items-center gap-2"
          >
            <BarChart4 className="h-4 w-4" />
            Export to Power BI
          </Button>
        )}
      </div>
      
      {isLoading ? (
        <div className="grid gap-6 py-12">
          <Loader />
        </div>
      ) : error ? (
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load report details. Please try again later.
          </AlertDescription>
        </Alert>
      ) : !report ? (
        <Alert>
          <AlertTitle>Report Not Found</AlertTitle>
          <AlertDescription>
            The requested report could not be found.
          </AlertDescription>
        </Alert>
      ) : (
        <Tabs defaultValue="info" value={activeTab} onValueChange={setActiveTab} className="animate-fade-in">
          <TabsList className="grid grid-cols-5 w-full md:w-auto mb-8">
            <TabsTrigger value="info" className="flex items-center gap-2">
              <List className="h-4 w-4" />
              <span>Information</span>
            </TabsTrigger>
            <TabsTrigger value="attributes" className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              <span>Attributes</span>
            </TabsTrigger>
            <TabsTrigger value="sql" className="flex items-center gap-2">
              <Code className="h-4 w-4" />
              <span>SQL</span>
            </TabsTrigger>
            <TabsTrigger value="data" className="flex items-center gap-2">
              <FileBarChart className="h-4 w-4" />
              <span>Data</span>
            </TabsTrigger>
            <TabsTrigger value="analyze" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              <span>Analyze</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="info" className="animate-slide-up">
            <Card>
              <CardHeader>
                <CardTitle>Report Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Report ID</h3>
                    <p className="font-mono text-sm bg-muted p-2 rounded">{report.id}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Type</h3>
                    <p>{report.type || 'Standard Report'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Created</h3>
                    <p>{report.date_created || 'Unknown'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Modified</h3>
                    <p>{report.date_modified || 'Unknown'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Owner</h3>
                    <p>{report.owner ? (typeof report.owner === 'string' ? report.owner : report.owner.name || 'Unknown') : 'Unknown'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Version</h3>
                    <p>{report.version || 'Unknown'}</p>
                  </div>
                  {report.ext_type && (
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Extended Type</h3>
                      <p>{report.ext_type}</p>
                    </div>
                  )}
                  {report.subtype !== undefined && (
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Subtype</h3>
                      <p>{report.subtype}</p>
                    </div>
                  )}
                </div>
                
                {report.description && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Description</h3>
                    <p className="text-sm">{report.description}</p>
                  </div>
                )}
                
                {report.ancestors && report.ancestors.length > 0 && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Location</h3>
                    <div className="flex flex-wrap items-center gap-1 text-sm">
                      {report.ancestors.map((ancestor: any, index: number) => (
                        <React.Fragment key={ancestor.id}>
                          <span>{ancestor.name}</span>
                          {index < report.ancestors.length - 1 && (
                            <ChevronRight className="h-3 w-3 mx-1 text-muted-foreground" />
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="attributes" className="animate-slide-up">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Attributes</CardTitle>
                </CardHeader>
                <CardContent>
                  {report.attributes && report.attributes.length > 0 ? (
                    <ul className="divide-y">
                      {report.attributes.map((attribute) => (
                        <li 
                          key={attribute.id} 
                          className="py-2 hover:bg-slate-50 cursor-pointer transition-colors px-2 rounded-md"
                          onClick={() => handleItemClick(attribute.id, attribute.name, 'attribute')}
                        >
                          <p className="font-medium flex items-center">
                            {attribute.name}
                            <Code className="ml-2 h-4 w-4 text-muted-foreground" />
                          </p>
                          <p className="text-xs text-muted-foreground font-mono">{attribute.id}</p>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-muted-foreground">No attributes found</p>
                  )}
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Metrics</CardTitle>
                </CardHeader>
                <CardContent>
                  {report.metrics && report.metrics.length > 0 ? (
                    <ul className="divide-y">
                      {report.metrics.map((metric) => (
                        <li 
                          key={metric.id} 
                          className="py-2 hover:bg-slate-50 cursor-pointer transition-colors px-2 rounded-md"
                          onClick={() => handleItemClick(metric.id, metric.name, 'metric')}
                        >
                          <p className="font-medium flex items-center">
                            {metric.name}
                            <Code className="ml-2 h-4 w-4 text-muted-foreground" />
                          </p>
                          <p className="text-xs text-muted-foreground font-mono">{metric.id}</p>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-muted-foreground">No metrics found</p>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="sql" className="animate-slide-up">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>SQL Query</CardTitle>
                {report.sql && (
                  <Button
                    size="sm"
                    variant="outline"
                    className="flex items-center gap-2"
                    onClick={() => {
                      setActiveTab('analyze');
                      // Only trigger analysis if we don't already have analysis data
                      if (!rawAnalysisData) {
                        handleAnalyze();
                      }
                    }}
                  >
                    <Search className="h-4 w-4" />
                    Analyze
                  </Button>
                )}
              </CardHeader>
              <CardContent>
                {report.sql ? (
                  <ScrollArea className="h-96 w-full rounded-md border p-4">
                    <pre className="font-mono text-sm whitespace-pre-wrap break-all">
                      {report.sql}
                    </pre>
                  </ScrollArea>
                ) : (
                  <div className="flex flex-col items-center justify-center h-32">
                    <p className="text-muted-foreground">No SQL query available for this report</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="data" className="animate-slide-up">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Data Preview</CardTitle>
              </CardHeader>
              <CardContent>
                {report.data ? (
                  (() => {
                    try {
                      // Try to parse the data as JSON (for pandas formatted data)
                      const jsonData = JSON.parse(report.data);
                      if (Array.isArray(jsonData) && jsonData.length > 0) {
                        // If it's an array, display as a table
                        return (
                          <div className="overflow-auto max-h-96 border rounded-md">
                            <table className="w-full border-collapse table-auto">
                              <thead className="sticky top-0 bg-background">
                                <tr className="bg-muted">
                                  {Object.keys(jsonData[0] || {}).map((key, index) => (
                                    <th key={index} className="p-2 text-left border whitespace-nowrap">
                                      {key.includes('@') ? key.split('@')[1] : key}
                                    </th>
                                  ))}
                                </tr>
                              </thead>
                              <tbody>
                                {jsonData.map((row, rowIndex) => (
                                  <tr key={rowIndex} className={rowIndex % 2 === 0 ? '' : 'bg-muted/30'}>
                                    {Object.entries(row).map(([key, value], cellIndex) => (
                                      <td key={cellIndex} className="p-2 border">
                                        {value === null ? 
                                          "null" : 
                                          typeof value === 'object' ? 
                                            JSON.stringify(value) : 
                                            String(value)}
                                      </td>
                                    ))}
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        );
                      }
                      // If it's not an array, display as JSON
                      return (
                        <ScrollArea className="h-96 w-full rounded-md border p-4">
                          <pre className="font-mono text-sm whitespace-pre-wrap">
                            {JSON.stringify(jsonData, null, 2)}
                          </pre>
                        </ScrollArea>
                      );
                    } catch (e) {
                      // If not valid JSON, display as plain text
                      return (
                        <ScrollArea className="h-96 w-full rounded-md border p-4">
                          <pre className="font-mono text-sm whitespace-pre-wrap">
                            {report.data}
                          </pre>
                        </ScrollArea>
                      );
                    }
                  })()
                ) : (
                  <div className="flex flex-col items-center justify-center h-32">
                    <p className="text-muted-foreground">No data preview available for this report</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="analyze" className="animate-slide-up">
            <div className="grid gap-8">
              {!formattedAnalysisData && !isAnalysisLoading && (
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Code className="h-5 w-5" />
                      SQL Analysis
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <Search className="h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium mb-2">Run SQL Analysis</h3>
                      <p className="text-muted-foreground max-w-md mb-6">
                        {importedSql ? 
                          "Click the \"Run Analysis\" button to analyze the imported SQL query." :
                          "Click the \"Run Analysis\" button to analyze the SQL query and get insights."
                        }
                      </p>
                      <div className="flex flex-col items-center gap-4">
                        <div className="flex items-center gap-4 mb-4">
                          <Button
                            variant={analysisFormat === 'visual' ? 'default' : 'outline'}
                            onClick={() => setAnalysisFormat('visual')}
                            className="flex items-center gap-2"
                          >
                            <Database className="h-4 w-4" />
                            Visual Format
                          </Button>
                          <Button
                            variant={analysisFormat === 'tabular' ? 'default' : 'outline'}
                            onClick={() => setAnalysisFormat('tabular')}
                            className="flex items-center gap-2"
                          >
                            <List className="h-4 w-4" />
                            Tabular Format
                          </Button>
                        </div>
                      <Button onClick={handleAnalyze}>
                        <Play className="h-4 w-4 mr-2" />
                        Run Analysis
                      </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
              
              {!sqlToAnalyze && shouldAnalyze && (
                <Card>
                  <CardContent>
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <p className="text-muted-foreground">No SQL query available to analyze.</p>
                    </div>
                  </CardContent>
                </Card>
              )}
              
              {isAnalysisLoading && (
                <Card>
                  <CardHeader>
                    <CardTitle>Analysis Results</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-center py-12">
                      <Loader  />
                    </div>
                  </CardContent>
                </Card>
              )}
              
              {formattedAnalysisData && (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Analysis Results</CardTitle>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleAnalyze}
                        className="flex items-center gap-2 mr-2"
                      >
                        <Play className="h-4 w-4" />
                        Reload
                      </Button>
                      <Button
                        size="sm"
                        variant={analysisFormat === 'visual' ? 'default' : 'outline'}
                        onClick={() => setAnalysisFormat('visual')}
                        className="flex items-center gap-2"
                      >
                        <Database className="h-4 w-4" />
                        Visual
                      </Button>
                  <Button
                    size="sm"
                        variant={analysisFormat === 'tabular' ? 'default' : 'outline'}
                        onClick={() => setAnalysisFormat('tabular')}
                    className="flex items-center gap-2"
                  >
                        <List className="h-4 w-4" />
                        Tabular
                  </Button>
                    </div>
              </CardHeader>
              <CardContent>
                    {analysisFormat === 'visual' ? (
                      <div className="space-y-6">
                        {/* Visual Format Display */}
                        <div className="space-y-4">
                          {/* Add SQL Visualization */}
                          <SQLVisualization 
                            main_tables={(formattedAnalysisData as SQLAnalysisVisualResponse).nodes as SQLAnalysisTable[]}
                            relationships={(formattedAnalysisData as SQLAnalysisVisualResponse).links}
                            model_structure={(formattedAnalysisData as SQLAnalysisVisualResponse).model_structure}
                          />

                          <h3 className="text-lg font-semibold mt-8">Model Structure</h3>
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <h4 className="font-medium mb-2">Fact Tables</h4>
                              <ul className="list-disc list-inside space-y-1">
                                {(formattedAnalysisData as SQLAnalysisVisualResponse).model_structure.fact_tables.map((table, index) => (
                                  <li key={index}>{table}</li>
                                ))}
                              </ul>
                            </div>
                            <div>
                              <h4 className="font-medium mb-2">Dimension Tables</h4>
                              <ul className="list-disc list-inside space-y-1">
                                {(formattedAnalysisData as SQLAnalysisVisualResponse).model_structure.dimension_tables.map((table, index) => (
                                  <li key={index}>{table}</li>
                                ))}
                              </ul>
                            </div>
                          </div>
                          
                          <div>
                            <h4 className="font-medium mb-2">Model Type</h4>
                            <p>{(formattedAnalysisData as SQLAnalysisVisualResponse).model_structure.model_type}</p>
                          </div>
                          
                          <div>
                            <h4 className="font-medium mb-2">Description</h4>
                            <p>{(formattedAnalysisData as SQLAnalysisVisualResponse).model_structure.description}</p>
                          </div>
                          
                          {(formattedAnalysisData as SQLAnalysisVisualResponse).model_structure.hierarchies.length > 0 && (
                            <div>
                              <h4 className="font-medium mb-2">Hierarchies</h4>
                              <div className="space-y-2">
                                {(formattedAnalysisData as SQLAnalysisVisualResponse).model_structure.hierarchies.map((hierarchy, index) => (
                                  <div key={index} className="p-4 border rounded-lg">
                                    <h5 className="font-medium mb-1">{hierarchy.name}</h5>
                                    <p className="text-sm text-muted-foreground mb-2">{hierarchy.description}</p>
                                    <div className="flex items-center gap-2">
                                      {hierarchy.levels.map((level, levelIndex) => (
                                        <React.Fragment key={levelIndex}>
                                          <span>{level}</span>
                                          {levelIndex < hierarchy.levels.length - 1 && (
                                            <ChevronRight className="h-4 w-4 text-muted-foreground" />
                                          )}
                                        </React.Fragment>
                                      ))}
                                    </div>
                                  </div>
                                ))}
                  </div>
                  </div>
                          )}
                  </div>
                  </div>
                ) : (
                      <div className="space-y-6">
                        {/* Tabular Format Display */}
                        <div className="space-y-4">
                          <div>
                            <h3 className="text-lg font-semibold mb-4">Tables</h3>
                            <div className="overflow-x-auto">
                              <table className="w-full border-collapse">
                                <thead>
                                  <tr className="bg-muted">
                                    <th className="px-4 py-2 text-left border">Table Name</th>
                                    <th className="px-4 py-2 text-left border">Type</th>
                                    <th className="px-4 py-2 text-left border">Fields</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {(formattedAnalysisData as SQLAnalysisTabularResponse).tables.map((table, index) => (
                                    <tr key={index}>
                                      <td className="px-4 py-2 border">{table.table_name}</td>
                                      <td className="px-4 py-2 border">{table.table_type}</td>
                                      <td className="px-4 py-2 border">
                                        <ul className="list-disc list-inside">
                                          {table.fields.map((field, fieldIndex) => (
                                            <li key={fieldIndex}>
                                              {field.field} ({field.type})
                                              {field.description && (
                                                <span className="text-muted-foreground"> - {field.description}</span>
                                              )}
                                            </li>
                                          ))}
                                        </ul>
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                          
                          <div>
                            <h3 className="text-lg font-semibold mb-4">Relationships</h3>
                            <div className="overflow-x-auto">
                              <table className="w-full border-collapse">
                                <thead>
                                  <tr className="bg-muted">
                                    <th className="px-4 py-2 text-left border">Source Table</th>
                                    <th className="px-4 py-2 text-left border">Target Table</th>
                                    <th className="px-4 py-2 text-left border">Type</th>
                                    <th className="px-4 py-2 text-left border">Fields</th>
                                    <th className="px-4 py-2 text-left border">Description</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {(formattedAnalysisData as SQLAnalysisTabularResponse).relationships.map((rel, index) => (
                                    <tr key={index}>
                                      <td className="px-4 py-2 border">{rel.source_table}</td>
                                      <td className="px-4 py-2 border">{rel.target_table}</td>
                                      <td className="px-4 py-2 border">{rel.type}</td>
                                      <td className="px-4 py-2 border">
                                        {rel.source_field} → {rel.target_field}
                                      </td>
                                      <td className="px-4 py-2 border">{rel.description}</td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                  </div>
                )}
              </CardContent>
            </Card>
              )}
            </div>
          </TabsContent>
        </Tabs>
      )}
      
      {/* Power BI Export Modal */}
      {report && selectedProject && (
        <PowerBIExportModal
          isOpen={isPowerBIExportOpen}
          onClose={() => setIsPowerBIExportOpen(false)}
          reportId={id || ''}
          reportName={report.name}
          projectId={selectedProject.id}
        />
      )}
      
      {/* Expression Modal */}
      {selectedItem && selectedProject && (
        <ExpressionModal
          isOpen={isExpressionModalOpen}
          onClose={() => setIsExpressionModalOpen(false)}
          id={selectedItem.id}
          name={selectedItem.name}
          type={selectedItem.type}
          projectId={selectedProject.id}
        />
      )}
    </div>
  );
};

export default ReportDetail;
