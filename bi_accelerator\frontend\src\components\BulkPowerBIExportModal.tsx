import React, { useState, useEffect, useRef } from 'react';
import { CheckIcon, ExternalLink, Check, AlertCircle, Info, Clock, BarChart4, SearchIcon } from 'lucide-react';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  Di<PERSON>Title,
  DialogFooter
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { apiService, Report, PowerBIDatasetUpdate } from '@/services/api';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader } from '@/components/shared/Loader';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';

interface BulkPowerBIExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  reports: Report[];
  projectId: string;
}

export const BulkPowerBIExportModal: React.FC<BulkPowerBIExportModalProps> = ({
  isOpen,
  onClose,
  reports,
  projectId
}) => {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedReports, setSelectedReports] = useState<Report[]>([]);
  const [workspaceId, setWorkspaceId] = useState('');
  const [datasetPrefix, setDatasetPrefix] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [currentTab, setCurrentTab] = useState('select');
  const [bulkTaskId, setBulkTaskId] = useState<string | null>(null);
  const [reportExports, setReportExports] = useState<{
    [reportId: string]: {
      taskId: string;
      datasetName: string;
      datasetId: string | null;
      status: 'pending' | 'exporting' | 'completed' | 'error';
      updates: PowerBIDatasetUpdate[];
    }
  }>({});
  
  const eventSourceRef = useRef<{ [taskId: string]: EventSource }>({});
  const updatesEndRef = useRef<HTMLDivElement>(null);

  // Filter reports based on search term
  const filteredReports = reports.filter(report => 
    report.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle report selection
  const toggleReportSelection = (report: Report) => {
    if (selectedReports.some(r => r.id === report.id)) {
      setSelectedReports(selectedReports.filter(r => r.id !== report.id));
    } else {
      setSelectedReports([...selectedReports, report]);
    }
  };

  // Handle select all
  const selectAll = () => {
    if (selectedReports.length === filteredReports.length) {
      setSelectedReports([]);
    } else {
      setSelectedReports(filteredReports);
    }
  };

  // Scroll to bottom of updates when new ones arrive
  useEffect(() => {
    if (updatesEndRef.current) {
      updatesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
    
    // Check if all exports are complete whenever reportExports changes
    if (isExporting) {
      checkAllExportsComplete();
    }
  }, [reportExports, isExporting]);

  // Clean up event sources on unmount
  useEffect(() => {
    return () => {
      Object.values(eventSourceRef.current).forEach(es => es.close());
    };
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (selectedReports.length === 0) {
      toast({
        title: "Selection Error",
        description: "Please select at least one report to export",
        variant: "destructive",
      });
      return;
    }

    if (!workspaceId.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter a workspace ID",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    
    // Initialize report exports
    const initialExports: { [reportId: string]: any } = {};
    selectedReports.forEach(report => {
      const datasetName = datasetPrefix 
        ? `${datasetPrefix}_${report.name}` 
        : report.name;
        
      initialExports[report.id] = {
        datasetName,
        taskId: null,
        datasetId: null,
        status: 'pending',
        updates: []
      };
    });
    
    setReportExports(initialExports);
    setIsExporting(true);
    setCurrentTab('progress');
    setIsSubmitting(false);
    
    // Start with the first report
    processNextReport(0);
  };

  // Process reports one by one sequentially
  const processNextReport = async (index: number) => {
    // If we've processed all reports, we're done
    if (index >= selectedReports.length) {
      console.log('All reports processed');
      return;
    }

    const report = selectedReports[index];
    const datasetName = datasetPrefix ? `${datasetPrefix}_${report.name}` : report.name;

    console.log(`Processing report ${index + 1}/${selectedReports.length}: ${report.name}`);
    
    try {
      // Update status to exporting
      setReportExports(prev => ({
        ...prev,
        [report.id]: {
          ...prev[report.id],
          status: 'exporting'
        }
      }));
      
      // Create Power BI dataset
      const response = await apiService.createPowerBIDataset(
        report.id, 
        datasetName, 
        projectId,
        workspaceId
      );
      
      setReportExports(prev => ({
        ...prev,
        [report.id]: {
          ...prev[report.id],
          taskId: response.task_id,
          status: 'exporting'
        }
      }));
      
      // Set up a promise that will resolve when this report's export completes
      const exportCompletionPromise = new Promise<void>((resolve, reject) => {
        // Connect to SSE endpoint
        const url = apiService.getPowerBIDatasetUpdatesUrl(response.task_id);
        const eventSource = new EventSource(url);
        eventSourceRef.current[response.task_id] = eventSource;

        eventSource.onmessage = (event) => {
          try {
            const update = JSON.parse(event.data) as PowerBIDatasetUpdate;
            console.log(`[EventSource] Received update for ${report.id}:`, update);
            
            // Handle status updates specifically
            if (update.type === 'status') {
              console.log(`[EventSource] Status update for ${report.id}: ${update.status}`);
            }
            
            setReportExports(prev => ({
              ...prev,
              [report.id]: {
                ...prev[report.id],
                updates: [...prev[report.id].updates, update],
                datasetId: update.dataset_id || prev[report.id].datasetId,
                status: (update.type === 'status' && ['completed', 'error'].includes(update.status || '')) 
                  ? (update.status === 'completed' ? 'completed' : 'error')
                  : prev[report.id].status
              }
            }));
            
            // Check if process is completed or errored
            if (update.type === 'status' && ['completed', 'error'].includes(update.status || '')) {
              console.log(`[EventSource] Closing connection for ${report.id} - task completed with status: ${update.status}`);
              eventSource.close();
              delete eventSourceRef.current[response.task_id];
              
              // Resolve the promise since the export is complete
              resolve();
            }
          } catch (error) {
            console.error('Error parsing SSE update:', error);
            // Close the connection on error
            eventSource.close();
            delete eventSourceRef.current[response.task_id];
            
            setReportExports(prev => ({
              ...prev,
              [report.id]: {
                ...prev[report.id],
                status: 'error',
                updates: [
                  ...prev[report.id].updates,
                  {
                    type: 'error',
                    message: "Error parsing update from server",
                    timestamp: new Date().toISOString()
                  }
                ]
              }
            }));
            
            // Reject the promise since there was an error
            reject(error);
          }
        };

        eventSource.onerror = (error) => {
          console.error('SSE connection error', error);
          eventSource.close();
          delete eventSourceRef.current[response.task_id];
          
          setReportExports(prev => ({
            ...prev,
            [report.id]: {
              ...prev[report.id],
              status: 'error',
              updates: [
                ...prev[report.id].updates,
                {
                  type: 'error',
                  message: "Lost connection to the server. The export process may still be running.",
                  timestamp: new Date().toISOString()
                }
              ]
            }
          }));
          
          // Reject the promise since there was an error
          reject(error);
        };
      });

      // Wait for this report to complete
      await exportCompletionPromise;
      
    } catch (error) {
      console.error(`Failed to create Power BI dataset for report ${report.name}:`, error);
      
      setReportExports(prev => ({
        ...prev,
        [report.id]: {
          ...prev[report.id],
          status: 'error',
          updates: [
            ...prev[report.id].updates,
            {
              type: 'error',
              message: error instanceof Error ? error.message : "Failed to start export process",
              timestamp: new Date().toISOString()
            }
          ]
        }
      }));
    } finally {
      // Move on to the next report regardless of success or failure
      setTimeout(() => {
        processNextReport(index + 1);
      }, 1000); // Short delay before starting the next report
    }
  };

  const connectToEventSource = (reportId: string, taskId: string) => {
    // This function is now handled within processNextReport
    console.log('The connectToEventSource function is deprecated and not used in sequential processing');
  };

  const checkAllExportsComplete = () => {
    const exportsList = Object.values(reportExports);
    console.log('[checkAllExportsComplete] Current exports status:', 
      exportsList.map(exp => ({ 
        datasetName: exp.datasetName, 
        status: exp.status,
        updates: exp.updates.length
      }))
    );
    
    const allComplete = exportsList.length > 0 && exportsList.every(
      exp => exp.status === 'completed' || exp.status === 'error'
    );
    
    // If all exports are complete, update the UI state
    if (allComplete && isExporting) {
      console.log('[checkAllExportsComplete] All exports complete, updating UI state');
      setIsExporting(false);
    }
    
    return allComplete;
  };

  const handleClose = () => {
    // Check if export is in progress
    const exportInProgress = isExporting && !checkAllExportsComplete();
    
    if (exportInProgress) {
      // Show a confirmation if exports are still in progress
      const shouldClose = window.confirm(
        "Exports are still in progress. Closing this window will not cancel the exports, but you won't be able to view their progress. Continue?"
      );
      
      if (!shouldClose) {
        return; // User cancelled the close
      }
    }
    
    // Close all event sources
    Object.values(eventSourceRef.current).forEach(es => es.close());
    eventSourceRef.current = {};
    
    // Reset state if needed
    if (!exportInProgress) {
      // Don't reset the exports data so users can see completed exports if they reopen
    }
    
    onClose();
  };

  // Add a new function to explicitly clear export data
  const resetExportData = () => {
    setReportExports({});
    setIsExporting(false);
  };

  const renderUpdateIcon = (type: string, status?: string) => {
    switch (type) {
      case 'success':
        return <Check className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-amber-500" />;
      case 'status':
        if (status === 'completed') return <Check className="h-4 w-4 text-green-500" />;
        if (status === 'error') return <AlertCircle className="h-4 w-4 text-red-500" />;
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'info':
      default:
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <Check className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'exporting':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-gray-400" />;
      default:
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="w-full sm:max-w-md md:max-w-xl lg:max-w-3xl xl:max-w-4xl" onInteractOutside={(e) => {
        // Prevent closing by clicking outside while export is in progress
        if (isExporting && !Object.values(reportExports).every(r => ['completed', 'error'].includes(r.status))) {
          e.preventDefault();
        }
      }} onEscapeKeyDown={(e) => {
        // Prevent closing with ESC key while export is in progress
        if (isExporting && !Object.values(reportExports).every(r => ['completed', 'error'].includes(r.status))) {
          e.preventDefault();
        }
      }}>
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <BarChart4 className="h-5 w-5 mr-2" />
            Bulk Export to Power BI
          </DialogTitle>
        </DialogHeader>
        
        <Tabs value={currentTab} onValueChange={setCurrentTab}>
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger 
              value="select" 
              disabled={isExporting && !Object.values(reportExports).every(r => ['completed', 'error'].includes(r.status))}
            >
              Select Reports
            </TabsTrigger>
            <TabsTrigger value="progress">
              Export Progress
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="select">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="workspace-id">Workspace ID</Label>
                <Input
                  id="workspace-id"
                  placeholder="Enter Power BI workspace ID"
                  value={workspaceId}
                  onChange={(e) => setWorkspaceId(e.target.value)}
                  disabled={isSubmitting}
                />
                <p className="text-xs text-muted-foreground">
                  Specify the Power BI workspace where the datasets will be created.
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="dataset-prefix">Dataset name prefix (optional)</Label>
                <Input
                  id="dataset-prefix"
                  placeholder="Enter prefix for all datasets"
                  value={datasetPrefix}
                  onChange={(e) => setDatasetPrefix(e.target.value)}
                  disabled={isSubmitting}
                />
                <p className="text-xs text-muted-foreground">
                  If provided, dataset names will be formatted as "prefix_reportName".
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between mb-2">
                  <Label>Select reports to export</Label>
                  <div className="relative max-w-xs w-full">
                    <SearchIcon className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search reports..."
                      className="pl-8"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
                
                <div className="border rounded-md">
                  <div className="p-2 border-b bg-muted/30 flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="select-all" 
                        checked={selectedReports.length > 0 && selectedReports.length === filteredReports.length}
                        onCheckedChange={selectAll}
                      />
                      <label 
                        htmlFor="select-all" 
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Select All
                      </label>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {selectedReports.length} of {reports.length} selected
                    </span>
                  </div>
                  
                  <ScrollArea className="h-60 w-full">
                    {filteredReports.length === 0 ? (
                      <div className="flex flex-col items-center justify-center py-8 text-center">
                        <p className="text-muted-foreground">No reports found matching "{searchTerm}"</p>
                      </div>
                    ) : (
                      <div className="p-1">
                        {filteredReports.map((report) => (
                          <div 
                            key={report.id} 
                            className="flex items-center space-x-2 p-2 hover:bg-muted/50 rounded-md"
                          >
                            <Checkbox 
                              id={`report-${report.id}`} 
                              checked={selectedReports.some(r => r.id === report.id)}
                              onCheckedChange={() => toggleReportSelection(report)}
                            />
                            <label 
                              htmlFor={`report-${report.id}`} 
                              className="flex-1 text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {report.name}
                            </label>
                            <span className="text-xs text-muted-foreground">{report.type}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </ScrollArea>
                </div>
              </div>
              
              {isSubmitting && (
                <div className="flex justify-center py-4">
                  <Loader className="scale-75" />
                </div>
              )}
              
              <DialogFooter>
                <Button
                  variant="outline"
                  type="button"
                  onClick={handleClose}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={isSubmitting || selectedReports.length === 0}
                >
                  {isSubmitting ? 'Processing...' : 'Export Selected Reports'}
                </Button>
              </DialogFooter>
            </form>
          </TabsContent>
          
          <TabsContent value="progress">
            <div className="space-y-4">
              {!isExporting && Object.keys(reportExports).length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <p className="text-muted-foreground">Select reports and start the export process</p>
                  <Button 
                    variant="outline" 
                    className="mt-4"
                    onClick={() => setCurrentTab('select')}
                  >
                    Go to Report Selection
                  </Button>
                </div>
              ) : !isExporting && Object.keys(reportExports).length > 0 ? (
                <>
                  <div className="border rounded-md">
                    <div className="p-2 border-b bg-muted/30 flex items-center justify-between">
                      <p className="text-sm font-medium">Previous Export Results</p>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={resetExportData}
                        className="text-xs h-7 px-2"
                      >
                        Clear History
                      </Button>
                    </div>
                    <div className="p-3">
                      <p className="text-sm mb-1">Total Reports: {Object.keys(reportExports).length}</p>
                      <div className="flex space-x-4 text-sm">
                        <p>Completed: {Object.values(reportExports).filter(r => r.status === 'completed').length}</p>
                        <p>Failed: {Object.values(reportExports).filter(r => r.status === 'error').length}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="border rounded-md">
                    <div className="p-2 border-b bg-muted/30">
                      <p className="text-sm font-medium">Export Details</p>
                    </div>
                    <ScrollArea className="h-60 w-full">
                      <div className="divide-y">
                        {Object.entries(reportExports).map(([reportId, exportInfo]) => {
                          const report = reports.find(r => r.id === reportId);
                          if (!report) return null;
                          
                          return (
                            <div key={reportId} className="p-3">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center space-x-2">
                                  {getStatusIcon(exportInfo.status)}
                                  <p className="font-medium">{report.name}</p>
                                </div>
                                <span className="text-xs px-2 py-1 rounded-full bg-muted">
                                  {exportInfo.status === 'pending' ? 'Pending' : 
                                   exportInfo.status === 'exporting' ? 'In Progress' :
                                   exportInfo.status === 'completed' ? 'Completed' : 'Failed'}
                                </span>
                              </div>
                              
                              {exportInfo.taskId && (
                                <p className="text-xs text-muted-foreground mb-1">
                                  Task ID: {exportInfo.taskId}
                                </p>
                              )}
                              
                              {exportInfo.datasetId && (
                                <p className="text-xs text-muted-foreground mb-1">
                                  Dataset ID: {exportInfo.datasetId}
                                </p>
                              )}
                              
                              {exportInfo.updates.length > 0 && (
                                <div className="mt-2 pl-6 border-l-2 border-muted">
                                  {exportInfo.updates.map((update, index) => (
                                    <div key={index} className="flex items-start space-x-2 text-xs mb-1">
                                      <div className="mt-0.5">
                                        {renderUpdateIcon(update.type, update.status)}
                                      </div>
                                      <div>
                                        <p className={`${update.type === 'error' ? 'text-red-500' : ''}`}>
                                          {update.message}
                                        </p>
                                        <p className="text-[10px] text-muted-foreground">
                                          {new Date(update.timestamp).toLocaleTimeString()}
                                        </p>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          );
                        })}
                        <div ref={updatesEndRef} />
                      </div>
                    </ScrollArea>
                  </div>
                </>
              ) : (
                <>
                  <div className="border rounded-md">
                    <div className="p-2 border-b bg-muted/30">
                      <p className="text-sm font-medium">Export Summary</p>
                    </div>
                    <div className="p-3">
                      <p className="text-sm mb-1">Total Reports: {selectedReports.length}</p>
                      <div className="flex space-x-4 text-sm">
                        <p>Completed: {Object.values(reportExports).filter(r => r.status === 'completed').length}</p>
                        <p>In Progress: {Object.values(reportExports).filter(r => r.status === 'exporting').length}</p>
                        <p>Failed: {Object.values(reportExports).filter(r => r.status === 'error').length}</p>
                        <p>Pending: {Object.values(reportExports).filter(r => r.status === 'pending').length}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="border rounded-md">
                    <div className="p-2 border-b bg-muted/30">
                      <p className="text-sm font-medium">Export Details</p>
                    </div>
                    <ScrollArea className="h-60 w-full">
                      <div className="divide-y">
                        {selectedReports.map((report) => {
                          const exportInfo = reportExports[report.id];
                          
                          if (!exportInfo) return null;
                          
                          return (
                            <div key={report.id} className="p-3">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center space-x-2">
                                  {getStatusIcon(exportInfo.status)}
                                  <p className="font-medium">{report.name}</p>
                                </div>
                                <span className="text-xs px-2 py-1 rounded-full bg-muted">
                                  {exportInfo.status === 'pending' ? 'Pending' : 
                                   exportInfo.status === 'exporting' ? 'In Progress' :
                                   exportInfo.status === 'completed' ? 'Completed' : 'Failed'}
                                </span>
                              </div>
                              
                              {exportInfo.taskId && (
                                <p className="text-xs text-muted-foreground mb-1">
                                  Task ID: {exportInfo.taskId}
                                </p>
                              )}
                              
                              {exportInfo.datasetId && (
                                <p className="text-xs text-muted-foreground mb-1">
                                  Dataset ID: {exportInfo.datasetId}
                                </p>
                              )}
                              
                              {exportInfo.updates.length > 0 && (
                                <div className="mt-2 pl-6 border-l-2 border-muted">
                                  {exportInfo.updates.map((update, index) => (
                                    <div key={index} className="flex items-start space-x-2 text-xs mb-1">
                                      <div className="mt-0.5">
                                        {renderUpdateIcon(update.type, update.status)}
                                      </div>
                                      <div>
                                        <p className={`${update.type === 'error' ? 'text-red-500' : ''}`}>
                                          {update.message}
                                        </p>
                                        <p className="text-[10px] text-muted-foreground">
                                          {new Date(update.timestamp).toLocaleTimeString()}
                                        </p>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          );
                        })}
                        <div ref={updatesEndRef} />
                      </div>
                    </ScrollArea>
                  </div>
                </>
              )}
              
              <DialogFooter>
                {checkAllExportsComplete() ? (
                  <>
                    <Button 
                      className="mr-auto" 
                      variant="outline" 
                      onClick={() => window.open(`https://app.powerbi.com/groups/${workspaceId}`, '_blank')}
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      View in Power BI
                    </Button>
                    <Button onClick={handleClose}>
                      Close
                    </Button>
                  </>
                ) : (
                  <>
                    {isExporting && Object.values(reportExports).some(r => r.status === 'exporting' || r.status === 'pending') ? (
                      <div className="flex items-center justify-center w-full gap-2">
                        <Loader className="scale-75" />
                
                      </div>
                    ) : (
                      <Button onClick={handleClose}>
                        Close
                      </Button>
                    )}
                  </>
                )}
              </DialogFooter>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default BulkPowerBIExportModal; 