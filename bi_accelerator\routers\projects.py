from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import JSONResponse
import logging
import traceback
from mstrio.server.project import Project
from routers.utils import get_connection
from typing import Optional

logger = logging.getLogger("bi_accelerator")
router = APIRouter(prefix="/api", tags=["projects"])

@router.get("/projects")
async def get_projects(environment_name: Optional[str] = Query(None, description="Optional environment name to use")):
    logger.info(f"Fetching list of projects using environment: {environment_name or 'active'}")
    try:
        # Establish connection at the environment level (without project)
        conn = get_connection(project_id=None, environment_name=environment_name)
        projects = list(Project._list_projects(connection=conn))
        
        # Check access for each project
        projects_data = []
        for p in projects:
            try:
                # Try to connect to the project to check access
                project_conn = get_connection(project_id=p.id, environment_name=environment_name)
                has_access = True
            except Exception as e:
                logger.warning(f"No access to project {p.name} ({p.id}): {str(e)}")
                has_access = False
            
            projects_data.append({
                "id": p.id, 
                "name": p.name,
                "has_access": has_access
            })
            
        logger.info(f"Successfully retrieved {len(projects_data)} projects")
        logger.debug(f"Projects data: {projects_data}")
        return JSONResponse(content=projects_data)
    except Exception as e:
        logger.error(f"Error retrieving projects: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to retrieve projects: {str(e)}")

@router.get("/projects/{project_id}")
async def get_project(project_id: str, environment_name: Optional[str] = Query(None, description="Optional environment name to use")):
    logger.info(f"Fetching project details for project_id: {project_id} using environment: {environment_name or 'active'}")
    try:
        conn = get_connection(project_id=None, environment_name=environment_name)
        project = Project(connection=conn, id=project_id)
        project_data = {"id": project.id, "name": project.name}
        logger.info(f"Successfully retrieved project details: {project_data}")
        return JSONResponse(content=project_data)
    except Exception as e:
        logger.error(f"Error retrieving project details: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to retrieve project details: {str(e)}") 