export interface ThemeColors {
  primary: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
    950: string;
  };
  accent?: {
    blue: string;
    red: string;
    yellow: string;
    green: string;
  };
  background: {
    light: string;
    dark: string;
  };
  card: {
    light: string;
    dark: string;
  };
  text: {
    light: string;
    dark: string;
  };
  border: {
    light: string;
    dark: string;
  };
}

export interface ThemeFonts {
  body: string;
  heading: string;
  mono: string;
}

export interface ThemeRadii {
  none: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  '3xl': string;
  full: string;
}

export interface ThemeShadows {
  sm: string;
  md: string;
  lg: string;
  xl?: string;
  '2xl'?: string;
}

export interface ThemeBranding {
  logo: {
    light: string;
    dark: string;
  };
  favicon: string;
  logoStyle?: {
    width?: string;
    height?: string;
  };
}

// Define the structure for functions stored as strings with _code property
export interface ThemeFunctionCode {
  _code: string;
}

// Component-specific theme styles
export interface ComponentStyles {
  badge?: {
    // Define style generators for specific components
    getBackgroundColor?: ThemeFunctionCode | ((severity: string, category: string) => string);
    getTextColor?: ThemeFunctionCode | ((severity: string, category: string) => string);
    getBorderStyle?: ThemeFunctionCode | ((severity: string, category: string) => React.CSSProperties | undefined);
    useGradient?: boolean;
    // Default style values can be defined per component
    style?: React.CSSProperties;
  };
  alert?: {
    getBorderColor?: ThemeFunctionCode | ((severity: string) => string);
    getActionColor?: ThemeFunctionCode | ((severity: string) => string);
    style?: React.CSSProperties;
  };
  // Add more components as needed
}

export interface Theme {
  name: string;
  id: string;
  colors: ThemeColors;
  fonts: ThemeFonts;
  radii: ThemeRadii;
  shadows: ThemeShadows;
  branding: ThemeBranding;
  components?: ComponentStyles; // Component-specific theme configuration
}
