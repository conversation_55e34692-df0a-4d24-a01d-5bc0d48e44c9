import React, { useState, useEffect } from 'react';
import { 
  CheckCircle2, 
  Plus, 
  Trash2, 
  Refresh<PERSON><PERSON>, 
  Settings 
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useNavigate } from 'react-router-dom';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Spinner } from '@/components/ui/spinner';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { apiService, Environment, EnvironmentCredentials } from '@/services/api';
import EnvironmentConnectionForm from './EnvironmentConnectionForm';

interface EnvironmentManagerProps {
  onClose?: () => void;
}

const EnvironmentManager: React.FC<EnvironmentManagerProps> = ({ onClose }) => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [environments, setEnvironments] = useState<Environment[]>([]);
  const [activeEnvironment, setActiveEnvironment] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [testingConnection, setTestingConnection] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [confirmDeleteEnv, setConfirmDeleteEnv] = useState<string | null>(null);
  const [editEnvironment, setEditEnvironment] = useState<EnvironmentCredentials | null>(null);
  const [environmentCredentials, setEnvironmentCredentials] = useState<Record<string, any>>({});

  useEffect(() => {
    loadEnvironments();
  }, []);

  const loadEnvironments = async () => {
    setLoading(true);
    try {
      const data = await apiService.getEnvironments();
      setEnvironments(data.environments || []);
      setActiveEnvironment(data.active_environment || null);
      
      // Fetch detailed credentials for each environment (using main credentials API)
      if (data.environments && data.environments.length > 0) {
        const credentialsMap: Record<string, any> = {};
        
        try {
          // Get all credentials at once
          const allCredentials = await apiService.getCredentials();
          
          // Map the credentials to their respective environments
          if (allCredentials && allCredentials.environments) {
            for (const env of allCredentials.environments) {
              credentialsMap[env.name] = env;
            }
          }
          
          setEnvironmentCredentials(credentialsMap);
        } catch (error) {
          console.error(`Failed to load credentials:`, error);
        }
      }
    } catch (error) {
      console.error('Failed to load environments:', error);
      toast({
        title: 'Error',
        description: 'Failed to load environments. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async (environmentName: string) => {
    setTestingConnection(environmentName);
    try {
      const result = await apiService.testEnvironmentConnection(environmentName);
      if (result.status === 'success') {
        toast({
          title: 'Connection Successful',
          description: `Successfully connected to ${environmentName}`,
        });
      } else {
        toast({
          title: 'Connection Failed',
          description: result.message || `Failed to connect to ${environmentName}`,
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Connection test error:', error);
      toast({
        title: 'Connection Failed',
        description: `Failed to connect to ${environmentName}. Please check your credentials.`,
        variant: 'destructive',
      });
    } finally {
      setTestingConnection(null);
    }
  };

  const handleSetActive = async (environmentName: string) => {
    try {
      await apiService.setActiveEnvironment(environmentName);
      setActiveEnvironment(environmentName);
      
      // Clear selected project when changing environments
      localStorage.removeItem('selectedProject');
      
      toast({
        title: 'Environment Activated',
        description: `Successfully set ${environmentName} as the active environment`,
      });
      
      // Refresh the environments list
      loadEnvironments();
      
      // Close the environment manager if callback provided
      if (onClose) {
        onClose();
      }
      
      // Force a full page reload to ensure consistent state across the application
      setTimeout(() => {
        window.location.href = '/';
      }, 300);
    } catch (error) {
      console.error('Failed to set active environment:', error);
      toast({
        title: 'Error',
        description: 'Failed to set active environment. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteEnvironment = async (environmentName: string) => {
    try {
      const isActive = environmentName === activeEnvironment;
      
      // Delete the environment
      await apiService.deleteEnvironment(environmentName);
      
      // If we deleted the active environment, handle the situation
      if (isActive) {
        // Check if there are other environments we can make active
        const remainingEnvironments = environments.filter(env => env.name !== environmentName);
        
        if (remainingEnvironments.length > 0) {
          // Try to set the default environment as active, or just use the first one
          const defaultEnv = remainingEnvironments.find(env => env.is_default);
          const newActiveEnv = defaultEnv || remainingEnvironments[0];
          
          try {
            await apiService.setActiveEnvironment(newActiveEnv.name);
            setActiveEnvironment(newActiveEnv.name);
            
            toast({
              title: 'Environment Changed',
              description: `Set ${newActiveEnv.name} as the active environment`,
            });
            
            // Clear selected project since we changed environments
            localStorage.removeItem('selectedProject');
          } catch (error) {
            console.error('Failed to set new active environment:', error);
          }
        } else {
          // No environments left, clear active environment state
          setActiveEnvironment(null);
          
          // Clear selected project since we have no active environment
          localStorage.removeItem('selectedProject');
          
          toast({
            title: 'No Active Environment',
            description: 'There are no environments configured. Please add one.',
            variant: 'destructive',
          });
        }
      }
      
      toast({
        title: 'Environment Deleted',
        description: `Successfully deleted environment ${environmentName}`,
      });
      
      // Refresh the environments list
      loadEnvironments();
      
      // If we deleted the active environment, force a page reload to ensure consistent state
      if (isActive) {
        setTimeout(() => {
          window.location.href = '/';
        }, 500);
      }
    } catch (error) {
      console.error('Failed to delete environment:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete environment. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setConfirmDeleteEnv(null);
    }
  };
  
  const handleEditEnvironment = (name: string) => {
    const envCredentials = environmentCredentials[name];
    if (envCredentials) {
      const editEnv: EnvironmentCredentials = {
        name: envCredentials.name,
        is_default: environments.find(e => e.name === name)?.is_default || false,
        credentials: {
          url: envCredentials.credentials?.url || '',
          username: envCredentials.credentials?.username || '',
          password: envCredentials.credentials?.password || '',
          description: envCredentials.credentials?.description || '',
        }
      };
      
      setEditEnvironment(editEnv);
    }
  };
  
  const handleSaveEnvironment = async (formData: EnvironmentCredentials) => {
    try {
      await apiService.saveEnvironment(formData);
      toast({
        title: 'Environment Saved',
        description: `Successfully saved environment ${formData.name}`,
      });
      
      setShowAddForm(false);
      setEditEnvironment(null);
      // Refresh the environments list
      loadEnvironments();
    } catch (error) {
      console.error('Failed to save environment:', error);
      toast({
        title: 'Error',
        description: 'Failed to save environment. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Environment Manager</CardTitle>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={loadEnvironments}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={() => setShowAddForm(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Environment
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Spinner />
          </div>
        ) : environments.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">No environments configured</p>
            <Button onClick={() => setShowAddForm(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Environment
            </Button>
          </div>
        ) : (
          <ScrollArea className="h-[400px] overflow-hidden">
            <div className="w-full max-w-full">
              <Table className="w-full">
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[20%] min-w-[120px]">Name</TableHead>
                    <TableHead className="w-[35%] min-w-[150px]">URL</TableHead>
                    <TableHead className="w-[15%] min-w-[80px]">Status</TableHead>
                    <TableHead className="w-[30%] min-w-[200px] text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {environments.map((env) => (
                    <TableRow key={env.name}>
                      <TableCell className="font-medium max-w-[20%]">
                        <div className="flex items-center flex-wrap gap-1">
                          <span className="truncate">{env.name}</span>
                          {env.is_active && (
                            <Badge variant="default" className="ml-1">Active</Badge>
                          )}
                          {env.is_default && (
                            <Badge variant="outline" className="ml-1">Default</Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-xs truncate max-w-[35%]">
                        {environmentCredentials[env.name]?.credentials?.url || 'N/A'}
                      </TableCell>
                      <TableCell className="max-w-[15%]">
                        {env.is_active ? (
                          <span className="flex items-center text-green-600">
                            <CheckCircle2 className="h-4 w-4 mr-1 flex-shrink-0" />
                            <span className="truncate">Active</span>
                          </span>
                        ) : (
                          <span className="text-muted-foreground truncate">Inactive</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right max-w-[30%]">
                        <div className="flex justify-end items-center flex-wrap gap-2">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="flex-shrink-0"
                            onClick={() => handleTestConnection(env.name)}
                            disabled={testingConnection === env.name}
                          >
                            {testingConnection === env.name ? (
                              <>
                                <Spinner className="h-4 w-4 mr-2" />
                                Testing...
                              </>
                            ) : 'Test'}
                          </Button>
                          
                          <Button 
                            variant="outline" 
                            size="sm"
                            className="flex-shrink-0" 
                            onClick={() => handleEditEnvironment(env.name)}
                          >
                            <Settings className="h-4 w-4" />
                          </Button>
                          
                          {!env.is_active && (
                            <Button 
                              variant="default" 
                              size="sm"
                              className="flex-shrink-0"
                              onClick={() => handleSetActive(env.name)}
                            >
                              Set Active
                            </Button>
                          )}
                          
                          <Dialog open={confirmDeleteEnv === env.name} onOpenChange={(open) => !open && setConfirmDeleteEnv(null)}>
                            <DialogTrigger asChild>
                              <Button 
                                variant="destructive" 
                                size="sm"
                                className="flex-shrink-0"
                                onClick={() => setConfirmDeleteEnv(env.name)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Delete Environment</DialogTitle>
                                <DialogDescription>
                                  Are you sure you want to delete the environment "{env.name}"? This action cannot be undone.
                                </DialogDescription>
                              </DialogHeader>
                              <DialogFooter className="gap-2 sm:gap-0">
                                <Button
                                  variant="outline"
                                  onClick={() => setConfirmDeleteEnv(null)}
                                >
                                  Cancel
                                </Button>
                                <Button
                                  variant="destructive"
                                  onClick={() => handleDeleteEnvironment(env.name)}
                                >
                                  Delete
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </ScrollArea>
        )}
      </CardContent>

      {/* Add Environment Dialog */}
      <Dialog open={showAddForm} onOpenChange={setShowAddForm}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add New Environment</DialogTitle>
            <DialogDescription>
              Enter the connection details for your MicroStrategy environment.
            </DialogDescription>
          </DialogHeader>
          <EnvironmentConnectionForm 
            onSuccess={() => {
              setShowAddForm(false);
              loadEnvironments();
            }}
            onCancel={() => setShowAddForm(false)}
          />
        </DialogContent>
      </Dialog>
      
      {/* Edit Environment Dialog */}
      <Dialog open={!!editEnvironment} onOpenChange={(open) => !open && setEditEnvironment(null)}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Environment</DialogTitle>
            <DialogDescription>
              Update the connection details for your MicroStrategy environment.
            </DialogDescription>
          </DialogHeader>
          {editEnvironment && (
            <EnvironmentConnectionForm 
              initialData={editEnvironment}
              isEditMode={true}
              onSuccess={() => {
                setEditEnvironment(null);
                loadEnvironments();
              }}
              onCancel={() => setEditEnvironment(null)}
            />
          )}
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default EnvironmentManager; 