
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 30 98% 49%;
    --primary-foreground: 0 0% 100%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 30 98% 49%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 30 98% 49%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 30 98% 49%;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;

    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;

    --popover: 224 71% 4%;
    --popover-foreground: 213 31% 91%;

    --primary: 30 98% 49%;
    --primary-foreground: 0 0% 100%;

    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;

    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;

    --accent: 216 34% 17%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 216 34% 17%;
    --input: 216 34% 17%;
    --ring: 30 98% 49%;

    --sidebar-background: 224 71% 4%;
    --sidebar-foreground: 213 31% 91%;
    --sidebar-primary: 30 98% 49%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 216 34% 17%;
    --sidebar-accent-foreground: 213 31% 91%;
    --sidebar-border: 216 34% 17%;
    --sidebar-ring: 30 98% 49%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-display tracking-tight;
  }
}

@layer components {
  .glass-card {
    @apply bg-white bg-opacity-10 backdrop-blur-lg border border-white border-opacity-20 rounded-xl shadow-glass;
  }
  
  .glassmorphism {
    @apply bg-gradient-glass backdrop-filter backdrop-blur-md bg-opacity-20 border border-white border-opacity-20 shadow-glass;
  }
  
  .text-balance {
    text-wrap: balance;
  }

  .page-transition {
    @apply transition-all duration-300 ease-apple;
  }

  .slide-enter {
    @apply transform translate-x-full opacity-0;
  }

  .slide-enter-active {
    @apply transform translate-x-0 opacity-100 transition-all duration-300 ease-apple;
  }

  .slide-exit {
    @apply transform translate-x-0 opacity-100;
  }

  .slide-exit-active {
    @apply transform -translate-x-full opacity-0 transition-all duration-300 ease-apple;
  }
}
