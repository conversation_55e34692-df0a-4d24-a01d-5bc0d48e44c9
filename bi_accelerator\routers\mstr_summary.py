from fastapi import APIRouter, HTTPException
from mstrio.connection import Connection
from mstrio.project_objects.report import list_reports
from mstrio.project_objects.datasets import list_super_cubes, list_olap_cubes
from mstrio.modeling.schema.table import list_physical_tables
from mstrio.project_objects.dashboard import list_dashboards
from dotenv import load_dotenv
import os
from routers.utils import get_connection
# Load environment variables
load_dotenv()

# Initialize router
router = APIRouter(
    prefix="/api/mstr",
    tags=["MicroStrategy"],
    responses={404: {"description": "Not found"}},
)

@router.get("/project-summary/{project_id}")
async def get_project_summary(project_id: str):
    """
    Get a summary of all objects in a MicroStrategy project.
    
    Args:
        project_id (str): The ID of the MicroStrategy project
        
    Returns:
        dict: A summary of all objects in the project including:
            - Total number of dashboards, cubes, tables, and reports
            - List of reports with their properties
            - List of dashboards with their properties
            - List of cubes with their properties
    """

    try:
        # Create base connection
        project = get_connection(project_id)

        # Initialize the data structure
        data = {
            "total dashboards": "",
            "total cubes": "",
            "total table": "",
            "total reports": "",
            "reports": [],
            "dashboards": [],
            "cubes": []
        }

        # Get physical tables count
        physical_tables = list_physical_tables(connection=project)
        data["total table"] = len(physical_tables)

        # Get reports information
        reports = list_reports(connection=project)
        data["total reports"] = len(reports)
        for report in reports:
            try:
                properties = report.list_properties()
                report_data = {
                    "name": str(properties.get("name", "")),
                    "ext_type": str(properties.get("ext_type", "")),
                    "date_created": str(properties.get("date_created", "")),
                    "date_modified": str(properties.get("date_modified", "")),
                    "owner": str(properties.get("owner", ""))
                }
                data["reports"].append(report_data)
            except:
                continue

        # Get dashboards information
        dashboards = list_dashboards(connection=project)
        data["total dashboards"] = len(dashboards)
        for dashboard in dashboards:
            try:
                properties = dashboard.list_properties()
                dashboard_data = {
                    "name": str(properties.get("name", "")),
                    "ext_type": str(properties.get("ext_type", "")),
                    "date_created": str(properties.get("date_created", "")),
                    "date_modified": str(properties.get("date_modified", "")),
                    "owner": str(properties.get("owner", ""))
                }
                data["dashboards"].append(dashboard_data)
            except:
                continue

        # Get cubes information
        super_cubes = list_super_cubes(connection=project)
        olap_cubes = list_olap_cubes(connection=project)
        data["total cubes"] = len(super_cubes) + len(olap_cubes)

        # Process SuperCubes
        for cube in super_cubes:
            try:
                properties = cube.list_properties()
                cube_data = {
                    "name": str(properties.get("name", "")),
                    "ext_type": str(properties.get("ext_type", "")),
                    "date_created": str(properties.get("date_created", "")),
                    "date_modified": str(properties.get("date_modified", "")),
                    "owner": str(properties.get("owner", ""))
                }
                data["cubes"].append(cube_data)
            except:
                continue

        # Process OLAP Cubes
        for cube in olap_cubes:
            try:
                properties = cube.list_properties()
                cube_data = {
                    "name": str(properties.get("name", "")),
                    "ext_type": str(properties.get("ext_type", "")),
                    "date_created": str(properties.get("date_created", "")),
                    "date_modified": str(properties.get("date_modified", "")),
                    "owner": str(properties.get("owner", ""))
                }
                data["cubes"].append(cube_data)
            except:
                continue

        return data

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 