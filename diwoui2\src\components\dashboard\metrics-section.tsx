import { MetricCard } from "@/components/dashboard/metric-card";
import { Activity, FileText, Layers, Clock, Target } from "lucide-react";
import { useDashboardStore } from "@/store/dashboardStore";
import { useBrandTheme } from "@/themes/brand-theme-provider";
import { cn } from "@/lib/utils";

export function MetricsSection() {
  const { metrics } = useDashboardStore();
  const { currentTheme } = useBrandTheme();
  
  // Get theme primary color or fallback to darker red
  const primaryColor = 
    currentTheme?.colors.primary[500] || 
    "var(--theme-primary, #D32F2F)";
  
  // Primary color for lighter backgrounds
  const primaryBgLight = 
    currentTheme?.colors.primary[50] || 
    "var(--override-red-50, #FFEBEE)";
    
  // Get theme-specific icon classes
  const getIconClass = () => {
    if (currentTheme?.id === 'purple') {
      return "text-purple-600";
    } else if (currentTheme?.id === 'green') {
      return "text-green-600";
    } else if (currentTheme?.id === 'google') {
      return "text-blue-600";
    } else {
      return "text-primary";
    }
  };
  
  // Get theme-specific background classes
  const getIconBgClass = () => {
    if (currentTheme?.id === 'purple') {
      return "bg-purple-50 dark:bg-purple-900/20";
    } else if (currentTheme?.id === 'green') {
      return "bg-green-50 dark:bg-green-900/20";
    } else if (currentTheme?.id === 'google') {
      return "bg-blue-50 dark:bg-blue-900/20";
    } else {
      return "bg-primary/10 dark:bg-primary/20";
    }
  };

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
      <MetricCard
        title="AI Score Average"
        value={`${metrics.aiScoreAverage.value}%`}
        icon={<Target className={cn("h-5 w-5 zoom-animation", getIconClass())} style={{ color: primaryColor }} />}
        iconBg={getIconBgClass()}
        iconBgStyle={{ backgroundColor: primaryBgLight }}
        showProgress
        progress={metrics.aiScoreAverage.value}
        progressColors={{
          from: currentTheme?.id === 'purple' ? "from-purple-500" : 
                currentTheme?.id === 'green' ? "from-green-500" : "from-primary",
          to: "to-blue-500",
          fromColor: primaryColor,
        }}
      />
      <MetricCard
        title="Active Use Cases"
        value={metrics.activeUseCases.value}
        icon={<FileText className={cn("h-5 w-5", getIconClass())} style={{ color: primaryColor }} />}
        iconBg={getIconBgClass()}
        iconBgStyle={{ backgroundColor: primaryBgLight }}
      />
      <MetricCard
        title="Recent Insights"
        value={metrics.recentInsights.value}
        icon={<Layers className={cn("h-5 w-5", getIconClass())} style={{ color: primaryColor }} />}
        iconBg={getIconBgClass()}
        iconBgStyle={{ backgroundColor: primaryBgLight }}
      />
      <MetricCard
        title="Last Activity"
        value="2h ago"
        icon={<Clock className={cn("h-5 w-5", getIconClass())} style={{ color: primaryColor }} />}
        iconBg={getIconBgClass()}
        iconBgStyle={{ backgroundColor: primaryBgLight }}
      />
    </div>
  );
}
