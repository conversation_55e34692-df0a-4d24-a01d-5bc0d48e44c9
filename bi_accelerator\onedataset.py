from mstrio.connection import Connection

import json
from mstrio.project_objects.report import  list_reports
from mstrio.project_objects.datasets import  list_super_cubes, list_olap_cubes
from dotenv import load_dotenv
from mstrio.modeling.schema.table import (
    list_physical_tables,
)
from mstrio.project_objects.dashboard import (
    list_dashboards,
)
from mstrio.datasources.datasource_instance import list_connected_datasource_instances
import os
import openai
import time
# Load environment variables for secure configuration management
load_dotenv()
 
# Read necessary environment variables for connecting to MicroStrategy and accessing reports and projects
URL = "http://10.1.51.211:8080/MicroStrategyLibrary/api"#os.getenv('URL')
USER = "administrator"#os.getenv('USER')
PASSWORD = ""#os.getenv('PASSWORD')

# Set OpenAI API key - Make sure to add this to your .env file
openai.api_key = os.getenv('OPENAI_API_KEY')
 
# Create a connection instance
conn = Connection(
    base_url=URL,  # Replace with your MSTR server URL
    username=<PERSON><PERSON>,           # Replace with your username
    password=<PERSON><PERSON>WORD,           # Replace with your password
    project_name=None,   # Replace with your project name
    login_mode=1                        # Adjust login_mode if needed (e.g., 1 for standard authentication)
)
 
# Establish the connection
conn.connect()
print("Connected to MicroStrategy successfully!")
 
# Let the user select a project by entering its ID
pid = "95550C99497DAAC3AC19DD86D91C4BB3"#input("\nEnter the project ID: ")
 
# Create a project-specific connection using the selected project ID
project = Connection(
    base_url=URL,
    username=USER,
    password=PASSWORD,
    project_id=pid,   # U    "total projects": "",se the selected project ID
    login_mode=1
)
project.connect()
print("\nConnected to MicroStrategy Project successfully!\n")

allsql = []
reports = list_reports(connection=project)
for i in reports:
    if hasattr(i, 'sql') and i.sql:  # Check if report has SQL and it's not empty
        allsql.append(i.sql)
    
# for i, sql in enumerate(allsql):
#     print(f"{i} : {sql}", end="\n\n")

# Initialize datamodel with the structure expected for Power BI
datamodel = {
    "tables": [],
    "relationships": []
}

def analyze_sql_with_openai(sql):
    """
    Send SQL to OpenAI API to analyze and extract table information
    """
    try:
        prompt = f"""
        You are an expert data engineer. Given the following SQL query from MicroStrategy,
        
        SQL query: {sql}
        
        Return only a JSON object with tables and relationships in this format:
        {{
          "tables": [
              {{
                 "name": "<table_name>",
                 "columns": [
                      {{"name": "<column_name>", "dataType": "<PowerBI-compatible data type>"}}
                 ]
              }}
          ],
          "relationships": [
              {{
                 "name": "rel_<fromTable>_<toTable>",
                 "fromTable": "<table_name>",
                 "fromColumn": "<column_name>",
                 "toTable": "<table_name>",
                 "toColumn": "<column_name>",
                 "crossFilteringBehavior": "OneDirection"
              }}
          ]
        }}
        
        Analyze the SQL carefully for JOIN statements and WHERE clauses that indicate relationships.
        Infer data types from column usage patterns.

        note:
        - sql from microstrategy
        - do not consider temporary tables(mstrio temporary table).
        - Use data types such as "Int64", "string", "double", "DateTime", or "bool".
        """
        
        response = openai.chat.completions.create(
            model="o3-mini",  # Using compact model for SQL analysis
            messages=[
                {"role": "system", "content": "You are an expert data modeler and SQL analyst."},
                {"role": "user", "content": prompt}
            ],
        )
        
        result = response.choices[0].message.content
        try:
            result_json = json.loads(result)
            return result_json
        except json.JSONDecodeError:
            print("Error parsing JSON from OpenAI response. Skipping this SQL.")
            return None
            
    except Exception as e:
        print(f"Error calling OpenAI API: {e}")
        return None

def merge_into_datamodel(partial_model, datamodel):
    """
    Merge tables and relationships from partial model into main datamodel,
    avoiding duplicates
    """
    # Process tables
    for new_table in partial_model.get("tables", []):
        # Check if table already exists
        existing_table = next((t for t in datamodel["tables"] if t["name"].lower() == new_table["name"].lower()), None)
        
        if existing_table:
            # Update existing table with any new columns
            for new_column in new_table.get("columns", []):
                if not any(c["name"].lower() == new_column["name"].lower() for c in existing_table["columns"]):
                    existing_table["columns"].append(new_column)
        else:
            # Add new table
            datamodel["tables"].append(new_table)
    
    # Process relationships
    for new_rel in partial_model.get("relationships", []):
        # Check if relationship already exists (case-insensitive comparison)
        if not any(
            r["fromTable"].lower() == new_rel["fromTable"].lower() and 
            r["fromColumn"].lower() == new_rel["fromColumn"].lower() and
            r["toTable"].lower() == new_rel["toTable"].lower() and
            r["toColumn"].lower() == new_rel["toColumn"].lower()
            for r in datamodel["relationships"]
        ):
            datamodel["relationships"].append(new_rel)
    
    return datamodel

# Process each SQL query
print(f"Processing {len(allsql)} SQL queries...")
for i, sql in enumerate(allsql):
    print(f"Processing SQL {i+1}/{len(allsql)}...")
    if not sql or sql.strip() == "":
        print("  Empty SQL. Skipping.")
        continue
        
    # Call OpenAI to analyze the SQL without sending full datamodel
    partial_model = analyze_sql_with_openai(sql)
    
    if partial_model:
        # Merge the partial model into the main datamodel locally
        datamodel = merge_into_datamodel(partial_model, datamodel)
    
    # Avoid rate limiting
    time.sleep(1)

# Save the consolidated datamodel to a text file
with open("consolidated_datamodel.txt", "w") as f:
    f.write(json.dumps(datamodel, indent=2))

# Save all extracted SQL queries to a text file
with open("all_sqls.txt", "w") as f:
    for i, sql in enumerate(allsql):
        f.write(f"--- SQL Query {i+1} ---\n")
        f.write(sql)
        f.write("\n\n")
print("\nData model extraction complete!")
print(f"Processed {len(allsql)} SQL queries")
print(f"Extracted {len(datamodel['tables'])} tables and {len(datamodel['relationships'])} relationships")
print("Data model saved to consolidated_datamodel.txt")
print("All SQL queries saved to all_sqls.txt")


