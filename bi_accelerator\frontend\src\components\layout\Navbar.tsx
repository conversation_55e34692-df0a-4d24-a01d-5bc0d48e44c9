import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Menu, Search, Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface NavbarProps {
  toggleSidebar: () => void;
}

const Navbar: React.FC<NavbarProps> = ({ toggleSidebar }) => {
  const navigate = useNavigate();
  const location = useLocation();
  
  const getPageTitle = () => {
    const path = location.pathname;
    
    if (path === '/') return 'Dashboard';
    if (path === '/projects') return 'Projects';
    if (path.startsWith('/reports')) return 'Reports';
    if (path.startsWith('/cubes')) return 'Cubes';
    if (path.startsWith('/dashboards')) return 'Dashboards';
    
    // Extract ID for detail pages
    const segments = path.split('/').filter(Boolean);
    if (segments.length >= 2) {
      const id = segments[1];
      return `${segments[0].charAt(0).toUpperCase() + segments[0].slice(1, -1)} Detail: ${id.substring(0, 8)}...`;
    }
    
    return 'Dashboard';
  };

  return (
    <>
      <header className="sticky top-0 z-10 flex items-center h-16 bg-background/95 backdrop-blur-sm shadow-sm border-b border-border">
        <div className="flex items-center justify-between w-full px-4 md:px-6">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={toggleSidebar}
            >
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle menu</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="hidden md:flex"
              onClick={toggleSidebar}
            >
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle menu</span>
            </Button>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="icon" className="hidden md:flex">
              <Search className="h-5 w-5" />
              <span className="sr-only">Search</span>
            </Button>
            <Button variant="ghost" size="icon">
              <Bell className="h-5 w-5" />
              <span className="sr-only">Notifications</span>
            </Button>
            <div className="h-8 w-8 rounded-full bg-brand-400 flex items-center justify-center text-white font-medium">
              U
            </div>
          </div>
        </div>
      </header>
    </>
  );
};

export default Navbar;
