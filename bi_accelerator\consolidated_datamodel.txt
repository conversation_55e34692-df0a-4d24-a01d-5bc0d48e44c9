{"tables": [{"name": "CB_15Min", "columns": [{"name": "SA15MBEGIN", "dataType": "DateTime"}, {"name": "SA15MEND", "dataType": "DateTime"}, {"name": "SADAYID", "dataType": "Int64"}, {"name": "SAWKID", "dataType": "Int64"}, {"name": "SAMNTHID", "dataType": "Int64"}, {"name": "SAQTRID", "dataType": "Int64"}, {"name": "SAYRID", "dataType": "Int64"}, {"name": "SADAYOFWKID", "dataType": "Int64"}, {"name": "SAHROFDAYID", "dataType": "Int64"}]}, {"name": "CB_30Min", "columns": [{"name": "SA30MBEGIN", "dataType": "DateTime"}, {"name": "SA30MEND", "dataType": "DateTime"}, {"name": "SADAYID", "dataType": "Int64"}, {"name": "SAWKID", "dataType": "Int64"}, {"name": "SAMNTHID", "dataType": "Int64"}, {"name": "SAQTRID", "dataType": "Int64"}, {"name": "SAYRID", "dataType": "Int64"}, {"name": "SADAYOFWKID", "dataType": "Int64"}, {"name": "SAHROFDAYID", "dataType": "Int64"}]}, {"name": "CB_60Min", "columns": [{"name": "SAHOURBEGIN", "dataType": "DateTime"}, {"name": "SAHOUREND", "dataType": "DateTime"}, {"name": "SADAYID", "dataType": "Int64"}, {"name": "SAWKID", "dataType": "Int64"}, {"name": "SAMNTHID", "dataType": "Int64"}, {"name": "SAQTRID", "dataType": "Int64"}, {"name": "SAYRID", "dataType": "Int64"}, {"name": "SADAYOFWKID", "dataType": "Int64"}, {"name": "SAHROFDAYID", "dataType": "Int64"}]}, {"name": "CB_Day", "columns": [{"name": "SADAYBEGIN", "dataType": "DateTime"}, {"name": "SADAYEND", "dataType": "DateTime"}, {"name": "SADAYID", "dataType": "Int64"}, {"name": "SAWKID", "dataType": "Int64"}, {"name": "SAMNTHID", "dataType": "Int64"}, {"name": "SAQTRID", "dataType": "Int64"}, {"name": "SAYRID", "dataType": "Int64"}, {"name": "SADAYOFWKID", "dataType": "Int64"}]}, {"name": "CB_Week", "columns": [{"name": "SAWKBEGIN", "dataType": "DateTime"}, {"name": "SAWKEND", "dataType": "DateTime"}, {"name": "SAWKID", "dataType": "Int64"}]}, {"name": "CB_Month", "columns": [{"name": "SAMNTHBEGIN", "dataType": "DateTime"}, {"name": "SAMNTHEND", "dataType": "DateTime"}, {"name": "SAMNTHID", "dataType": "Int64"}, {"name": "SAQTRID", "dataType": "Int64"}, {"name": "SAYRID", "dataType": "Int64"}]}, {"name": "CB_Quarter", "columns": [{"name": "SAQTRBEGIN", "dataType": "DateTime"}, {"name": "SAQTREND", "dataType": "DateTime"}, {"name": "SAQTRID", "dataType": "Int64"}, {"name": "SAYRID", "dataType": "Int64"}]}, {"name": "CB_Year", "columns": [{"name": "SAYRBEGIN", "dataType": "DateTime"}, {"name": "SAYREND", "dataType": "DateTime"}, {"name": "SAYRID", "dataType": "Int64"}]}, {"name": "CB_DW", "columns": [{"name": "SADAYOFWKID", "dataType": "Int64"}]}, {"name": "CB_HD", "columns": [{"name": "SAHROFDAYBEGIN", "dataType": "DateTime"}, {"name": "SAHROFDAYID", "dataType": "Int64"}]}, {"name": "Leavebalance", "columns": [{"name": "Employee_ID", "dataType": "Int64"}, {"name": "Availed_PTO", "dataType": "double"}, {"name": "Availed_Compoff", "dataType": "double"}, {"name": "Balance_Compoff", "dataType": "double"}, {"name": "Loss_of_Pay", "dataType": "double"}, {"name": "Total_WFH", "dataType": "double"}, {"name": "Total_PTO", "dataType": "double"}, {"name": "Balance_WFH", "dataType": "double"}, {"name": "Balance_PTO", "dataType": "double"}]}, {"name": "<PERSON><PERSON>", "columns": [{"name": "Employee_ID", "dataType": "Int64"}, {"name": "Active", "dataType": "string"}, {"name": "Designation", "dataType": "string"}, {"name": "Department", "dataType": "string"}]}, {"name": "Employee", "columns": [{"name": "Employee_ID", "dataType": "Int64"}, {"name": "Manager_Name", "dataType": "string"}, {"name": "Manager_ID", "dataType": "Int64"}, {"name": "Name", "dataType": "string"}, {"name": "Gender", "dataType": "string"}, {"name": "Email", "dataType": "string"}]}, {"name": "QuarterProcess", "columns": [{"name": "Employee_ID", "dataType": "Int64"}, {"name": "Year", "dataType": "Int64"}, {"name": "Quarter", "dataType": "Int64"}, {"name": "To_Date", "dataType": "DateTime"}, {"name": "From_Date", "dataType": "DateTime"}]}, {"name": "Swipe", "columns": [{"name": "Employee_ID", "dataType": "Int64"}, {"name": "First_Check_In", "dataType": "string"}, {"name": "Last_Check_Out", "dataType": "string"}]}], "relationships": [{"name": "rel_<PERSON><PERSON><PERSON>_Login", "fromTable": "Leavebalance", "fromColumn": "Employee_ID", "toTable": "<PERSON><PERSON>", "toColumn": "Employee_ID", "crossFilteringBehavior": "OneDirection"}, {"name": "rel_Leavebalance_Employee", "fromTable": "Leavebalance", "fromColumn": "Employee_ID", "toTable": "Employee", "toColumn": "Employee_ID", "crossFilteringBehavior": "OneDirection"}, {"name": "rel_Leavebalance_QuarterProcess", "fromTable": "Leavebalance", "fromColumn": "Employee_ID", "toTable": "QuarterProcess", "toColumn": "Employee_ID", "crossFilteringBehavior": "OneDirection"}, {"name": "rel_Leave<PERSON>ance_Swipe", "fromTable": "Leavebalance", "fromColumn": "Employee_ID", "toTable": "Swipe", "toColumn": "Employee_ID", "crossFilteringBehavior": "OneDirection"}]}