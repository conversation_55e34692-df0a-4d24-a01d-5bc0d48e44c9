from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
import logging
import traceback
from mstrio.project_objects.dashboard import Dashboard, list_dashboards
from routers.utils import get_connection, get_credentials
import requests
import json

logger = logging.getLogger("bi_accelerator")
router = APIRouter(prefix="/api", tags=["dashboards"])

@router.get("/dashboards")
async def get_dashboards(project_id: str):
    logger.info(f"Fetching dashboards for project_id: {project_id}")
    try:
        # Connect to the selected project and fetch dashboards
        project_conn = get_connection(project_id)
        dashboards = list_dashboards(connection=project_conn, project_id=project_id)
        dashboards_data = [{"id": d.id, "name": d.name} for d in dashboards]
        logger.info(f"Successfully retrieved {len(dashboards_data)} dashboards for project {project_id}")
        logger.debug(f"Dashboards data: {dashboards_data}")
        return JSONResponse(content=dashboards_data)
    except Exception as e:
        logger.error(f"Error retrieving dashboards for project {project_id}: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to retrieve dashboards: {str(e)}")
@router.get("/dashboard/{dashboard_id}")
async def get_dashboard_details(dashboard_id: str, project_id: str):
    logger.info(f"Fetching details for dashboard_id: {dashboard_id} in project_id: {project_id}")
    # Retrieve dashboard details, including chapters, pages, and visualizations.
    try:
        project_conn = get_connection(project_id)
        dashboard_obj = Dashboard(connection=project_conn, id=dashboard_id)
        logger.debug(f"Successfully loaded dashboard object with name: {dashboard_obj.name}")
        
        # Get chapters, pages, and visualizations
        chapters = []
        try:
            for chapter in dashboard_obj.chapters:
                chapter_info = {
                    "key": chapter.key,
                    "name": chapter.name,
                    "pages": []
                }
                for page in chapter.pages:
                    page_info = {
                        "key": page.key,
                        "name": page.name,
                        "visualizations": [
                            {
                                "key": v.key, 
                                "name": v.name, 
                                "type": str(v.visualization_type)
                            }
                            for v in page.visualizations
                        ]
                    }
                    chapter_info["pages"].append(page_info)
                chapters.append(chapter_info)
            logger.debug(f"Retrieved {len(chapters)} chapters for dashboard {dashboard_id}")
        except Exception as ch_err:
            logger.error(f"Error retrieving chapters for dashboard {dashboard_id}: {str(ch_err)}")
            logger.error(traceback.format_exc())
            chapters = [{"error": str(ch_err)}]
        
        # Get connected cubes
        connected_cubes = []
        try:
            cubes = dashboard_obj.get_connected_cubes()
            for cube in cubes:
                connected_cubes.append({
                    "id": cube.id,
                    "name": cube.name,
                    "type": cube.__class__.__name__
                })
            logger.debug(f"Retrieved {len(connected_cubes)} connected cubes for dashboard {dashboard_id}")
        except Exception as cube_err:
            logger.error(f"Error retrieving connected cubes for dashboard {dashboard_id}: {str(cube_err)}")
            logger.error(traceback.format_exc())
            connected_cubes = [{"error": str(cube_err)}]
        
        # Get properties with error handling and convert non-serializable objects to strings
        properties = {}
        try:
            if hasattr(dashboard_obj, "list_properties"):
                raw_properties = dashboard_obj.list_properties()
                # Convert non-serializable objects to their string representation
                for key, value in raw_properties.items():
                    if isinstance(value, (str, int, float, bool, type(None))):
                        properties[key] = value
                    elif isinstance(value, (list, tuple)):
                        # For collections, convert each item if needed
                        try:
                            properties[key] = [str(item) if not isinstance(item, (str, int, float, bool, type(None), dict, list)) else item for item in value]
                        except:
                            properties[key] = str(value)
                    elif isinstance(value, dict):
                        # For dictionaries, attempt to convert non-serializable values
                        try:
                            serializable_dict = {}
                            for k, v in value.items():
                                if isinstance(v, (str, int, float, bool, type(None), dict, list)):
                                    serializable_dict[k] = v
                                else:
                                    serializable_dict[k] = str(v)
                            properties[key] = serializable_dict
                        except:
                            properties[key] = str(value)
                    else:
                        # For any other type, convert to string
                        properties[key] = str(value)
            
            logger.debug(f"Retrieved properties for dashboard {dashboard_id}")
        except Exception as prop_err:
            logger.error(f"Error retrieving properties for dashboard {dashboard_id}: {str(prop_err)}")
            properties = {"error": str(prop_err)}
        
        details = {
            "id": dashboard_id,
            "name": dashboard_obj.name,
            "properties": properties,
            "chapters": chapters,
            "connected_cubes": connected_cubes
        }
        
        logger.info(f"Successfully retrieved details for dashboard {dashboard_id}")
        return JSONResponse(content=details)
    except Exception as e:
        logger.error(f"Error retrieving dashboard {dashboard_id} details: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=404, detail=f"Dashboard not found or error: {str(e)}") 


@router.get("/dashboard/{dashboard_id}/chapters/{chapter_key}/visualizations/{visualization_key}/data")
async def get_visualization_data(
    dashboard_id: str,
    chapter_key: str,
    visualization_key: str,
    project_id: str,
    offset: int = 0,
    limit: int = 12000
):
    """
    Fetch raw data for a specific visualization within a chapter/page of a MicroStrategy Dossier.
    
    Steps:
      1) Authenticate to get session cookies
      2) Create a Dossier instance
      3) Retrieve the visualization data for the given instance, chapter, and visualization.
    """
    logger.info(f"Request to retrieve visualization data for: Dashboard={dashboard_id}, Chapter={chapter_key}, "
                f"Viz={visualization_key}, Project={project_id}, offset={offset}, limit={limit}")
    
    try:
        # Get credentials
        credentials = get_credentials()
        if not credentials:
            logger.error("Failed to load credentials")
            raise HTTPException(status_code=500, detail="Failed to load credentials")
        
        # Default to not verifying SSL
        verify_ssl = False
        
        # Configure session with specific settings
        session = requests.Session()
        
        # SSL verification options - disable warning if we're not verifying
        if not verify_ssl:
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        # ---------------------------------------------------------------------
        # 1) Authenticate and obtain Session Cookies
        # ---------------------------------------------------------------------
        # Normalize base URL (ensure no trailing slash)
        base_url = credentials['url'].rstrip('/')
        auth_url = f"{base_url}/auth/login"
        
        # Prepare login payload
        login_payload = {
            "username": credentials['username'],
            "password": credentials['password'],
            "loginMode": 1
        }
        
        # Common headers used across all requests
        common_headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        
        logger.info(f"Authenticating via URL: {auth_url}")
        
        try:
            # Make the login request
            login_resp = session.post(
                auth_url,
                json=login_payload,
                headers=common_headers,
                verify=verify_ssl,
                timeout=30
            )
            
            logger.debug(f"Login status code: {login_resp.status_code}")
            
            if not login_resp.ok:
                logger.error(f"Authentication failed: Status code {login_resp.status_code}")
                logger.error(f"Response content: {login_resp.text}")
                raise HTTPException(
                    status_code=401,
                    detail={
                        "message": "Authentication to MicroStrategy failed",
                        "status": login_resp.status_code,
                        "response": login_resp.text
                    }
                )
            
            # Get auth token from cookies first, then headers
            auth_token = None
            cookies_dict = login_resp.cookies.get_dict()
            
            if 'iSession' in cookies_dict:
                auth_token = cookies_dict['iSession']
                logger.info("Using iSession cookie for authentication")
            elif login_resp.headers.get("X-MSTR-AuthToken"):
                auth_token = login_resp.headers.get("X-MSTR-AuthToken")
                logger.info("Using X-MSTR-AuthToken header for authentication")
            
            if not auth_token:
                logger.error("No authentication token found in response")
                raise HTTPException(
                    status_code=401,
                    detail={"message": "Authentication failed - no session token found"}
                )
            
            # ---------------------------------------------------------------------
            # 2) Create a Dossier instance
            # ---------------------------------------------------------------------
            instance_url = f"{base_url}/dossiers/{dashboard_id}/instances"
            
            # Update headers with auth token and project ID
            request_headers = {
                **common_headers,
                "X-MSTR-AuthToken": auth_token,
                "X-MSTR-ProjectID": project_id
            }
            
            logger.info(f"Creating dossier instance via: {instance_url}")
            logger.debug(f"Headers being sent: {request_headers}")
            
            instance_resp = session.post(
                instance_url,
                headers=request_headers,
                cookies=cookies_dict,
                json={},
                verify=verify_ssl,
                timeout=30
            )
            
            if not instance_resp.ok:
                error_msg = f"Failed to create dossier instance. Status: {instance_resp.status_code}"
                logger.error(f"{error_msg}, Response: {instance_resp.text}")
                
                if instance_resp.status_code == 404:
                    raise HTTPException(
                        status_code=404,
                        detail={"message": f"Dashboard ID not found: {dashboard_id}"}
                    )
                else:
                    raise HTTPException(
                        status_code=instance_resp.status_code,
                        detail={"message": error_msg, "response": instance_resp.text}
                    )
            
            # Parse instance data
            instance_data = instance_resp.json()
            instance_id = instance_data.get("mid")
            
            if not instance_id:
                logger.error("No dossier instance ID ('mid') found in the creation response")
                raise HTTPException(
                    status_code=500,
                    detail={"message": "Dossier instance ID not found in response"}
                )
            
            logger.info(f"Successfully created dossier instance ID: {instance_id}")
            
            # ---------------------------------------------------------------------
            # 3) Retrieve visualization data
            # ---------------------------------------------------------------------
            viz_data_url = (
                f"{base_url}/v2/dossiers/{dashboard_id}/instances/{instance_id}/"
                f"chapters/{chapter_key}/visualizations/{visualization_key}"
                f"?offset={offset}&limit={limit}"
            )
            
            logger.info(f"Fetching visualization data via: {viz_data_url}")
            
            # Use the same session with cookies and headers for visualization data request
            viz_data_resp = session.get(
                viz_data_url,
                headers=request_headers,
                cookies=cookies_dict,
                verify=verify_ssl,
                timeout=30
            )
            
            if not viz_data_resp.ok:
                error_msg = f"Failed to retrieve visualization data: Status code {viz_data_resp.status_code}"
                logger.error(f"{error_msg}, Response: {viz_data_resp.text}")
                raise HTTPException(
                    status_code=viz_data_resp.status_code,
                    detail={"message": error_msg, "response": viz_data_resp.text}
                )
            
            # Parse and return visualization data with metadata
            viz_data = viz_data_resp.json()
            logger.info("Successfully retrieved visualization data")
            
            return viz_data
            
        except requests.exceptions.RequestException as req_err:
            logger.error(f"Request exception: {str(req_err)}")
            raise HTTPException(
                status_code=500,
                detail={"message": f"Error communicating with MicroStrategy", "error": str(req_err)}
            )
            
    except HTTPException as http_err:
        # Re-raise HTTP exceptions with their original status code and detail
        raise http_err
    except Exception as e:
        logger.error(f"Unexpected error while retrieving visualization data: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail={"message": "Unexpected error", "error": str(e)}
        )
    finally:
        # Attempt to close the session if it exists
        if 'session' in locals():
            session.close()