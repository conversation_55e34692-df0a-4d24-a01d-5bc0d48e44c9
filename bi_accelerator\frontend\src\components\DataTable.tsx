import React, { useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Input } from "@/components/ui/input";
import { 
  Card, 
  CardContent 
} from "@/components/ui/card";
import { 
  Search, 
  Filter, 
  Calendar, 
  Clock, 
  ArrowUpDown,
  ChevronDown,
  ChevronUp,
  Download,
  RefreshCw,
  Maximize2,
  Minimize2,
  X
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface DataTableProps {
  data: {
    Project: string;
    Object: string;
    GUID: string;
    'Creation Time': string | Date;
    'Modification Time': string | Date;
    'Object Type': string;
    'Object Owner': string;
    'Last Action Timestamp (UTC)': string | Date;
    'No of Action': number;
  }[];
  onFilteredDataChange?: (filteredData: any[]) => void;
}

export function DataTable({ data, onFilteredDataChange }: DataTableProps) {
  const [selectedProject, setSelectedProject] = React.useState<string>("all");
  const [lastActionFilter, setLastActionFilter] = React.useState<string>("all");
  const [currentPage, setCurrentPage] = React.useState(1);
  const [itemsPerPage, setItemsPerPage] = React.useState(10);
  const [searchTerm, setSearchTerm] = React.useState("");
  const [sortField, setSortField] = React.useState<string>("");
  const [sortDirection, setSortDirection] = React.useState<"asc" | "desc">("asc");
  const [isFilterDialogOpen, setIsFilterDialogOpen] = React.useState(false);
  const [objectTypeFilter, setObjectTypeFilter] = React.useState<string>("all");
  const [ownerFilter, setOwnerFilter] = React.useState<string>("all");
  const [isRefreshing, setIsRefreshing] = React.useState(false);
  const [isFullscreen, setIsFullscreen] = React.useState(false);
  
  // Get unique projects, object types, and owners for filters
  const uniqueProjects = React.useMemo(() => {
    const projects = Array.from(new Set(data.map(item => item.Project)));
    return projects.filter(project => project); // Filter out any undefined or empty values
  }, [data]);

  const uniqueObjectTypes = React.useMemo(() => {
    const types = Array.from(new Set(data.map(item => item['Object Type'])));
    return types.filter(type => type); // Filter out any undefined or empty values
  }, [data]);

  const uniqueOwners = React.useMemo(() => {
    const owners = Array.from(new Set(data.map(item => item['Object Owner'])));
    return owners.filter(owner => owner); // Filter out any undefined or empty values
  }, [data]);

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      // Toggle direction if clicking the same field
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Set new field and default to ascending
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Simulate refresh
  const handleRefresh = () => {
    setIsRefreshing(true);
    setTimeout(() => {
      setIsRefreshing(false);
    }, 800);
  };

  // Reset all filters
  const resetFilters = () => {
    setSelectedProject("all");
    setLastActionFilter("all");
    setObjectTypeFilter("all");
    setOwnerFilter("all");
    setSearchTerm("");
    setSortField("");
    setSortDirection("asc");
    setCurrentPage(1);
  };

  // Filter data based on all filters
  const filteredData = React.useMemo(() => {
    let filtered = data;
    
    // Filter by project
    if (selectedProject !== "all") {
      filtered = filtered.filter(item => item.Project === selectedProject);
    }
    
    // Filter by object type
    if (objectTypeFilter !== "all") {
      filtered = filtered.filter(item => item['Object Type'] === objectTypeFilter);
    }
    
    // Filter by owner
    if (ownerFilter !== "all") {
      filtered = filtered.filter(item => item['Object Owner'] === ownerFilter);
    }
    
    // Filter by last action
    if (lastActionFilter !== "all") {
      const now = new Date();
      filtered = filtered.filter(item => {
        if (!item['Last Action Timestamp (UTC)']) return false;
        
        try {
          const lastAction = item['Last Action Timestamp (UTC)'] instanceof Date 
            ? item['Last Action Timestamp (UTC)'] 
            : new Date(item['Last Action Timestamp (UTC)']);
          
          // Check if date is valid
          if (isNaN(lastAction.getTime())) {
            return false;
          }
          
          const diffInMilliseconds = now.getTime() - lastAction.getTime();
          const diffInDays = Math.floor(diffInMilliseconds / (1000 * 60 * 60 * 24));
          
          switch (lastActionFilter) {
            case "last7days":
              return diffInDays <= 7;
            case "last30days":
              return diffInDays <= 30;
            case "last90days":
              return diffInDays <= 90;
            case "older90days":
              return diffInDays > 90;
            case "older180days":
              return diffInDays > 180;
            case "older365days":
              return diffInDays > 365;
            default:
              return true;
          }
        } catch (error) {
          return false;
        }
      });
    }
    
    // Filter by search term
    if (searchTerm.trim() !== "") {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(item => 
        (item.Object && item.Object.toLowerCase().includes(term)) ||
        (item.GUID && item.GUID.toLowerCase().includes(term)) ||
        (item.Project && item.Project.toLowerCase().includes(term)) ||
        (item['Object Owner'] && item['Object Owner'].toLowerCase().includes(term))
      );
    }
    
    // Sort data
    if (sortField) {
      filtered = [...filtered].sort((a, b) => {
        let aValue = a[sortField as keyof typeof a];
        let bValue = b[sortField as keyof typeof b];
        
        // Handle date fields
        if (sortField.includes('Time') || sortField.includes('Timestamp')) {
          aValue = aValue ? new Date(aValue).getTime() : 0;
          bValue = bValue ? new Date(bValue).getTime() : 0;
        }
        
        // Handle string comparison
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortDirection === 'asc' 
            ? aValue.localeCompare(bValue) 
            : bValue.localeCompare(aValue);
        }
        
        // Handle numeric comparison
        if (sortDirection === 'asc') {
          return (aValue as number) - (bValue as number);
        } else {
          return (bValue as number) - (aValue as number);
        }
      });
    }
    
    return filtered;
  }, [data, selectedProject, objectTypeFilter, ownerFilter, lastActionFilter, searchTerm, sortField, sortDirection]);

  // Notify parent component about filtered data changes
  useEffect(() => {
    if (onFilteredDataChange) {
      onFilteredDataChange(filteredData);
    }
  }, [filteredData, onFilteredDataChange]);

  // Pagination logic
  const totalPages = Math.max(1, Math.ceil(filteredData.length / itemsPerPage));
  
  // Reset current page when filters change or if current page is out of bounds
  React.useEffect(() => {
    if (currentPage > totalPages) {
      setCurrentPage(1);
    }
  }, [filteredData, totalPages, currentPage]);

  // Get current page data
  const currentData = React.useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredData.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredData, currentPage, itemsPerPage]);

  // Handle slider change
  const handleSliderChange = (value: number[]) => {
    setCurrentPage(value[0]);
  };

  // Format date safely
  const formatDate = (dateValue: string | Date | undefined): string => {
    if (!dateValue) return 'N/A';
    
    try {
      const date = dateValue instanceof Date ? dateValue : new Date(dateValue);
      
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }
      
      return date.toLocaleDateString();
    } catch (error) {
      console.error("Error formatting date:", error);
      return 'Error';
    }
  };

  // Calculate age from creation time
  const calculateAge = (creationTime: string | Date) => {
    if (!creationTime) return 'N/A';
    
    try {
      const created = creationTime instanceof Date ? creationTime : new Date(creationTime);
      
      // Check if date is valid
      if (isNaN(created.getTime())) {
        return 'Invalid Date';
      }
      
      const now = new Date();
      const diffInMilliseconds = now.getTime() - created.getTime();
      const diffInDays = Math.floor(diffInMilliseconds / (1000 * 60 * 60 * 24));
      return `${diffInDays} days`;
    } catch (error) {
      return 'Error';
    }
  };

  // Calculate last action age
  const calculateLastActionAge = (lastAction: string | Date) => {
    if (!lastAction) return 'N/A';
    
    try {
      const actionDate = lastAction instanceof Date ? lastAction : new Date(lastAction);
      
      // Check if date is valid
      if (isNaN(actionDate.getTime())) {
        return 'Invalid Date';
      }
      
      const now = new Date();
      const diffInMilliseconds = now.getTime() - actionDate.getTime();
      const diffInDays = Math.floor(diffInMilliseconds / (1000 * 60 * 60 * 24));
      
      if (diffInDays === 0) {
        return 'Today';
      } else if (diffInDays === 1) {
        return 'Yesterday';
      } else if (diffInDays <= 7) {
        return `${diffInDays} days ago`;
      } else if (diffInDays <= 30) {
        const weeks = Math.floor(diffInDays / 7);
        return `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago`;
      } else if (diffInDays <= 365) {
        const months = Math.floor(diffInDays / 30);
        return `${months} ${months === 1 ? 'month' : 'months'} ago`;
      } else {
        const years = Math.floor(diffInDays / 365);
        return `${years} ${years === 1 ? 'year' : 'years'} ago`;
      }
    } catch (error) {
      return 'Error';
    }
  };

  // Render sort indicator
  const renderSortIndicator = (field: string) => {
    if (sortField !== field) return <ArrowUpDown className="ml-1 h-4 w-4 text-gray-400" />;
    return sortDirection === "asc" 
      ? <ChevronUp className="ml-1 h-4 w-4" /> 
      : <ChevronDown className="ml-1 h-4 w-4" />;
  };

  // Toggle fullscreen mode
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
    <div className="space-y-4 relative pb-24">
      <Card className="shadow-sm">
        <CardContent className="p-6">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
              <div className="relative w-full sm:w-64 md:w-72">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by object, GUID, project..."
                  className="pl-9"
                  value={searchTerm}
                  onChange={handleSearch}
                />
              </div>
              
              <Dialog open={isFilterDialogOpen} onOpenChange={setIsFilterDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" className="gap-2">
                    <Filter className="h-4 w-4" />
                    <span>Filters</span>
                    {(selectedProject !== "all" || objectTypeFilter !== "all" || ownerFilter !== "all" || lastActionFilter !== "all") && (
                      <span className="ml-1 rounded-full bg-primary w-5 h-5 text-xs flex items-center justify-center text-primary-foreground">
                        {(selectedProject !== "all" ? 1 : 0) + 
                         (objectTypeFilter !== "all" ? 1 : 0) + 
                         (ownerFilter !== "all" ? 1 : 0) + 
                         (lastActionFilter !== "all" ? 1 : 0)}
                      </span>
                    )}
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>Filter Data</DialogTitle>
                    <DialogDescription>
                      Apply filters to narrow down the results
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Project</label>
                        <Select value={selectedProject} onValueChange={setSelectedProject}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a project" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Projects</SelectItem>
                            {uniqueProjects.map((project) => (
                              <SelectItem key={project} value={project}>
                                {project}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Object Type</label>
                        <Select value={objectTypeFilter} onValueChange={setObjectTypeFilter}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select object type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Types</SelectItem>
                            {uniqueObjectTypes.map((type) => (
                              <SelectItem key={type} value={type}>
                                {type}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Object Owner</label>
                        <Select value={ownerFilter} onValueChange={setOwnerFilter}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select owner" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Owners</SelectItem>
                            {uniqueOwners.map((owner) => (
                              <SelectItem key={owner} value={owner}>
                                {owner}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Last Action</label>
                        <Select value={lastActionFilter} onValueChange={setLastActionFilter}>
                          <SelectTrigger>
                            <SelectValue placeholder="Filter by last action" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Time</SelectItem>
                            <SelectItem value="last7days">Last 7 Days</SelectItem>
                            <SelectItem value="last30days">Last 30 Days</SelectItem>
                            <SelectItem value="last90days">Last 90 Days</SelectItem>
                            <SelectItem value="older90days">Older than 90 Days</SelectItem>
                            <SelectItem value="older180days">Older than 180 Days</SelectItem>
                            <SelectItem value="older365days">Older than 1 Year</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div className="flex justify-between mt-4">
                      <Button variant="outline" onClick={resetFilters}>Reset All</Button>
                      <Button onClick={() => setIsFilterDialogOpen(false)}>Apply Filters</Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
            
            <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
              <div className="flex items-center gap-2">
                <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemsPerPage(Number(value))}>
                  <SelectTrigger className="w-[110px]">
                    <SelectValue placeholder="Rows" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 rows</SelectItem>
                    <SelectItem value="10">10 rows</SelectItem>
                    <SelectItem value="20">20 rows</SelectItem>
                    <SelectItem value="50">50 rows</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline" size="icon" className="h-9 w-9" onClick={handleRefresh}>
                      <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Refresh data</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline" size="icon" className="h-9 w-9" onClick={toggleFullscreen}>
                      {isFullscreen ? 
                        <Minimize2 className="h-4 w-4" /> : 
                        <Maximize2 className="h-4 w-4" />
                      }
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{isFullscreen ? "Exit fullscreen" : "Fullscreen view"}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="gap-2">
                    <Download className="h-4 w-4" />
                    <span>Export</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    Export to CSV
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    Export to Excel
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    Export to PDF
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          
          <div className="mt-4">
            <p className="text-sm text-muted-foreground">
              Showing {filteredData.length > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0}-
              {Math.min(currentPage * itemsPerPage, filteredData.length)} of {filteredData.length} records
              {(selectedProject !== "all" || objectTypeFilter !== "all" || ownerFilter !== "all" || lastActionFilter !== "all" || searchTerm) && (
                <Button variant="link" className="px-1 py-0 h-auto" onClick={resetFilters}>
                  Clear all filters
                </Button>
              )}
            </p>
          </div>
        </CardContent>
      </Card>
      
      {/* Fullscreen overlay */}
      {isFullscreen && (
        <div className="fixed inset-0 z-50 bg-white dark:bg-slate-950 overflow-auto flex flex-col">
          <div className="p-4 border-b flex justify-between items-center">
            <h2 className="text-lg font-medium">Data Table - Fullscreen View</h2>
            <Button variant="ghost" size="icon" onClick={toggleFullscreen}>
              <X className="h-5 w-5" />
            </Button>
          </div>
          <div className="p-4 flex-1 overflow-auto">
            <Table className="w-auto min-w-full">
              <TableHeader className="bg-slate-50 dark:bg-slate-900 sticky top-0 z-10">
                <TableRow>
                  <TableHead 
                    className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors whitespace-nowrap"
                    onClick={() => handleSort("Project")}
                  >
                    <div className="flex items-center">
                      Project
                      {renderSortIndicator("Project")}
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors whitespace-nowrap"
                    onClick={() => handleSort("Object")}
                  >
                    <div className="flex items-center">
                      Object
                      {renderSortIndicator("Object")}
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors whitespace-nowrap"
                    onClick={() => handleSort("GUID")}
                  >
                    <div className="flex items-center">
                      GUID
                      {renderSortIndicator("GUID")}
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors whitespace-nowrap"
                    onClick={() => handleSort("Creation Time")}
                  >
                    <div className="flex items-center">
                      <Calendar className="mr-1 h-4 w-4 text-gray-400" />
                      Creation Time
                      {renderSortIndicator("Creation Time")}
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors whitespace-nowrap"
                    onClick={() => handleSort("Age")}
                  >
                    <div className="flex items-center">
                      <Clock className="mr-1 h-4 w-4 text-gray-400" />
                      Age
                      {renderSortIndicator("Age")}
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors whitespace-nowrap"
                    onClick={() => handleSort("Modification Time")}
                  >
                    <div className="flex items-center">
                      <Calendar className="mr-1 h-4 w-4 text-gray-400" />
                      Modification Time
                      {renderSortIndicator("Modification Time")}
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors whitespace-nowrap"
                    onClick={() => handleSort("Object Type")}
                  >
                    <div className="flex items-center">
                      Object Type
                      {renderSortIndicator("Object Type")}
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors whitespace-nowrap"
                    onClick={() => handleSort("Object Owner")}
                  >
                    <div className="flex items-center">
                      Object Owner
                      {renderSortIndicator("Object Owner")}
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors whitespace-nowrap"
                    onClick={() => handleSort("Last Action Timestamp (UTC)")}
                  >
                    <div className="flex items-center">
                      <Calendar className="mr-1 h-4 w-4 text-gray-400" />
                      Last Action
                      {renderSortIndicator("Last Action Timestamp (UTC)")}
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors whitespace-nowrap"
                    onClick={() => handleSort("Last Action Age")}
                  >
                    <div className="flex items-center">
                      <Clock className="mr-1 h-4 w-4 text-gray-400" />
                      Last Action Age
                      {renderSortIndicator("Last Action Age")}
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors whitespace-nowrap"
                    onClick={() => handleSort("No of Action")}
                  >
                    <div className="flex items-center">
                      No of Actions
                      {renderSortIndicator("No of Action")}
                    </div>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentData.length > 0 ? (
                  currentData.map((row, index) => (
                    <TableRow 
                      key={`${row.GUID}-${index}`}
                      className="hover:bg-slate-50 dark:hover:bg-slate-900"
                    >
                      <TableCell className="font-medium whitespace-nowrap">{row.Project}</TableCell>
                      <TableCell className="whitespace-nowrap">
                        <div title={row.Object}>
                          {row.Object}
                        </div>
                      </TableCell>
                      <TableCell className="whitespace-nowrap">
                        <div className="font-mono text-xs" title={row.GUID}>
                          {row.GUID}
                        </div>
                      </TableCell>
                      <TableCell className="whitespace-nowrap">{formatDate(row['Creation Time'])}</TableCell>
                      <TableCell className="whitespace-nowrap">{calculateAge(row['Creation Time'])}</TableCell>
                      <TableCell className="whitespace-nowrap">{formatDate(row['Modification Time'])}</TableCell>
                      <TableCell className="whitespace-nowrap">{row['Object Type']}</TableCell>
                      <TableCell className="whitespace-nowrap">{row['Object Owner']}</TableCell>
                      <TableCell className="whitespace-nowrap">{formatDate(row['Last Action Timestamp (UTC)'])}</TableCell>
                      <TableCell className="whitespace-nowrap">{calculateLastActionAge(row['Last Action Timestamp (UTC)'])}</TableCell>
                      <TableCell className="text-center whitespace-nowrap">{row['No of Action']}</TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={11} className="text-center py-10">
                      <div className="flex flex-col items-center justify-center space-y-3">
                        <Search className="h-10 w-10 text-gray-400" strokeWidth={1.5} />
                        <p className="text-lg font-medium">No records found</p>
                        <p className="text-sm text-muted-foreground">Try adjusting your search or filter parameters</p>
                        <Button variant="outline" onClick={resetFilters}>Clear Filters</Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          <div className="p-4 border-t">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious 
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))} 
                    className={currentPage <= 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>
                <PaginationItem>
                  <span className="text-sm font-medium">
                    Page {currentPage} of {totalPages}
                  </span>
                </PaginationItem>
                <PaginationItem>
                  <PaginationNext 
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    className={currentPage >= totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      )}
      
      {!isFullscreen && (
        <div className="rounded-md border shadow-sm overflow-x-auto bg-white dark:bg-slate-950">
          <Table>
            <TableHeader className="bg-slate-50 dark:bg-slate-900">
              <TableRow>
                <TableHead 
                  className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
                  onClick={() => handleSort("Project")}
                >
                  <div className="flex items-center">
                    Project
                    {renderSortIndicator("Project")}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
                  onClick={() => handleSort("Object")}
                >
                  <div className="flex items-center">
                    Object
                    {renderSortIndicator("Object")}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
                  onClick={() => handleSort("GUID")}
                >
                  <div className="flex items-center">
                    GUID
                    {renderSortIndicator("GUID")}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
                  onClick={() => handleSort("Creation Time")}
                >
                  <div className="flex items-center">
                    <Calendar className="mr-1 h-4 w-4 text-gray-400" />
                    Creation Time
                    {renderSortIndicator("Creation Time")}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
                  onClick={() => handleSort("Age")}
                >
                  <div className="flex items-center">
                    <Clock className="mr-1 h-4 w-4 text-gray-400" />
                    Age
                    {renderSortIndicator("Age")}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
                  onClick={() => handleSort("Modification Time")}
                >
                  <div className="flex items-center">
                    <Calendar className="mr-1 h-4 w-4 text-gray-400" />
                    Modification Time
                    {renderSortIndicator("Modification Time")}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
                  onClick={() => handleSort("Object Type")}
                >
                  <div className="flex items-center">
                    Object Type
                    {renderSortIndicator("Object Type")}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
                  onClick={() => handleSort("Object Owner")}
                >
                  <div className="flex items-center">
                    Object Owner
                    {renderSortIndicator("Object Owner")}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
                  onClick={() => handleSort("Last Action Timestamp (UTC)")}
                >
                  <div className="flex items-center">
                    <Calendar className="mr-1 h-4 w-4 text-gray-400" />
                    Last Action
                    {renderSortIndicator("Last Action Timestamp (UTC)")}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
                  onClick={() => handleSort("Last Action Age")}
                >
                  <div className="flex items-center">
                    <Clock className="mr-1 h-4 w-4 text-gray-400" />
                    Last Action Age
                    {renderSortIndicator("Last Action Age")}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
                  onClick={() => handleSort("No of Action")}
                >
                  <div className="flex items-center">
                    No of Actions
                    {renderSortIndicator("No of Action")}
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentData.length > 0 ? (
                currentData.map((row, index) => (
                  <TableRow 
                    key={`${row.GUID}-${index}`}
                    className="hover:bg-slate-50 dark:hover:bg-slate-900"
                  >
                    <TableCell className="font-medium">{row.Project}</TableCell>
                    <TableCell>
                      <div className="max-w-xs truncate" title={row.Object}>
                        {row.Object}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-mono text-xs max-w-xs truncate" title={row.GUID}>
                        {row.GUID}
                      </div>
                    </TableCell>
                    <TableCell>{formatDate(row['Creation Time'])}</TableCell>
                    <TableCell>{calculateAge(row['Creation Time'])}</TableCell>
                    <TableCell>{formatDate(row['Modification Time'])}</TableCell>
                    <TableCell>{row['Object Type']}</TableCell>
                    <TableCell>{row['Object Owner']}</TableCell>
                    <TableCell>{formatDate(row['Last Action Timestamp (UTC)'])}</TableCell>
                    <TableCell>{calculateLastActionAge(row['Last Action Timestamp (UTC)'])}</TableCell>
                    <TableCell className="text-center">{row['No of Action']}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={11} className="text-center py-10">
                    <div className="flex flex-col items-center justify-center space-y-3">
                      <Search className="h-10 w-10 text-gray-400" strokeWidth={1.5} />
                      <p className="text-lg font-medium">No records found</p>
                      <p className="text-sm text-muted-foreground">Try adjusting your search or filter parameters</p>
                      <Button variant="outline" onClick={resetFilters}>Clear Filters</Button>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}
      
      {/* Fixed navigation bar at bottom of viewport */}
      {!isFullscreen && totalPages > 1 && (
        <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-slate-950 border-t shadow-lg z-50 p-4">
          <div className="container mx-auto flex flex-col md:flex-row justify-between items-center gap-3">
            <div className="flex items-center gap-2 w-full md:w-auto">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious 
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))} 
                      className={currentPage <= 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>
                  <PaginationItem>
                    <span className="text-sm font-medium">
                      Page {currentPage} of {totalPages}
                    </span>
                  </PaginationItem>
                  <PaginationItem>
                    <PaginationNext 
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      className={currentPage >= totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
            
            <div className="flex-1 max-w-2xl px-4">
              <Slider
                value={[currentPage]}
                min={1}
                max={totalPages}
                step={1}
                onValueChange={handleSliderChange}
                className="py-2"
              />
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Jump to page:</span>
              <Input
                type="number"
                min={1}
                max={totalPages}
                value={currentPage}
                onChange={(e) => {
                  const page = Number(e.target.value);
                  if (page >= 1 && page <= totalPages) {
                    setCurrentPage(page);
                  }
                }}
                className="w-16 h-9"
              />
              <Button
                size="sm"
                className="h-9"
                onClick={() => {
                  const input = document.querySelector('input[type="number"]') as HTMLInputElement;
                  const page = Number(input.value);
                  if (page >= 1 && page <= totalPages) {
                    setCurrentPage(page);
                  }
                }}
              >
                Go
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}