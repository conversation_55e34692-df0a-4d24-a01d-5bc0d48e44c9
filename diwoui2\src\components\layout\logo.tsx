import { useTheme } from "@/lib/theme-provider";
import { useBrandTheme } from "@/themes/brand-theme-provider";

// Default fallback logos that could be configured globally
const DEFAULT_LOGOS = {
  light: "/logo.png",
  dark: "/logo-dark.png"
};

export function Logo({ className }: { className?: string }) {
  const { theme } = useTheme();
  const { currentTheme } = useBrandTheme();
  
  // Determine if it's light or dark mode
  const mode = theme === "dark" ? "dark" : "light";
  
  // Use theme-specific logo if available, otherwise fallback to default logo
  const logoSrc = currentTheme?.branding?.logo 
    ? currentTheme.branding.logo[mode]
    : DEFAULT_LOGOS[mode];

  console.log("Logo src:", logoSrc, currentTheme);

  return (
    <img 
      key={logoSrc}
      src={logoSrc} 
      alt="Catalyst AI" 
      className={className}
      onError={(e) => {
        // Fallback if the logo doesn't exist
        e.currentTarget.src = DEFAULT_LOGOS[mode];
      }}
    />
  );
} 