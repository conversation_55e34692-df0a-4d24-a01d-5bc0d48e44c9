from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
import logging
import traceback
import re
from mstrio.project_objects import Report
from mstrio.project_objects.dashboard import Dashboard
from mstrio.project_objects.datasets.super_cube import SuperCube
from mstrio.project_objects.datasets.olap_cube import OlapCube
from routers.utils import get_connection

logger = logging.getLogger("bi_accelerator")
router = APIRouter(prefix="/api", tags=["utilities"])


@router.get("/health")
async def health_check():
    """Health check endpoint to verify API is running"""
    logger.info("Health check endpoint called")
    return {"status": "healthy"} 