from fastapi import APIRouter, HTTPException
import os
import json
from dotenv import load_dotenv
from pathlib import Path

# Load environment variables
load_dotenv()

router = APIRouter(
    prefix="/api/auth",
    tags=["auth"],
    responses={404: {"description": "Not found"}},
)

def load_credentials():
    """
    Load credentials from the JSON file.
    """
    try:
        # Get the base directory of the project
        base_dir = Path(__file__).resolve().parent.parent
        credentials_file = base_dir / "auth_credentials.json"
        
        # Read credentials from the JSON file
        with open(credentials_file, "r") as f:
            credentials = json.load(f)
        return credentials
    except Exception as e:
        print(f"Error loading credentials: {e}")
        # Fall back to .env values if JSON loading fails
        return {
            "username": os.getenv("LOGIN_USERNAME", "admin"),
            "password": os.getenv("LOGIN_PASSWORD", "password")
        }

@router.get("/credentials")
async def get_auth_credentials():
    """
    Return default login credentials from the JSON file.
    This is for demo purposes only and should be replaced with proper auth in production.
    """
    # In production, you would NEVER expose credentials like this
    # This is only for the demo/development environment
    return load_credentials()

@router.post("/login")
async def login(username: str, password: str):
    """
    Verify login credentials.
    """
    credentials = load_credentials()
    
    # Trim whitespace from inputs and stored credentials for comparison
    if (username.strip() == credentials["username"].strip() and 
        password.strip() == credentials["password"].strip()):
        return {"authenticated": True}
    else:
        # For debugging - log what was received vs expected
        print(f"Login failed: Received '{username}' (expected '{credentials['username']}') and password length: {len(password)}")
        raise HTTPException(status_code=401, detail="Invalid credentials") 