import React, { createContext, useContext, useEffect, useState } from 'react';
import { Theme } from './types';
import { loadAllThemes, loadTheme, DEFAULT_THEMES } from './theme-loader';
import { useTheme as useColorModeTheme } from '@/lib/theme-provider';

type ColorMode = 'light' | 'dark' | 'system';

interface BrandThemeContextType {
  currentTheme: Theme | null;
  allThemes: Theme[];
  isLoading: boolean;
  setCurrentBrandTheme: (themeId: string) => Promise<void>;
  colorMode: ColorMode;
  refreshTheme: () => Promise<void>;
}

const BrandThemeContext = createContext<BrandThemeContextType>({
  currentTheme: null,
  allThemes: [],
  isLoading: true,
  setCurrentBrandTheme: async () => {},
  colorMode: 'system',
  refreshTheme: async () => {},
});

const LOCAL_STORAGE_KEY = 'catalyst-brand-theme';

interface BrandThemeProviderProps {
  children: React.ReactNode;
  defaultThemeId?: string;
}

export function BrandThemeProvider({
  children,
  defaultThemeId = 'default',
}: BrandThemeProviderProps) {
  const [currentTheme, setCurrentTheme] = useState<Theme | null>(null);
  const [allThemes, setAllThemes] = useState<Theme[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { theme: colorMode } = useColorModeTheme();

  // Function to apply theme CSS variables
  const applyThemeVariables = (theme: Theme) => {
    if (!theme) return;

    console.log(`[Theme] Applying theme: ${theme.name} with primary color: ${theme.colors.primary[500]}`);

    // Get current color mode
    const root = document.documentElement;
    const mode = colorMode === 'system' 
      ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light')
      : colorMode;

    // IMPORTANT: Apply specific CSS class for the theme to the html element
    // Remove any existing theme classes (make this dynamic)
    DEFAULT_THEMES.forEach(themeId => {
      document.documentElement.classList.remove(`theme-${themeId}`);
    });
    document.documentElement.classList.add(`theme-${theme.id}`);
    
    // Apply primary color directly from hex to ensure consistency
    const primaryColor = theme.colors.primary[500];
    root.style.setProperty('--theme-primary', primaryColor);
    root.style.setProperty('--theme-primary-dark', theme.colors.primary[600]);
    root.style.setProperty('--theme-primary-light', theme.colors.primary[400]);

    // Also convert and apply as HSL for compatibility with existing components
    const primaryHSL = hexToHSL(primaryColor);
    if (primaryHSL) {
      root.style.setProperty('--primary', primaryHSL);
      console.log('[Theme] Setting --primary to:', primaryHSL);
      
      // Also set the sidebar primary to match
      root.style.setProperty('--sidebar-primary', primaryHSL);
    }

    // Apply to specific places that use red hardcoded values
    // Apply for all non-default themes to ensure consistency
    if (theme.id !== 'default') {
      // Override hardcoded red colors with theme's primary colors
      root.style.setProperty('--override-red-50', theme.colors.primary[50]);
      root.style.setProperty('--override-red-500', theme.colors.primary[500]);
      root.style.setProperty('--override-red-600', theme.colors.primary[600]);
      root.style.setProperty('--override-red-700', theme.colors.primary[700]);
    }

    // Also set destructive to match primary
    root.style.setProperty('--destructive', root.style.getPropertyValue('--primary'));

    // Set individual primary shade colors for components that might use them
    Object.entries(theme.colors.primary).forEach(([shade, color]) => {
      root.style.setProperty(`--primary-${shade}`, color);
      
      // Also set direct hex values for components that don't use HSL
      root.style.setProperty(`--color-primary-${shade}`, color);
    });

    // Apply background color
    const bgColor = theme.colors.background[mode];
    const bgHSL = hexToHSL(bgColor);
    if (bgHSL) {
      root.style.setProperty('--background', bgHSL);
      root.style.setProperty('--color-background', bgColor);
      console.log(`[Theme] Setting --background to: ${bgHSL} (${mode} mode)`);
    }

    // Apply card color
    const cardColor = theme.colors.card[mode];
    const cardHSL = hexToHSL(cardColor);
    if (cardHSL) {
      root.style.setProperty('--card', cardHSL);
      root.style.setProperty('--color-card', cardColor);
      console.log(`[Theme] Setting --card to: ${cardHSL} (${mode} mode)`);
      
      // Set card foreground color
      const textColor = theme.colors.text[mode];
      const textHSL = hexToHSL(textColor);
      if (textHSL) {
        root.style.setProperty('--card-foreground', textHSL);
        root.style.setProperty('--color-card-foreground', textColor);
      }
    }

    // Apply text color
    const textColor = theme.colors.text[mode];
    const textHSL = hexToHSL(textColor);
    if (textHSL) {
      root.style.setProperty('--foreground', textHSL);
      root.style.setProperty('--color-foreground', textColor);
      console.log(`[Theme] Setting --foreground to: ${textHSL} (${mode} mode)`);
    }

    // Apply border color
    const borderColor = theme.colors.border[mode];
    const borderHSL = hexToHSL(borderColor);
    if (borderHSL) {
      root.style.setProperty('--border', borderHSL);
      root.style.setProperty('--color-border', borderColor);
      root.style.setProperty('--input', borderHSL);
    }

    // Apply fonts
    root.style.setProperty('--font-body', theme.fonts.body);
    root.style.setProperty('--font-heading', theme.fonts.heading);
    root.style.setProperty('--font-mono', theme.fonts.mono);
    
    // Apply actual font-family
    document.body.style.fontFamily = theme.fonts.body;
    
    // Apply heading font to headings
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach(heading => {
      (heading as HTMLElement).style.fontFamily = theme.fonts.heading;
    });

    // Apply border radii
    Object.entries(theme.radii).forEach(([key, value]) => {
      root.style.setProperty(`--radius-${key}`, value);
    });

    // Update favicon if provided
    const faviconLink = document.querySelector('link[rel="icon"]');
    if (faviconLink && theme.branding.favicon) {
      faviconLink.setAttribute('href', theme.branding.favicon);
    }

    // Force a more robust repaint to ensure styles are applied
    const currentScroll = window.scrollY;
    document.body.style.display = 'none';
    document.body.offsetHeight; // Force reflow
    document.body.style.display = '';
    window.scrollTo(0, currentScroll);
    
    // Apply the theme override to body as well for inheritance
    document.body.setAttribute('data-theme', theme.id);
  };

  // Load all available themes
  useEffect(() => {
    async function loadThemes() {
      try {
        console.log('[Theme] Loading all themes...');
        const themes = await loadAllThemes();
        setAllThemes(themes);
        console.log(`[Theme] Loaded ${themes.length} themes`);

        // Get stored theme ID from localStorage, or use default
        const storedThemeId = localStorage.getItem(LOCAL_STORAGE_KEY) || defaultThemeId;
        console.log(`[Theme] Current theme ID from localStorage: ${storedThemeId}`);
        
        const theme = await loadTheme(storedThemeId);
        setCurrentTheme(theme);
        console.log(`[Theme] Current theme loaded: ${theme.name}`);
        
        // Ensure theme is applied immediately
        applyThemeVariables(theme);
      } catch (error) {
        console.error('[Theme] Failed to load themes:', error);
      } finally {
        setIsLoading(false);
      }
    }

    loadThemes();
  }, [defaultThemeId]);

  // Reapply CSS variables when color mode changes
  useEffect(() => {
    if (currentTheme) {
      console.log(`[Theme] Color mode changed to ${colorMode}, reapplying theme...`);
      applyThemeVariables(currentTheme);
    }
  }, [colorMode, currentTheme]);

  // Helper function to convert hex to HSL string
  function hexToHSL(hex: string): string | null {
    if (!hex) return null;
    
    // Remove the # if it exists
    hex = hex.replace('#', '');
    
    // Convert 3-digit hex to 6-digit
    if (hex.length === 3) {
      hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
    }
    
    // Check if valid hex
    if (hex.length !== 6) {
      console.error('[Theme] Invalid hex color:', hex);
      return null;
    }
    
    // Parse the hex values
    const r = parseInt(hex.substring(0, 2), 16) / 255;
    const g = parseInt(hex.substring(2, 4), 16) / 255;
    const b = parseInt(hex.substring(4, 6), 16) / 255;
    
    // Find the min and max values to determine hue
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    
    // Calculate lightness
    const l = (max + min) / 2;
    
    let h = 0;
    let s = 0;
    
    if (max !== min) {
      // Calculate saturation
      s = l > 0.5 ? (max - min) / (2 - max - min) : (max - min) / (max + min);
      
      // Calculate hue
      if (max === r) {
        h = ((g - b) / (max - min)) % 6;
      } else if (max === g) {
        h = (b - r) / (max - min) + 2;
      } else {
        h = (r - g) / (max - min) + 4;
      }
      
      h = Math.round(h * 60);
      if (h < 0) h += 360;
    }
    
    // Convert to HSL percentages
    s = Math.round(s * 100);
    const l_percent = Math.round(l * 100);
    
    return `${h} ${s}% ${l_percent}%`;
  }

  // Function to refresh the current theme (useful for debugging)
  const refreshTheme = async () => {
    if (!currentTheme) return;
    
    try {
      console.log(`[Theme] Manually refreshing theme: ${currentTheme.name}`);
      applyThemeVariables(currentTheme);
    } catch (error) {
      console.error('[Theme] Error refreshing theme:', error);
    }
  };

  // Function to change the current brand theme
  const setCurrentBrandTheme = async (themeId: string) => {
    try {
      setIsLoading(true);
      console.log(`[Theme] Setting theme to: ${themeId}`);
      const theme = await loadTheme(themeId);
      console.log(`[Theme] Theme loaded: ${theme.name}`);
      setCurrentTheme(theme);
      
      // Immediately apply the theme variables
      applyThemeVariables(theme);
      
      localStorage.setItem(LOCAL_STORAGE_KEY, themeId);
      console.log(`[Theme] Theme saved to localStorage with key: ${LOCAL_STORAGE_KEY}`);
    } catch (error) {
      console.error(`[Theme] Failed to set theme ${themeId}:`, error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <BrandThemeContext.Provider
      value={{
        currentTheme,
        allThemes,
        isLoading,
        setCurrentBrandTheme,
        colorMode: colorMode as ColorMode,
        refreshTheme,
      }}
    >
      {children}
    </BrandThemeContext.Provider>
  );
}

export function useBrandTheme() {
  const context = useContext(BrandThemeContext);
  if (context === undefined) {
    throw new Error('useBrandTheme must be used within a BrandThemeProvider');
  }
  return context;
}
