import logging
from fastapi import APIRouter, HTTPException, Path, Query
from pydantic import BaseModel
from typing import List, Optional
from .utils import get_available_environments, get_connection

# Set up logging
logger = logging.getLogger("bi_accelerator")

# Define the router
router = APIRouter(
    prefix="/api/environments",
    tags=["environments"],
    responses={404: {"description": "Not found"}},
)

class Environment(BaseModel):
    name: str
    is_default: bool
    is_active: bool

@router.get("")
async def list_environments():
    """
    List all available MicroStrategy environments
    """
    try:
        env_data = get_available_environments()
        
        if not env_data["environments"]:
            return {"environments": [], "active_environment": None}
        
        # Format response
        environments = []
        active_env = env_data.get("active_environment")
        
        for env_name in env_data["environments"]:
            environments.append({
                "name": env_name,
                "is_active": env_name == active_env,
                "is_default": env_name == active_env  # Default is usually the active one
            })
            
        return {
            "environments": environments,
            "active_environment": active_env
        }
    except Exception as e:
        logger.error(f"Failed to list environments: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list environments: {str(e)}")

@router.get("/active")
async def get_active_environment():
    """
    Get the currently active MicroStrategy environment
    """
    try:
        env_data = get_available_environments()
        active_env = env_data.get("active_environment")
        
        if not active_env:
            raise HTTPException(status_code=404, detail="No active environment found")
            
        return {"active_environment": active_env}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get active environment: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get active environment: {str(e)}")

@router.post("/test-connection")
async def test_environment_connection(environment_name: Optional[str] = Query(None, description="Name of the environment to test, or current active if not provided")):
    """
    Test connection to a specific MicroStrategy environment or the active one
    """
    try:
        # If no environment provided, get the active one
        if not environment_name:
            env_data = get_available_environments()
            environment_name = env_data.get("active_environment")
            
            if not environment_name:
                raise HTTPException(status_code=404, detail="No active environment found")
        
        # Test the connection
        conn = get_connection(project_id=None, environment_name=environment_name)
        
        # If successful, return success message
        return {
            "status": "success", 
            "message": f"Successfully connected to MicroStrategy environment '{environment_name}'",
            "environment": environment_name
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Connection test to environment '{environment_name}' failed: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Connection test to environment '{environment_name}' failed: {str(e)}"
        ) 