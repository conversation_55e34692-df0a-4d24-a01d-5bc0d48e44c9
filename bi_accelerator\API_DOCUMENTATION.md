# Analytics API Documentation

This document provides comprehensive documentation for the Analytics API, which serves as an interface to interact with MicroStrategy Analytics.

## Base URL

All endpoints are prefixed with `http://localhost:8000/api`.

## Authentication

Authentication is now handled via the credentials management API. You can store your MicroStrategy connection credentials using the provided endpoints instead of environment variables.

## Response Format

All successful responses return JSON data. Error responses include HTTP status codes and error details.

## Endpoints

### Credentials Management

#### POST `/credentials`

Save MicroStrategy connection credentials for a specific environment.

**Request Body**:
```json
{
  "name": "production",
  "is_default": true,
  "credentials": {
    "url": "https://your-mstr-server.com",
    "username": "your-username",
    "password": "your-password",
    "description": "Production Environment"
  }
}
```

**Response**:
```json
{
  "status": "success",
  "message": "Credentials for environment 'production' saved successfully"
}
```

#### GET `/credentials`

Retrieve all stored MicroStrategy environments and their connection credentials (with passwords masked).

**Parameters**: None

**Response**:
```json
{
  "environments": [
    {
      "name": "production",
      "is_default": true,
      "credentials": {
        "url": "https://prod-mstr-server.com",
        "username": "prod-user",
        "password": "********",
        "description": "Production Environment"
      }
    },
    {
      "name": "development",
      "is_default": false,
      "credentials": {
        "url": "https://dev-mstr-server.com",
        "username": "dev-user",
        "password": "********",
        "description": "Development Environment"
      }
    }
  ],
  "active_environment": "production"
}
```

#### GET `/credentials?environment_name=development`

Retrieve a specific environment's credentials.

**Parameters**:
- `environment_name` (query): Name of the environment to retrieve

**Response**:
```json
{
  "name": "development",
  "is_default": false,
  "credentials": {
    "url": "https://dev-mstr-server.com",
    "username": "dev-user",
    "password": "********",
    "description": "Development Environment"
  }
}
```

#### DELETE `/credentials/{environment_name}`

Delete stored MicroStrategy connection credentials for a specific environment.

**Parameters**:
- `environment_name` (path): Name of the environment to delete

**Response**:
```json
{
  "status": "success",
  "message": "Environment 'development' deleted successfully"
}
```

#### DELETE `/credentials`

Delete all stored MicroStrategy connection credentials.

**Parameters**: None

**Response**:
```json
{
  "status": "success",
  "message": "All credentials deleted successfully"
}
```

#### PUT `/credentials/active/{environment_name}`

Set the active MicroStrategy environment.

**Parameters**:
- `environment_name` (path): Name of the environment to set as active

**Response**:
```json
{
  "status": "success",
  "message": "Active environment set to 'production'"
}
```

### Environment Management

#### GET `/environments`

List all available MicroStrategy environments.

**Parameters**: None

**Response**:
```json
{
  "environments": [
    {
      "name": "production",
      "is_active": true,
      "is_default": true
    },
    {
      "name": "development",
      "is_active": false,
      "is_default": false
    }
  ],
  "active_environment": "production"
}
```

#### GET `/environments/active`

Get the currently active MicroStrategy environment.

**Parameters**: None

**Response**:
```json
{
  "active_environment": "production"
}
```

#### POST `/environments/test-connection`

Test connection to a specific MicroStrategy environment or the active one.

**Parameters**:
- `environment_name` (query, optional): Name of the environment to test, or current active if not provided

**Response**:
```json
{
  "status": "success",
  "message": "Successfully connected to MicroStrategy environment 'production'",
  "environment": "production"
}
```

### Health Check

#### GET `/api/health`

Check if the API is functioning properly.

**Parameters**: None

**Response**:
```json
{
  "status": "ok",
  "message": "API is running"
}
```

### Projects

#### GET `/api/projects`

Retrieve a list of all available projects.

**Parameters**: 
- `environment_name` (query, optional): Name of the environment to use, or current active if not provided

**Response**:
```json
[
  {
    "id": "project_id",
    "name": "Project Name"
  }
]
```

### Reports

#### GET `/api/reports`

Retrieve a list of all reports in a project.

**Parameters**:
- `project_id` (query): The ID of the project to retrieve reports from

**Response**:
```json
[
  {
    "id": "report_id",
    "name": "Report Name",
    "ext_type": "ReportType"
  }
]
```

#### GET `/api/report/{report_id}`

Retrieve detailed information about a specific report.

**Parameters**:
- `report_id` (path): The ID of the report to retrieve
- `project_id` (query): The ID of the project containing the report

**Response**:
```json
{
  "id": "{report_id}",
  "name": "Report Name",
  "description": "Report description or null",
  "type": "Report type (e.g., ObjectTypes.REPORT_DEFINITION)",
  "subtype": 123,
  "ext_type": "Extended type (e.g., ExtendedType.RELATIONAL)",
  "abbreviation": null,
  "instance_id": null,
  "date_created": "YYYY-MM-DD HH:MM:SS+00:00",
  "date_modified": "YYYY-MM-DD HH:MM:SS+00:00",
  "version": "Version ID string",
  "owner": "User object information",
  "view_media": 123456789,
  "ancestors": [
    {
      "name": "Parent Folder Name", 
      "id": "parent_folder_id", 
      "level": 6
    }
  ],
  "certified_info": "Certification status information",
  "attributes": [
    {
      "id": "attribute_id",
      "name": "Attribute Name",
      "has_pass_through_function": true
    }
  ],
  "metrics": [
    {
      "id": "metric_id",
      "name": "Metric Name",
      "has_pass_through_function": false
    }
  ],
  "acg": 255,
  "acl": [
    "Access control entry information"
  ],
  "Report SQL View:": "SQL query or error message if SQL is not available",
  "Report Data (first few rows):": "JSON array of dictionaries representing the first few rows of the report data"
}
```

**Response Details**:
- The `attributes` array now includes a `has_pass_through_function` boolean indicating if the attribute uses pass-through functions
- The `metrics` array now includes a `has_pass_through_function` boolean indicating if the metric uses pass-through functions
- The response includes SQL view and data preview information
- All values are JSON serializable, with non-serializable types converted to strings
- Dates and timestamps are formatted consistently
- Error handling is implemented for all data retrieval attempts

#### GET `/api/report-attribute/{attribute_id}`

Retrieve detailed information about a specific attribute in a report.

**Parameters**:
- `attribute_id` (path): The ID of the attribute to retrieve
- `project_id` (query): The ID of the project containing the attribute

**Response**:
```json
{
  "id": "attribute_id",
  "name": "Attribute Name",
  "tables": [
    {
      "id": "table_id",
      "name": "Table Name"
    }
  ],
  "relationships": [
    {
      "table": {
        "id": "table_id",
        "name": "Table Name"
      },
      "related_attribute": {
        "id": "related_attribute_id",
        "name": "Related Attribute Name"
      },
      "relationship_type": "related attribute in table"
    }
  ],
  "dependencies": [
    {
      "id": "dependency_id",
      "name": "Dependency Name"
    }
  ]
}
```

**Response Details**:
- The response includes information about tables the attribute is associated with
- Relationship information shows how the attribute is connected to other tables and attributes
- Dependencies list shows other objects that this attribute depends on
- All IDs and names are preserved in their original format
- Error handling is implemented for all data retrieval attempts

### Dashboards

#### GET `/api/dashboards`

Retrieve a list of all dashboards in a project.

**Parameters**:
- `project_id` (query): The ID of the project to retrieve dashboards from

**Response**:
```json
[
  {
    "id": "dashboard_id",
    "name": "Dashboard Name",
  }
]
```

#### GET `/api/dashboard/{dashboard_id}`

Retrieve detailed information about a specific dashboard.

**Parameters**:
- `dashboard_id` (path): The ID of the dashboard to retrieve
- `project_id` (query): The ID of the project containing the dashboard

**Response**:
```json
{
  "id": "dashboard_id",
  "name": "Dashboard Name",
  "properties": {
    "id": "dashboard_id",
    "name": "Dashboard Name",
    "description": "Dashboard description",
    "type": "ObjectTypes.DOCUMENT_DEFINITION",
    "subtype": 14081,
    "ext_type": "ExtendedType.RESERVED",
    "ancestors": [
      {
        "name": "Ancestor Name",
        "id": "ancestor_id",
        "level": 6
      }
    ],
    "certified_info": "Object is not certified.",
    "comments": null,
    "date_created": "2012-05-16 15:48:13+00:00",
    "date_modified": "2012-06-06 15:16:32+00:00",
    "instance_id": "instance_id",
    "owner": "User object named: 'Username' with ID: 'user_id'",
    "recipients": [],
    "version": "version_id",
    "template_info": {
      "template": false,
      "last_modified_by": {}
    },
    "acg": 255,
    "acl": [
      "ACE(deny=False, entry_type=1, rights=255, trustee_id='trustee_id', trustee_name='trustee_name', trustee_type=34, trustee_subtype=8704, inheritable=False)"
    ],
    "chapters": [
      "DashboardChapter(key='chapter_key', pages=[ChapterPage(key='page_key', visualizations=[PageVisualization(key='viz_key', visualization_type='grid', name='Visualization Name', selector=None)], name='Page Name', selectors=None)], name='Chapter Name', filters=[...])"
    ],
    "current_chapter": "chapter_key"
  },
  "chapters": [
    {
      "key": "chapter_key",
      "name": "Chapter Name",
      "pages": [
        {
          "key": "page_key",
          "name": "Page Name",
          "visualizations": [
            {
              "key": "viz_key",
              "name": "Visualization Name",
              "type": "visualization_type"
            }
          ]
        }
      ]
    }
  ],
  "connected_cubes": [
    {
      "id": "cube_id",
      "name": "Cube Name",
      "type": "CubeType"
    }
  ]
}
```

#### GET `/api/dashboard/{dashboard_id}/chapters/{chapter_key}/visualizations/{visualization_key}/data`

Retrieve raw data for a specific visualization within a chapter of a dashboard.

**Parameters**:
- `dashboard_id` (path): The ID of the dashboard (not the project ID)
- `chapter_key` (path): The key of the chapter/page containing the visualization
- `visualization_key` (path): The key of the visualization to retrieve data for
- `project_id` (query): The ID of the project containing the dashboard
- `offset` (query, optional): The starting position for data retrieval (default: 0)
- `limit` (query, optional): Maximum number of rows to retrieve (default: 12000)

**Response**:
```json
{
  "key": "visualization_key",
  "name": "Visualization Name",
  "isGrid": true,
  "visualizationType": "grid",
  "definition": {
    "grid": {
      "crossTab": false,
      "metricsPosition": {
        "axis": "columns",
        "index": 0
      },
      "rows": [
        {
          "name": "Attribute1",
          "id": "attribute1_id",
          "type": "attribute",
          "forms": [
            {
              "id": "form_id",
              "name": "ID",
              "dataType": "varChar",
              "baseFormCategory": "ID",
              "baseFormType": "text"
            }
          ],
          "elements": [
            {
              "formValues": ["Value1"],
              "id": "element_id1"
            }
          ]
        },
        {
          "name": "Attribute2",
          "id": "attribute2_id",
          "type": "attribute",
          "forms": [
            {
              "id": "form_id1",
              "name": "Form1",
              "dataType": "varChar",
              "baseFormCategory": "DESC",
              "baseFormType": "text"
            },
            {
              "id": "form_id2",
              "name": "Form2",
              "dataType": "varChar",
              "baseFormCategory": "ID",
              "baseFormType": "text"
            }
          ],
          "elements": [
            {
              "formValues": ["ValueA", "ValueB"],
              "id": "element_id2"
            },
            {
              "formValues": ["ValueC", "ValueD"],
              "id": "element_id3"
            }
          ]
        }
      ],
      "columns": [
        {
          "name": "Metrics",
          "id": "metrics_id",
          "type": "templateMetrics",
          "elements": [
            {
              "name": "Metric1",
              "id": "metric1_id",
              "type": "metric",
              "min": 10,
              "max": 50,
              "dataType": "double",
              "numberFormatting": {
                "category": 0,
                "decimalPlaces": 0,
                "formatString": "#,##0;(#,##0)"
              }
            },
            {
              "name": "Metric2",
              "id": "metric2_id",
              "type": "metric",
              "derived": true,
              "min": 1,
              "max": 5,
              "dataType": "double",
              "numberFormatting": {
                "category": 0,
                "decimalPlaces": 0,
                "formatString": "#,##0;(#,##0)"
              }
            }
          ]
        }
      ],
      "sorting": {
        "rows": [],
        "columns": [],
        "pageBy": []
      },
      "thresholds": []
    }
  },
  "data": {
    "paging": {
      "total": 100,
      "current": 50,
      "offset": 0,
      "limit": 12000
    },
    "headers": {
      "rows": [
        [0, 0],
        [0, 1],
        [1, 0],
        [1, 1]
      ],
      "columns": [
        [0, 1]
      ]
    },
    "metricValues": {
      "raw": [
        [25, 3],
        [30, 2],
        [15, 4],
        [40, 1]
      ],
      "formatted": [
        ["25", "3"],
        ["30", "2"],
        ["15", "4"],
        ["40", "1"]
      ],
      "extras": [
        [{}, {}],
        [{}, {}],
        [{}, {}],
        [{}, {}]
      ]
    }
  }
}
```

**Response Structure**:

The visualization data response includes several key sections:

1. **Visualization Metadata**:
   - `key`, `name`: Identifiers and display name for the visualization
   - `visualizationType`: Type of visualization (e.g., "grid", "chart", "heatmap", "bar")
   - `isGrid`: Boolean indicating whether the visualization is in grid/table format

2. **Definition Object**:
   - Contains the structure and schema of the visualization data
   - Includes attributes, metrics, formatting options, and data types
   - `rows`: Attributes shown in the rows of the grid
   - `columns`: Metrics or attributes shown in the columns
   - Each attribute contains `forms` that define different display formats (e.g., ID, description, date format)
   - Each attribute contains `elements` that represent the distinct values for that attribute

3. **Data Object**:
   - `paging`: Information about data pagination, including:
     - `total`: Total number of data rows available
     - `current`: Number of rows in the current response
     - `offset`: Starting position of the data
     - `limit`: Maximum number of rows to return
   - `headers`: Contains indexes for mapping row and column positions
   - `metricValues`: The actual data values organized as:
     - `raw`: Raw numerical values in their native format
     - `formatted`: Formatted string values for display purposes
     - `extras`: Additional metadata or formatting information for each value

**Common Errors**:
- `404 Not Found`: If the dashboard, chapter, or visualization doesn't exist. The error message will suggest available dashboard IDs if the project ID was incorrectly used as a dashboard ID.
- `401 Unauthorized`: If authentication fails or the authentication token is invalid.
- `500 Internal Server Error`: If there's an unexpected error processing the request.

### Cubes

#### GET `/api/cubes`

Retrieve a list of all cubes in a project.

**Parameters**:
- `project_id` (query): The ID of the project to retrieve cubes from

**Response**:
```json
[
  {
    "id": "cube_id",
    "name": "Cube Name",
    "type": "CubeType"
  }
]
```

#### GET `/api/cube/{cube_id}`

Retrieve detailed information about a specific cube.

**Parameters**:
- `cube_id` (path): The ID of the cube to retrieve
- `project_id` (query): The ID of the project containing the cube
- `cube_type` (query, optional): The type of cube (SuperCube, OlapCube, etc.)

**Response**:
```json
{
  "id": "cube_id",
  "name": "Cube Name",
  "type": "CubeType",
  "properties": {
    "id": "cube_id",
    "name": "Cube Name",
    "description": "Cube description",
    "type": "ObjectTypes.REPORT_DEFINITION",
    "subtype": "number",
    "ext_type": "ExtendedType value",
    "abbreviation": "string or null",
    "instance_id": "string or null",
    "owner_id": "string or null",
    "path": "Cube path in the project hierarchy",
    "server_mode": "string or null",
    "size": "number",
    "status": "number",
    "date_created": "timestamp string",
    "date_modified": "timestamp string",
    "version": "version string",
    "owner": "Owner information string",
    "view_media": "number",
    "ancestors": [
      {
        "name": "Ancestor Name",
        "id": "ancestor_id",
        "level": "number"
      }
    ],
    "certified_info": "Certification status string",
    "acg": "number",
    "acl": [
      "Access control entry string"
    ]
  },
  "attributes": [
    {
      "id": "attribute_id",
      "name": "Attribute Name"
    }
  ],
  "metrics": [
    {
      "id": "metric_id",
      "name": "Metric Name"
    }
  ]
}
```

#### GET `/api/cube/{cube_id}/sql`

Retrieve the SQL for a specific cube.

**Parameters**:
- `cube_id` (path): The ID of the cube to retrieve SQL for
- `project_id` (query): The ID of the project containing the cube
- `cube_type` (query, optional): The type of cube (SuperCube, OlapCube, etc.)

**Response**:
```json
{
  "cube_id": "cube_id",
  "sql": "SQL query string"
}
```

### Metrics

#### GET `/api/metrics`

Retrieve a list of all metrics in a project.

**Parameters**:
- `project_id` (query): The ID of the project to retrieve metrics from

**Response**:
```json
[
  {
    "id": "metric_id",
    "name": "Metric Name"
  }
]
```

#### GET `/api/metric/{metric_id}`

Retrieve detailed information about a specific metric.

**Parameters**:
- `metric_id` (path): The ID of the metric to retrieve
- `project_id` (query): The ID of the project containing the metric

**Response**:
```json
{
  "id": "metric_id",
  "name": "Metric Name",
  "description": "Metric description",
  "formula": "Metric formula if available",
  "expression": "Metric expression if available",
  "dataType": "Metric data type",
  "dimensionality": "Metric dimensionality information"
}
```

#### GET `/api/metric/{metric_id}/expression`

Retrieve the expression for a specific metric.

**Parameters**:
- `metric_id` (path): The ID of the metric to retrieve the expression for
- `project_id` (query): The ID of the project containing the metric

**Response**:
```json
{
  "id": "metric_id",
  "name": "Metric_Name",
  "expression": {
    "text": "Sum(ApplySimple(\"CASE WHEN #0 = value1 THEN result1 WHEN #0 = value2 THEN result2 ELSE default_value END\", Fact_Reference))",
    "tokens": "[Token(value='Sum', type=Type.FUNCTION, target=SchemaObjectReference(sub_type=ObjectSubType.FUNCTION, object_id='function_id', name='Sum', is_embedded=None), attribute_form=None, level=Level.RESOLVED, state=State.INITIAL), Token(value='(', type=Type.CHARACTER, target=None, attribute_form=None, level=Level.RESOLVED, state=State.INITIAL), ... additional tokens ...]",
    "tree": null
  }
}
```

**Response Details**:
- The `expression` field contains a detailed object with the following properties:
  - `text`: The human-readable expression text showing the formula
  - `tokens`: Tokenized representation of the expression (may be truncated or null)
  - `tree`: The parsed expression tree structure (may be null for some metrics)

#### GET `/api/metric/{metric_id}/to_dax`

Convert a metric's MicroStrategy expression to DAX (Data Analysis Expressions) format used in Power BI.

**Parameters**:
- `metric_id` (path): The ID of the metric to convert
- `project_id` (query): The ID of the project containing the metric

**Response**:
```json
{
  "dax": "Equivalent DAX expression"
}
```

**Notes**:
- Requires an OpenAI API key set as the OPENAI_API_KEY environment variable
- Uses GPT-4o-mini model to perform the conversion
- Returns only the DAX expression without explanations or formatting

### Attributes

#### GET `/api/attributes`

Retrieve a list of all attributes in a project.

**Parameters**:
- `project_id` (query): The ID of the project to retrieve attributes from

**Response**:
```json
[
  {
    "id": "attribute_id",
    "name": "Attribute Name"
  }
]
```

#### GET `/api/attribute/{attribute_id}`

Retrieve detailed information about a specific attribute.

**Parameters**:
- `attribute_id` (path): The ID of the attribute to retrieve
- `project_id` (query): The ID of the project containing the attribute

**Response**:
```json
{
  "id": "attribute_id",
  "name": "Attribute Name",
  "description": "Attribute description",
  "forms": [
    {
      "id": "form_id",
      "name": "Form Name",
      "category": "Form Category",
      "displayFormat": "Display Format"
    }
  ],
  "dataType": "Attribute data type"
}
```

#### GET `/api/attribute/{attribute_id}/expressions`

Retrieve expressions for a specific attribute.

**Parameters**:
- `attribute_id` (path): The ID of the attribute to retrieve expressions for
- `project_id` (query): The ID of the project containing the attribute

**Response**:
```json
{
  "id": "attribute_id",
  "name": "Attribute_Name",
  "forms": [
    {
      "id": "form_id",
      "name": "Form_Name",
      "expressions": [
        {
          "id": "expression_id",
          "expression": {
            "text": "ApplySimple(\"CASE WHEN #0 BETWEEN value1 AND value2 THEN 'Result1' ELSE 'Result2' END\", Attribute_Reference)",
            "tokens": null,
            "tree": "Operator(expression_type=None, dimty_type=None, dependence_type=None, children=[...], function=Function.APPLY_SIMPLE, function_properties=None, function_prompt=None, level_type=None, level=None, node_property=None, custom_function=None)"
          }
        }
      ]
    }
  ]
}
```

**Response Details**:
- The `expression` field contains a detailed object with the following properties:
  - `text`: The human-readable expression text
  - `tokens`: Expression tokens (may be null)
  - `tree`: The parsed expression tree structure that represents the internal syntax tree (truncated in example for brevity)

#### GET `/api/attribute/{attribute_id}/to_dax`

Convert an attribute's MicroStrategy expression to DAX (Data Analysis Expressions) format used in Power BI.

**Parameters**:
- `attribute_id` (path): The ID of the attribute to convert
- `project_id` (query): The ID of the project containing the attribute

**Response**:
```json
{
  "dax": "Equivalent DAX expression"
}
```

**Notes**:
- Requires an OpenAI API key set as the OPENAI_API_KEY environment variable
- Uses GPT-4o-mini model to perform the conversion
- Returns only the DAX expression without explanations or formatting
- If an attribute has multiple expressions, only the first one is converted

### Utilities

#### POST `/api/parse-sql`

Parse a SQL query to extract tables and join information.

**Parameters**:
- Request body:
  ```json
  {
    "query": "SQL query string"
  }
  ```

**Response**:
```json
{
  "tables": ["table1", "table2"],
  "joins": [
    {
      "type": "JOIN_TYPE",
      "tables": ["table1", "table2"],
      "condition": "table1.id = table2.id"
    }
  ],
  "parsed_query": "Formatted SQL query"
}
```

#### GET `/api/object-attributes`

Retrieve attributes associated with a specific object.

**Parameters**:
- `object_type` (query): Type of object (report, dashboard, cube)
- `object_id` (query): ID of the object
- `project_id` (query): ID of the project containing the object

**Response**:
```json
{
  "id": "object_id",
  "name": "Object Name",
  "type": "Object Type",
  "attributes": [
    {
      "id": "attribute_id",
      "name": "Attribute Name"
    }
  ],
  "metrics": [
    {
      "id": "metric_id",
      "name": "Metric Name"
    }
  ]
}
```

#### GET `/api/backend-relationships`

Retrieve backend relationships for a specific object.

**Parameters**:
- `object_type` (query): Type of object (report, dashboard, cube)
- `object_id` (query): ID of the object
- `project_id` (query): ID of the project containing the object

**Response**:
```json
{
  "tables": [
    {
      "name": "Table Name",
      "columns": ["column1", "column2"]
    }
  ],
  "joins": [
    {
      "type": "JOIN_TYPE",
      "tables": ["table1", "table2"],
      "condition": "table1.id = table2.id"
    }
  ],
  "diagram": "Base64 encoded image of relationship diagram if available"
}
```

### SQL Analysis

#### POST `/analyze-cube-sql`

Analyze SQL from a form submission and return HTML formatted analysis results.

**Parameters**:
- Form data:
  - `cube_sql`: The SQL query to analyze

**Response**:
HTML formatted page containing:
- Main tables identified in the SQL
- Relationships between tables
- Model structure
- Power BI implementation recommendations

#### POST `/analyze-sql`

Analyze SQL provided in JSON format and return raw structured data with relationships and model information.
This endpoint focuses only on permanent tables and direct relationships between them, excluding temporary tables from the analysis.

**Parameters**:
- Request body:
  ```json
  {
    "sql": "SQL query string"
  }
  ```

**Response**:
```json
{
  "main_tables": [
    {
      "id": "table_name",
      "name": "Table Name",
      "type": "fact/dimension/lookup/other",
      "fields": [
        {
          "name": "field_name",
          "type": "data_type",
          "description": "field description"
        }
      ]
    }
  ],
  "relationships": [
    {
      "source": "source_table_id",
      "target": "target_table_id",
      "relationship": "one-to-many",
      "sourceField": "source_field",
      "targetField": "target_field",
      "description": "Describes the relationship"
    }
  ],
  "model_structure": {
    "fact_tables": ["table_id1", "table_id2"],
    "dimension_tables": ["table_id3", "table_id4"],
    "hierarchies": [
      {
        "name": "hierarchy_name",
        "levels": ["dimension1", "dimension2", "dimension3"],
        "description": "Describes the hierarchy"
      }
    ],
    "model_type": "star/snowflake/galaxy/other",
    "description": "Overall description of the data model"
  }
}
```

**Model Structure Details**:

The model structure analysis provides detailed information about:

1. **Table Classification**:
   - Fact Tables: Tables containing measurements and metrics
   - Dimension Tables: Tables containing descriptive attributes
   - Lookup Tables: Reference tables
   - Other: Tables that don't fit into standard categories

2. **Hierarchies**:
   - Identifies hierarchical relationships in dimensions
   - Example: Date hierarchy (Year > Quarter > Month > Day)
   - Example: Geography hierarchy (Country > State > City)

3. **Model Type**:
   - Star Schema: Fact table(s) connected to dimension tables
   - Snowflake Schema: Normalized dimension tables
   - Galaxy/Constellation: Multiple fact tables sharing dimensions
   - Other: Custom or hybrid schemas

4. **Field Information**:
   - Data types and descriptions
   - Role in relationships (primary key, foreign key, etc.)
   - Usage in hierarchies

**Error Responses**:

- `400 Bad Request`: Invalid SQL query
  ```json
  {
    "detail": "Invalid SQL query format"
  }
  ```

- `422 Unprocessable Entity`: SQL parsing error
  ```json
  {
    "detail": "Failed to parse SQL query: [error details]"
  }
  ```

- `500 Internal Server Error`: Server-side processing error
  ```json
  {
    "detail": "Failed to analyze SQL: [error message]"
  }
  ```

**Best Practices**:

1. **Query Optimization**:
   - Provide complete SQL queries with joins
   - Include table aliases for clarity
   - Use proper SQL syntax and formatting

2. **Error Handling**:
   - Validate SQL queries before submission
   - Handle both success and error responses
   - Provide meaningful feedback to users

3. **Performance**:
   - Large SQL queries may take longer to analyze
   - Consider implementing loading states in UI
   - Cache results when appropriate

## Error Responses

All API endpoints may return the following error responses:

### 400 Bad Request
```json
{
  "detail": "Error message describing the issue with the request"
}
```

### 401 Unauthorized
```json
{
  "detail": "Authentication to MicroStrategy failed. Status: 401, Response: {error details}"
}
```

### 404 Not Found
```json
{
  "detail": "Dashboard ID not found: {dashboard_id}. This appears to be a project ID, not a dashboard ID. Please try one of these dashboard IDs instead: 
- 170CCC8C4999FBF015FBEAA511C0B44E (Employee Dossier)
- C627CE18431E1C1D1411BABA2DAF4CCF (Datafactz_DossBoss2023)
- 4802DE4C4C18F434C75BFA84EC8A5E4B (New Dashboard)"
}
```

### 500 Internal Server Error
```json
{
  "detail": "Failed to retrieve [resource]: [error message]"
}
```

## Authentication Notes

The API uses the MicroStrategy REST API authentication mechanisms. When making requests, be aware that:

1. Authentication tokens (iSession or X-MSTR-AuthToken) are obtained when credentials are provided
2. These tokens are used for subsequent requests
3. Tokens can expire, and the API automatically handles reauthentication when needed
4. For visualization data retrieval, ensure you're using proper dashboard IDs (not project IDs)

## Using the API for UI Development

When building the UI, you typically will:

1. First call `/credentials` to save your MicroStrategy connection credentials
2. Then call `/api/projects` to get a list of available projects
3. Have the user select a project
4. Then use the project_id to call other endpoints like `/api/reports`, `/api/dashboards`, etc.
5. Allow the user to select specific objects to view details via the corresponding detail endpoints
6. For dashboard visualizations, ensure you use the correct dashboard ID, chapter key, and visualization key

This hierarchical approach allows for intuitive navigation through the MicroStrategy environment.

## Common Issues and Troubleshooting

### Dashboard Visualization Data Retrieval

When retrieving visualization data, common issues include:

1. **Using Project ID as Dashboard ID**: The dashboard_id parameter must be a valid dashboard ID from `/api/dashboards`, not the project ID.
2. **Invalid Authentication**: Ensure credentials are valid and the authentication tokens are properly managed.
3. **Expired Sessions**: If you receive session expiration errors, the API will handle reauthentication automatically.
4. **Invalid Chapter or Visualization Keys**: Ensure you're using the correct keys from `/api/dashboard/{dashboard_id}` response.

### Performance Considerations

- The `limit` parameter in visualization data retrieval can be adjusted to balance between performance and data completeness
- Default limit is 12,000 rows, which should be sufficient for most visualizations
- For large datasets, consider implementing pagination in your UI 

### Power BI Integration

The Power BI integration enables direct creation of Power BI datasets from MicroStrategy reports. The service extracts SQL from MicroStrategy reports, analyzes the structure, creates a corresponding dataset in Power BI, and optionally transfers the data.

#### Create Power BI Dataset

```
POST /api/power-bi/create-dataset
```

Creates a Power BI dataset from a MicroStrategy report with optional data transfer.

**Request Body:**

```json
{
  "report_id": "REPORT_ID",
  "dataset_name": "My Dataset Name",
  "include_data": true,
  "project_id": "PROJECT_ID",
  "workspace_id": "WORKSPACE_ID"
}
```

**Parameters:**

| Name | Type | Required | Description |
|------|------|----------|-------------|
| report_id | string | Yes | MicroStrategy report ID |
| dataset_name | string | Yes | Name for the Power BI dataset |
| include_data | boolean | No | Push data to the dataset (default: true) |
| project_id | string | No | MicroStrategy project ID |
| workspace_id | string | Yes | Power BI workspace ID where the dataset will be created |

**Response:**

```json
{
  "task_id": "TASK_ID",
  "dataset_name": "My Dataset Name",
  "status": "processing",
  "message": "Dataset creation has started. Connect to the SSE endpoint to receive live updates."
}
```

#### Check Dataset Creation Status

```
GET /api/power-bi/dataset-status/{task_id}
```

Retrieves the current status of a dataset creation task.

**Parameters:**

| Name | Type | Required | Description |
|------|------|----------|-------------|
| task_id | string | Yes | Task ID returned from create-dataset |

**Response:**

```json
{
  "task_id": "TASK_ID",
  "dataset_name": "My Dataset Name",
  "dataset_id": "DATASET_ID",
  "status": "completed",
  "updates_count": 20
}
```

#### Stream Dataset Creation Updates

```
GET /api/power-bi/dataset-updates/{task_id}
```

Streams real-time updates on the dataset creation process using Server-Sent Events (SSE). This endpoint provides live progress updates for each step of the process as it happens, similar to watching a terminal log.

**Parameters:**

| Name | Type | Required | Description |
|------|------|----------|-------------|
| task_id | string | Yes | Task ID returned from create-dataset |

**Response Headers:**

The endpoint uses several headers to ensure proper streaming behavior:

```
Content-Type: text/event-stream
Cache-Control: no-cache, no-transform
Connection: keep-alive
X-Accel-Buffering: no
Transfer-Encoding: chunked
```

**Response:**

This endpoint returns a continuous stream of Server-Sent Events (SSE) that are sent in real-time as each step of the process executes:

```
data: {"type":"status","status":"started","message":"Starting dataset creation for 'My Dataset Name'","timestamp":"TIMESTAMP"}

data: {"type":"info","message":"Connecting to MicroStrategy...","timestamp":"TIMESTAMP"}

data: {"type":"success","message":"Connected to MicroStrategy Project successfully!","timestamp":"TIMESTAMP"}

data: {"type":"info","message":"Loading MicroStrategy report ID: REPORT_ID","timestamp":"TIMESTAMP"}

data: {"type":"success","message":"Fetched SQL for report: REPORT_ID","timestamp":"TIMESTAMP"}

data: {"type":"info","message":"Extracting schema from SQL query...","timestamp":"TIMESTAMP"}

data: {"type":"success","message":"Extracted schema from SQL with 3 tables","timestamp":"TIMESTAMP"}

data: {"type":"info","message":"Connecting to SQL Server...","timestamp":"TIMESTAMP"}

data: {"type":"info","message":"Merging schema with SQL Server metadata...","timestamp":"TIMESTAMP"}

data: {"type":"success","message":"Merged schema with SQL Server metadata. Final schema has 3 tables","timestamp":"TIMESTAMP"}

data: {"type":"info","message":"Creating Power BI dataset: 'My Dataset Name'...","timestamp":"TIMESTAMP"}

data: {"type":"success","message":"Created Power BI dataset with ID: DATASET_ID","timestamp":"TIMESTAMP"}

data: {"type":"info","message":"Starting data transfer for 3 tables","timestamp":"TIMESTAMP"}

data: {"type":"info","message":"Processing table 1/3: TableName","timestamp":"TIMESTAMP"}

data: {"type":"info","message":"Fetching data for table: TableName","timestamp":"TIMESTAMP"}

data: {"type":"success","message":"Retrieved 1500 rows from TableName","timestamp":"TIMESTAMP"}

data: {"type":"info","message":"Pushing data to Power BI for table: TableName","timestamp":"TIMESTAMP"}

data: {"type":"success","message":"Pushed 1500 rows to table TableName","timestamp":"TIMESTAMP"}

... (process continues for each table) ...

data: {"type":"status","status":"completed","message":"Data transfer completed for dataset: My Dataset Name","dataset_id":"DATASET_ID","timestamp":"TIMESTAMP"}
```

**Event Types:**

| Type | Description |
|------|-------------|
| status | Overall task status updates (started, completed, error) |
| info | Informational messages about current operations |
| success | Success messages about completed operations |
| warning | Warning messages about potential issues |
| error | Error messages about failed operations |

**Client Usage Example:**

```javascript
// Connect to the SSE endpoint
const eventSource = new EventSource(`/api/power-bi/dataset-updates/${taskId}`);

// Handle incoming events
eventSource.onmessage = (event) => {
  const update = JSON.parse(event.data);
  
  // Display the update in your UI in real-time
  console.log(`[${update.type}] ${update.message}`);
  
  // Update UI elements based on the event type (e.g., progress indicators)
  updateProgressUI(update);
  
  // Close the connection when the process completes or errors
  if (update.type === 'status' && ['completed', 'error'].includes(update.status)) {
    eventSource.close();
  }
};

// Handle connection errors
eventSource.onerror = () => {
  console.error('SSE connection error');
  eventSource.close();
};
```

**Notes:**

- The dataset creation process is asynchronous and runs in the background
- **Real-time updates** are pushed to the client as each step completes - you see operations happening as they execute
- Each update includes a timestamp so you can track the timing of each operation
- Connection timeout is set to 10 minutes but will close automatically once the task completes
- Requires environment variables for SQL Server, Power BI and OpenAI configuration
- If project_id is not provided, uses the credentials stored via the Credentials Management API
- The implementation includes forced flushing to ensure immediate delivery of events
- Keepalive messages are sent every 2 seconds when no new data is available to prevent connection timeouts
- Special headers prevent buffering in proxies and web servers, ensuring true real-time streaming

This integration enables automated synchronization between MicroStrategy reports and Power BI datasets with detailed, live progress updates showing each step of the process as it executes.

### Troubleshooting SSE Streaming Issues

If you experience issues with Server-Sent Events (SSE) not streaming in real-time, consider the following:

#### Server-Side Issues
- **Proxies or Load Balancers**: If your application is behind a proxy (like Nginx, Apache, or AWS ALB), ensure they're configured to not buffer responses. The `X-Accel-Buffering: no` header should help with Nginx, but other proxies might need specific configurations.
- **CORS Configuration**: If accessing from a different domain, ensure CORS headers are properly set up to allow streaming responses.
- **Connection Timeouts**: Default timeout is set to 10 minutes, but some proxies might have shorter timeouts. Adjust proxy configurations accordingly.

#### Client-Side Issues
- **Browser Limitations**: Some older browsers might not fully support SSE or might buffer incoming data. Use modern browsers for best results.
- **Event Handling**: Ensure your client code processes events as they arrive rather than waiting for multiple events.
- **Network Conditions**: Poor network conditions can cause browsers to buffer data until a sufficient amount is received.

#### Example Debugging
To debug SSE issues, you can monitor network traffic using browser developer tools:
1. Open browser developer tools (F12 in most browsers)
2. Go to the Network tab
3. Filter for "event-source" or look for the SSE endpoint
4. Check the "Response" tab to see if events are arriving at the browser
5. Check the timing information to verify if events are being delivered in real-time

If events are visible in the network tab but not appearing in your UI, the issue is likely in your client-side event handling code. 