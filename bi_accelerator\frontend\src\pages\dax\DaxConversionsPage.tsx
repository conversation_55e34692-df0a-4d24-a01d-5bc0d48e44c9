import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Code, Search, Info, ChevronRight, FileText, Grid, List, BarChart4, Database, Check, CheckSquare, Loader2, RefreshCw, Download } from 'lucide-react';
import { apiService, Project } from '@/services/api';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import Card, { CardHeader, CardTitle, CardContent, CardFooter, CardDescription } from '@/components/shared/Card';
import Button from '@/components/shared/Button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader } from '@/components/shared/Loader';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { Progress } from '@/components/ui/progress';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface FilteredItem {
  id: string;
  name: string;
}

interface ConvertedExpression {
  id: string;
  name: string;
  mstr_expression: string;
  dax_expression: string;
  form_name?: string;
}

interface ConvertedAttribute {
  id: string;
  name: string;
  expressions: {
    mstr_expression: string;
    dax_expression: string;
  }[];
}

const DaxConversionsPage = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [selectedAttributes, setSelectedAttributes] = useState<string[]>([]);
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([]);
  const [convertedAttributes, setConvertedAttributes] = useState<ConvertedAttribute[]>([]);
  const [convertedMetrics, setConvertedMetrics] = useState<ConvertedExpression[]>([]);
  const [isConverting, setIsConverting] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [useCachedData, setUseCachedData] = useState(true);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [isDownloading, setIsDownloading] = useState(false);
  
  useEffect(() => {
    const storedProject = localStorage.getItem('selectedProject');
    if (storedProject) {
      setSelectedProject(JSON.parse(storedProject));
    }
  }, [navigate]);
  
  // Query for cached data
  const { 
    data: cachedAttributes, 
    isLoading: isLoadingCachedAttributes,
    refetch: refetchCachedAttributes
  } = useQuery({
    queryKey: ['cached_attributes', selectedProject?.id],
    queryFn: () => selectedProject ? apiService.getCachedAttributes(selectedProject.id) : Promise.resolve([]),
    enabled: !!selectedProject && useCachedData,
    staleTime: Infinity,
    gcTime: Infinity,
  });

  const { 
    data: cachedMetrics, 
    isLoading: isLoadingCachedMetrics,
    refetch: refetchCachedMetrics
  } = useQuery({
    queryKey: ['cached_metrics', selectedProject?.id],
    queryFn: () => selectedProject ? apiService.getCachedMetrics(selectedProject.id) : Promise.resolve([]),
    enabled: !!selectedProject && useCachedData,
    staleTime: Infinity,
    gcTime: Infinity,
  });

  // Queries for live data (only used if cached data is not available or user chooses not to use it)
  const { 
    data: filteredAttributes, 
    isLoading: isLoadingAttributes,
    isFetching: isFetchingAttributes
  } = useQuery({
    queryKey: ['filtered_attributes', selectedProject?.id],
    queryFn: () => selectedProject ? apiService.getFilteredAttributes(selectedProject.id) : Promise.resolve([]),
    enabled: !!selectedProject && !useCachedData,
    staleTime: Infinity,
    gcTime: Infinity,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });

  const { 
    data: filteredMetrics, 
    isLoading: isLoadingMetrics,
    isFetching: isFetchingMetrics
  } = useQuery({
    queryKey: ['filtered_metrics', selectedProject?.id],
    queryFn: () => selectedProject ? apiService.getFilteredMetrics(selectedProject.id) : Promise.resolve([]),
    enabled: !!selectedProject && !useCachedData,
    staleTime: Infinity,
    gcTime: Infinity,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });

  const { data: existingAttributes } = useQuery({
    queryKey: ['converted_attributes', selectedProject?.id],
    queryFn: () => selectedProject ? apiService.getConvertedAttributes(selectedProject.id) : Promise.resolve([]),
    enabled: !!selectedProject,
  });

  const { data: existingMetrics } = useQuery({
    queryKey: ['converted_metrics', selectedProject?.id],
    queryFn: () => selectedProject ? apiService.getConvertedMetrics(selectedProject.id) : Promise.resolve([]),
    enabled: !!selectedProject,
  });

  // Decide which data to use - cached or live
  const attributesData = useCachedData ? cachedAttributes : filteredAttributes;
  const metricsData = useCachedData ? cachedMetrics : filteredMetrics;
  const isLoadingObjectsData = useCachedData 
    ? (isLoadingCachedAttributes || isLoadingCachedMetrics)
    : (isLoadingAttributes || isLoadingMetrics || isFetchingAttributes || isFetchingMetrics);

  // Mutations for background processing
  const processAttributesMutation = useMutation({
    mutationFn: (projectId: string) => apiService.startAttributesBackgroundProcess(projectId),
    onSuccess: () => {
      toast({
        title: "Background Processing Started",
        description: "Attributes with Apply functions are being processed in the background.",
      });
    },
    onError: (error) => {
      console.error('Error starting background process for attributes:', error);
      toast({
        title: "Error",
        description: "Failed to start background processing for attributes.",
        variant: "destructive",
      });
    },
  });

  const processMetricsMutation = useMutation({
    mutationFn: (projectId: string) => apiService.startMetricsBackgroundProcess(projectId),
    onSuccess: () => {
      toast({
        title: "Background Processing Started",
        description: "Metrics with Apply functions are being processed in the background.",
      });
    },
    onError: (error) => {
      console.error('Error starting background process for metrics:', error);
      toast({
        title: "Error",
        description: "Failed to start background processing for metrics.",
        variant: "destructive",
      });
    },
  });

  const convertAttributesMutation = useMutation({
    mutationFn: (attributeIds: string[]) => 
      apiService.batchConvertAttributesToDax(selectedProject!.id, attributeIds),
    onSuccess: (data) => {
      setConvertedAttributes(prev => [...prev, ...data]);
      setIsConverting(false);
    },
    onError: (error) => {
      console.error('Error converting attributes:', error);
      setIsConverting(false);
    },
  });

  const convertMetricsMutation = useMutation({
    mutationFn: (metricIds: string[]) => 
      apiService.batchConvertMetricsToDax(selectedProject!.id, metricIds),
    onSuccess: (data) => {
      setConvertedMetrics(prev => [...prev, ...data]);
      setIsConverting(false);
    },
    onError: (error) => {
      console.error('Error converting metrics:', error);
      setIsConverting(false);
    },
  });
  
  useEffect(() => {
    if (existingAttributes) {
      setConvertedAttributes(existingAttributes);
    }
    if (existingMetrics) {
      setConvertedMetrics(existingMetrics);
    }
  }, [existingAttributes, existingMetrics]);
  
  const filteredAttributesList = attributesData?.filter(attr => 
    attr.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const filteredMetricsList = metricsData?.filter(metric => 
    metric.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const handleAttributeToggle = (id: string) => {
    setSelectedAttributes(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const handleMetricToggle = (id: string) => {
    setSelectedMetrics(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const handleSelectAllAttributes = (checked: boolean) => {
    if (checked) {
      setSelectedAttributes(filteredAttributesList.map(attr => attr.id));
    } else {
      setSelectedAttributes([]);
    }
  };

  const handleSelectAllMetrics = (checked: boolean) => {
    if (checked) {
      setSelectedMetrics(filteredMetricsList.map(metric => metric.id));
    } else {
      setSelectedMetrics([]);
    }
  };

  const handleConvert = async () => {
    if (!selectedProject) return;
    
    setIsConverting(true);
    
    // Filter out already converted items
    const existingAttributeIds = new Set(convertedAttributes.map(attr => attr.id));
    const existingMetricIds = new Set(convertedMetrics.map(metric => metric.id));
    
    const newAttributeIds = selectedAttributes.filter(id => !existingAttributeIds.has(id));
    const newMetricIds = selectedMetrics.filter(id => !existingMetricIds.has(id));
    
    if (newAttributeIds.length > 0) {
      convertAttributesMutation.mutate(newAttributeIds);
    }
    
    if (newMetricIds.length > 0) {
      convertMetricsMutation.mutate(newMetricIds);
    }
    
    // Clear selections after conversion
    setSelectedAttributes([]);
    setSelectedMetrics([]);
  };

  const handleGetObjects = async () => {
    if (!selectedProject) return;
    
    setIsProcessing(true);
    toast({
      title: "Processing Started",
      description: "Starting background processing of attributes and metrics with Apply functions.",
    });
    
    // Start background processing for both attributes and metrics
    try {
      await Promise.all([
        processAttributesMutation.mutateAsync(selectedProject.id),
        processMetricsMutation.mutateAsync(selectedProject.id)
      ]);
      
      // Start polling for cached data
      startPollingForCachedData();
      
      toast({
        title: "Processing In Progress",
        description: "Background processing has started. Data will load automatically when ready.",
      });
      
    } catch (error) {
      console.error("Error starting background processes:", error);
      setIsProcessing(false);
      toast({
        title: "Error",
        description: "Failed to start background processing.",
        variant: "destructive",
      });
    }
  };

  // Function to start polling for cached data
  const startPollingForCachedData = () => {
    // Clear any existing polling interval
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }
    
    // Track the number of polling attempts
    let attempts = 0;
    const maxAttempts = 20; // Max 1 minute (20 attempts * 3 seconds)
    
    // Reset progress
    setProcessingProgress(0);
    
    // Start a new polling interval
    pollingIntervalRef.current = setInterval(async () => {
      attempts++;
      
      // Update progress indicator (5% per attempt, up to 95%)
      setProcessingProgress(Math.min(95, attempts * 5));
      
      try {
        // Check if data is available by attempting to fetch it
        const attributesResponse = await apiService.getCachedAttributes(selectedProject!.id);
        const metricsResponse = await apiService.getCachedMetrics(selectedProject!.id);
        
        // If we have data in either response, refresh the queries and consider processing done
        if ((attributesResponse && attributesResponse.length > 0) || 
            (metricsResponse && metricsResponse.length > 0)) {
          
          // Set progress to 100%
          setProcessingProgress(100);
          
          // Refresh the queries
          queryClient.invalidateQueries({ queryKey: ['cached_attributes'] });
          queryClient.invalidateQueries({ queryKey: ['cached_metrics'] });
          
          // Stop processing and polling
          setIsProcessing(false);
          stopPolling();
          
          toast({
            title: "Processing Complete",
            description: "Apply function data has been loaded successfully.",
          });
        } else if (attempts >= maxAttempts) {
          // If we've reached max attempts, set progress to 100% anyway to indicate completion
          setProcessingProgress(100);
          
          // Stop processing and polling
          setIsProcessing(false);
          stopPolling();
          
          toast({
            title: "Processing Status",
            description: "Background processing is continuing. You may need to click 'Get Objects' again in a few moments to check results.",
          });
        }
      } catch (error) {
        console.error("Error polling for cached data:", error);
        // If there's an error and we've reached max attempts, stop polling
        if (attempts >= maxAttempts) {
          // Set progress to 100%
          setProcessingProgress(100);
          
          setIsProcessing(false);
          stopPolling();
          
          toast({
            title: "Processing Status",
            description: "Unable to check processing status. You may need to click 'Get Objects' again in a few moments.",
          });
        }
      }
    }, 3000); // Check every 3 seconds
  };
  
  // Function to stop polling
  const stopPolling = () => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
  };
  
  // Clean up the polling interval when the component unmounts
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, []);

  // Add a function to handle downloading DAX expressions
  const handleDownloadDaxExpressions = async () => {
    if (!selectedProject) return;
    
    setIsDownloading(true);
    try {
      await apiService.downloadDaxExpressions(selectedProject.id);
      toast({
        title: "Download Successful",
        description: "DAX expressions have been downloaded as an Excel file.",
      });
    } catch (error) {
      console.error('Error downloading DAX expressions:', error);
      toast({
        title: "Download Failed",
        description: "Failed to download DAX expressions.",
        variant: "destructive",
      });
    } finally {
      setIsDownloading(false);
    }
  };

  if (!selectedProject) {
    return (
      <div className="container max-w-6xl mx-auto animate-fade-in">
        <div className="text-center py-16">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-muted mb-6">
            <Code className="h-10 w-10 text-muted-foreground" />
          </div>
          <h2 className="text-2xl font-semibold mb-3">No Project Selected</h2>
          <p className="text-muted-foreground text-lg max-w-md mx-auto">
            Please select a project from the Projects page to view DAX conversions for Apply functions.
          </p>
          <Button 
            onClick={() => navigate('/projects')}
            variant="default"
            className="mt-6"
          >
            Go to Projects
          </Button>
        </div>
      </div>
    );
  }

  const isLoading = isLoadingObjectsData;
  const totalSelected = selectedAttributes.length + selectedMetrics.length;
  
  return (
    <div className="container px-4 mx-auto animate-fade-in">
      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/')}>
          Home
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/projects')}>
          Projects
        </Button>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground truncate max-w-xs">
          DAX Conversions
        </span>
      </div>
      
      <div className="flex flex-col mb-8">
        <h1 className="text-2xl font-bold mb-1">DAX Conversions</h1>
        <p className="text-muted-foreground mb-6">
          Convert MicroStrategy expressions with Apply functions to DAX format
        </p>
        
        {isProcessing && (
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Processing objects...</span>
              <span className="text-sm">{processingProgress}%</span>
            </div>
            <Progress value={processingProgress} className="h-2" />
          </div>
        )}

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-2">
          <div className="relative max-w-md w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search for attributes or metrics..."
              className="pl-9"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              disabled={isLoading}
            />
          </div>
          <div className="flex gap-3">
            <Button
              onClick={handleGetObjects}
              className="flex items-center gap-2"
              disabled={isProcessing || !selectedProject}
              variant="outline"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4" />
                  Get Objects
                </>
              )}
            </Button>
            <Button
              onClick={handleConvert}
              className="flex items-center gap-2"
              disabled={isLoading || isConverting || (!selectedAttributes.length && !selectedMetrics.length)}
              size="lg"
            >
              {isConverting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Converting...
                </>
              ) : (
                <>
                  <Code className="h-4 w-4" />
                  {totalSelected > 0 ? `Convert ${totalSelected} Selected` : 'Convert to DAX'}
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      <Tabs defaultValue="selection" className="space-y-4">
        <TabsList>
          <TabsTrigger value="selection">Select Objects</TabsTrigger>
          <TabsTrigger value="conversions">View Conversions</TabsTrigger>
        </TabsList>
        
        <TabsContent value="selection">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Attributes</CardTitle>
                  <CardDescription>
                    Loading attributes with Apply functions...
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[...Array(5)].map((_, i) => (
                      <Skeleton key={i} className="h-8 w-full" />
                    ))}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Metrics</CardTitle>
                  <CardDescription>
                    Loading metrics with Apply functions...
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[...Array(5)].map((_, i) => (
                      <Skeleton key={i} className="h-8 w-full" />
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Database className="h-4 w-4" />
                      Attributes
                      <Badge variant="outline" className="ml-2">
                        {filteredAttributesList.length}
                      </Badge>
                    </CardTitle>
                    <CardDescription>
                      Attributes with Apply functions {useCachedData ? "(From Cache)" : ""}
                    </CardDescription>
                  </div>
                </CardHeader>
                <CardContent>
                  {filteredAttributesList.length > 0 ? (
                    <>
                      <div className="flex items-center space-x-2 mb-4 pb-2 border-b">
                        <Checkbox
                          id="select-all-attributes"
                          checked={selectedAttributes.length === filteredAttributesList.length && filteredAttributesList.length > 0}
                          onCheckedChange={handleSelectAllAttributes}
                        />
                        <label
                          htmlFor="select-all-attributes"
                          className="text-sm font-medium leading-none cursor-pointer select-none"
                        >
                          Select All Attributes
                        </label>
                        {selectedAttributes.length > 0 && (
                          <Badge variant="secondary" className="ml-auto">
                            {selectedAttributes.length} selected
                          </Badge>
                        )}
                      </div>
                      <ScrollArea className="h-[380px] pr-4">
                        <div className="space-y-3">
                          {filteredAttributesList.map((attr) => (
                            <div key={attr.id} className="flex items-center space-x-2 py-1 hover:bg-muted/40 px-1 rounded">
                              <Checkbox
                                id={`attr-${attr.id}`}
                                checked={selectedAttributes.includes(attr.id)}
                                onCheckedChange={() => handleAttributeToggle(attr.id)}
                              />
                              <label
                                htmlFor={`attr-${attr.id}`}
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer select-none flex-1"
                              >
                                {attr.name}
                              </label>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <Database className="h-10 w-10 text-muted-foreground/50 mb-4" />
                      <p className="text-muted-foreground">
                        {useCachedData && !cachedAttributes?.length ? (
                          isProcessing ? (
                            <>
                              Searching for attributes with Apply functions...<br/>
                              <span className="text-sm mt-2 italic">This may take a few moments</span>
                            </>
                          ) : (
                            <>
                              No cached attributes found. <br />
                              Click "Get Objects" to search for attributes with Apply functions.
                            </>
                          )
                        ) : (
                          "No attributes found with Apply functions"
                        )}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart4 className="h-4 w-4" />
                      Metrics
                      <Badge variant="outline" className="ml-2">
                        {filteredMetricsList.length}
                      </Badge>
                    </CardTitle>
                    <CardDescription>
                      Metrics with Apply functions {useCachedData ? "(From Cache)" : ""}
                    </CardDescription>
                  </div>
                </CardHeader>
                <CardContent>
                  {filteredMetricsList.length > 0 ? (
                    <>
                      <div className="flex items-center space-x-2 mb-4 pb-2 border-b">
                        <Checkbox
                          id="select-all-metrics"
                          checked={selectedMetrics.length === filteredMetricsList.length && filteredMetricsList.length > 0}
                          onCheckedChange={handleSelectAllMetrics}
                        />
                        <label
                          htmlFor="select-all-metrics"
                          className="text-sm font-medium leading-none cursor-pointer select-none"
                        >
                          Select All Metrics
                        </label>
                        {selectedMetrics.length > 0 && (
                          <Badge variant="secondary" className="ml-auto">
                            {selectedMetrics.length} selected
                          </Badge>
                        )}
                      </div>
                      <ScrollArea className="h-[380px] pr-4">
                        <div className="space-y-3">
                          {filteredMetricsList.map((metric) => (
                            <div key={metric.id} className="flex items-center space-x-2 py-1 hover:bg-muted/40 px-1 rounded">
                              <Checkbox
                                id={`metric-${metric.id}`}
                                checked={selectedMetrics.includes(metric.id)}
                                onCheckedChange={() => handleMetricToggle(metric.id)}
                              />
                              <label
                                htmlFor={`metric-${metric.id}`}
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer select-none flex-1"
                              >
                                {metric.name}
                              </label>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <BarChart4 className="h-10 w-10 text-muted-foreground/50 mb-4" />
                      <p className="text-muted-foreground">
                        {useCachedData && !cachedMetrics?.length ? (
                          isProcessing ? (
                            <>
                              Searching for metrics with Apply functions...<br/>
                              <span className="text-sm mt-2 italic">This may take a few moments</span>
                            </>
                          ) : (
                            <>
                              No cached metrics found. <br />
                              Click "Get Objects" to search for metrics with Apply functions.
                            </>
                          )
                        ) : (
                          "No metrics found with Apply functions"
                        )}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="conversions">
          <div className="space-y-6">
            <div className="flex justify-end mb-4">
              <Button
                onClick={handleDownloadDaxExpressions}
                className="flex items-center gap-2"
                variant="outline"
                disabled={isDownloading || (convertedAttributes.length === 0 && convertedMetrics.length === 0)}
              >
                {isDownloading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Downloading...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4" />
                    Download as Excel
                  </>
                )}
              </Button>
            </div>
            
            {convertedAttributes.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-4 w-4" />
                    Converted Attributes
                    <Badge variant="outline" className="ml-2">
                      {convertedAttributes.length}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>MicroStrategy Expression</TableHead>
                        <TableHead>DAX Expression</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {convertedAttributes.map((attr) => (
                        attr.expressions.map((expr, index) => (
                          <TableRow key={`${attr.id}-${index}`}>
                            <TableCell className="font-medium">{attr.name}</TableCell>
                            <TableCell className="font-mono text-sm whitespace-pre-wrap break-all max-w-[300px]">{expr.mstr_expression}</TableCell>
                            <TableCell className="font-mono text-sm whitespace-pre-wrap break-all max-w-[300px]">{expr.dax_expression}</TableCell>
                          </TableRow>
                        ))
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            )}
            
            {convertedMetrics.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart4 className="h-4 w-4" />
                    Converted Metrics
                    <Badge variant="outline" className="ml-2">
                      {convertedMetrics.length}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>MicroStrategy Expression</TableHead>
                        <TableHead>DAX Expression</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {convertedMetrics.map((metric) => (
                        <TableRow key={metric.id}>
                          <TableCell className="font-medium">{metric.name}</TableCell>
                          <TableCell className="font-mono text-sm whitespace-pre-wrap break-all max-w-[300px]">{metric.mstr_expression}</TableCell>
                          <TableCell className="font-mono text-sm whitespace-pre-wrap break-all max-w-[300px]">{metric.dax_expression}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            )}
            
            {convertedAttributes.length === 0 && convertedMetrics.length === 0 && (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-8 text-center">
                  <Code className="h-10 w-10 text-muted-foreground/50 mb-4" />
                  <p className="text-muted-foreground">No conversions available yet</p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DaxConversionsPage; 