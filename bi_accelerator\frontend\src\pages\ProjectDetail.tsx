import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { ChevronRight, Info, Database, BarChart3, LayoutDashboard, Search, Code, FileText } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { apiService, Project } from '@/services/api';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import Card, { CardHeader, CardTitle, CardContent, CardFooter } from '@/components/shared/Card';
import Button from '@/components/shared/Button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useViewMode } from '@/lib/ViewModeContext';
import { Loader } from '@/components/shared/Loader';

const ProjectDetail = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  
  useEffect(() => {
    const storedProject = localStorage.getItem('selectedProject');
    if (storedProject) {
      setSelectedProject(JSON.parse(storedProject));
    } else {
      navigate('/projects');
    }
  }, [navigate]);
  
  if (!selectedProject) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <Alert>
          <AlertTitle>No project selected</AlertTitle>
          <AlertDescription>
            Please select a project first.
            <Button
              variant="link"
              className="p-0 ml-2"
              onClick={() => navigate('/projects')}
            >
              Go to Projects
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }
  
  const projectModules = [
    { 
      title: "Cubes", 
      description: "Browse and explore data cubes in this project",
      icon: Database,
      path: "/cubes"
    },
    { 
      title: "Reports", 
      description: "View and analyze reports in this project",
      icon: BarChart3,
      path: "/reports"
    },
    { 
      title: "Dashboards", 
      description: "Access interactive dashboards for this project",
      icon: LayoutDashboard,
      path: "/dashboards"
    },
    { 
      title: "FFSQL Reports", 
      description: "Access and manage FFSQL reports",
      icon: FileText,
      path: "/ffsql-reports"
    },
    { 
      title: "Dax Conversions", 
      description: "Convert and transform DAX expressions",
      icon: Code,
      path: "/dax"
    }
  ];
  
  return (
    <div className="container px-4 mx-auto animate-fade-in">
      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/')}>
          Home
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/projects')}>
          Projects
        </Button>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground truncate max-w-xs">
          {selectedProject.name}
        </span>
      </div>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Project Resources</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {projectModules.map((module) => (
            <Card key={module.title} className="hover:shadow-md transition-shadow group">
              <Link to={module.path}>
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center gap-2">
                    <module.icon className="h-5 w-5 text-brand-600" />
                    <span>{module.title}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">{module.description}</p>
                </CardContent>
                <CardFooter>
                  <Button
                    variant="outline"
                    className="w-full group-hover:bg-brand-50 group-hover:text-brand-600 group-hover:border-brand-200 transition-colors flex justify-center items-center h-10"
                  >
                    View
                  </Button>
                </CardFooter>
              </Link>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProjectDetail;
