import { useLocation, useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Search, Bell, LogOut } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useState, useEffect } from 'react';

interface Project {
  id: string;
  name: string;
}

const TitleBar: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  
  useEffect(() => {
    const storedProject = localStorage.getItem('selectedProject');
    if (storedProject) {
      setSelectedProject(JSON.parse(storedProject));
    }
  }, [location.pathname]);
  
  const handleLogout = () => {
    localStorage.removeItem('isAuthenticated');
    navigate('/login');
  };

  // Check if current page is home or projects page
  const isHomeOrProjects = location.pathname === '/' || location.pathname === '/projects';

  return (
    <>
      <div className={cn(
        "sticky top-0 z-40 bg-background border-b border-border h-16 flex items-center justify-between px-4 md:px-8",
        "transition-all ease-apple shadow-sm"
      )}>
        {isHomeOrProjects ? (
          <div className="h-12">
            {/* <img src="/application_logo.png" alt="DataFact Logo" className="h-full" /> */}
          </div>
        ) : (
          <div className="flex flex-col">
            {selectedProject && (
              <>
                <h1 className={cn(
                  "text-2xl font-semibold transition-all duration-300", 
                  "animate-slide-down"
                )}>
                  {selectedProject.name}
                </h1>
                <div className="text-xs text-muted-foreground">
                  ID: {selectedProject.id}
                </div>
              </>
            )}
          </div>
        )}
        
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon">
            <Search className="h-5 w-5" />
            <span className="sr-only">Search</span>
          </Button>
          <Button variant="ghost" size="icon">
            <Bell className="h-5 w-5" />
            <span className="sr-only">Notifications</span>
          </Button>
          <div className="h-8 w-8 rounded-full bg-brand-400 flex items-center justify-center text-white font-medium">
            U
          </div>
          
          {/* Logout button - only show if authenticated */}
          {localStorage.getItem('isAuthenticated') === 'true' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLogout}
              className="text-muted-foreground hover:text-foreground"
            >
              <LogOut className="h-4 w-4 mr-1" /> Logout
            </Button>
          )}
        </div>
      </div>
    </>
  );
};

export default TitleBar; 