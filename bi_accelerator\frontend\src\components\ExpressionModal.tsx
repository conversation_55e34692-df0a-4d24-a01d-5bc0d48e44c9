import React, { useState, useEffect, useRef } from 'react';
import { Code, ArrowRight, Info, Database, GitBranch } from 'lucide-react';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { apiService, ReportAttributeDetail } from '@/services/api';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader as LoaderSpinner } from '@/components/shared/Loader';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface ExpressionModalProps {
  isOpen: boolean;
  onClose: () => void;
  id: string;
  name: string;
  type: 'attribute' | 'metric';
  projectId: string;
}

// Create a cache to store DAX expressions
const daxCache = new Map<string, string>();

const ExpressionModal: React.FC<ExpressionModalProps> = ({
  isOpen,
  onClose,
  id,
  name,
  type,
  projectId
}) => {
  const { toast } = useToast();
  const [expression, setExpression] = useState<string | null>(null);
  const [daxExpression, setDaxExpression] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isDaxLoading, setIsDaxLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [attributeDetails, setAttributeDetails] = useState<ReportAttributeDetail | null>(null);
  const [isAttributeLoading, setIsAttributeLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('expression');
  
  // Create a unique key for this item in the cache
  const cacheKey = `${type}_${id}_${projectId}`;

  // Determine if closing is allowed
  const canClose = !isDaxLoading && !isAttributeLoading;

  useEffect(() => {
    if (isOpen && id) {
      loadExpressionData();
      if (type === 'attribute') {
        loadAttributeDetails();
      }
    }
  }, [isOpen, id]);

  const loadExpressionData = async () => {
    setIsLoading(true);
    setError(null);
    setExpression(null);
    setDaxExpression(null);

    try {
      // Fetch the expression
      if (type === 'metric') {
        const response = await apiService.getMetricExpression(id, projectId);
        setExpression(response.expression.text);
      } else {
        const response = await apiService.getAttributeExpressions(id, projectId);
        if (response.forms && response.forms.length > 0 && 
            response.forms[0].expressions && response.forms[0].expressions.length > 0) {
          // Check if expression is a string or an object with a text property
          const expressionData = response.forms[0].expressions[0].expression;
          if (typeof expressionData === 'string') {
            setExpression(expressionData);
          } else if (expressionData && expressionData.text) {
            setExpression(expressionData.text);
          } else {
            setError("Invalid expression format for this attribute");
          }
        } else {
          setError("No expression found for this attribute");
        }
      }
      
      // Check if we have a cached DAX conversion for this item
      if (daxCache.has(cacheKey)) {
        setDaxExpression(daxCache.get(cacheKey) || null);
      }
    } catch (error) {
      console.error(`Failed to fetch ${type} expression:`, error);
      setError(`Failed to load expression: ${error instanceof Error ? error.message : "Unknown error"}`);
    } finally {
      setIsLoading(false);
    }
  };

  const loadDaxConversion = async () => {
    if (!expression) return;
    
    setIsDaxLoading(true);
    setError(null);
    
    try {
      // Fetch the DAX conversion
      if (type === 'metric') {
        const response = await apiService.getMetricToDax(id, projectId);
        setDaxExpression(response.dax);
        // Cache the DAX expression
        daxCache.set(cacheKey, response.dax);
      } else {
        const response = await apiService.getAttributeToDax(id, projectId);
        setDaxExpression(response.dax);
        // Cache the DAX expression
        daxCache.set(cacheKey, response.dax);
      }
    } catch (error) {
      console.error(`Failed to convert ${type} to DAX:`, error);
      setError(`Failed to convert to DAX: ${error instanceof Error ? error.message : "Unknown error"}`);
    } finally {
      setIsDaxLoading(false);
    }
  };

  const loadAttributeDetails = async () => {
    if (type !== 'attribute') return;
    
    setIsAttributeLoading(true);
    setError(null);
    
    try {
      const details = await apiService.getReportAttribute(id, projectId);
      setAttributeDetails(details);
    } catch (error) {
      console.error('Failed to fetch attribute details:', error);
      setError(`Failed to load attribute details: ${error instanceof Error ? error.message : "Unknown error"}`);
    } finally {
      setIsAttributeLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied to clipboard",
      description: "The expression has been copied to your clipboard",
    });
  };

  // Handle the Convert button click
  const handleConvert = () => {
    if (!daxExpression) {
      loadDaxConversion();
    }
  };

  // Handle dialog closing attempts
  const handleOpenChange = (open: boolean) => {
    if (open === false && !canClose) {
      // Prevent closing if we're still loading
      return;
    }
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="w-full sm:max-w-lg md:max-w-2xl lg:max-w-5xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Code className="h-5 w-5" />
            <span>{type === 'attribute' ? 'Attribute' : 'Metric'} Details</span>
          </DialogTitle>
        </DialogHeader>
        
        <Tabs defaultValue="expression" value={activeTab} onValueChange={setActiveTab} className="mt-4">
          <TabsList className={`grid w-full md:w-auto mb-4 ${type === 'attribute' ? 'grid-cols-2' : 'grid-cols-1'}`}>
            <TabsTrigger value="expression" className="flex items-center gap-2">
              <Code className="h-4 w-4" />
              <span>Expression</span>
            </TabsTrigger>
            {type === 'attribute' && (
              <TabsTrigger value="info" className="flex items-center gap-2">
                <Info className="h-4 w-4" />
                <span>Info</span>
              </TabsTrigger>
            )}
          </TabsList>
          
          <TabsContent value="expression">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h3 className="font-medium text-lg">{type === 'attribute' ? 'Attribute' : 'Metric'} Expression</h3>
                {isLoading ? (
                  <div className="flex justify-center items-center py-12">
                    <LoaderSpinner className="h-35 w-35 text-brand-600" />
                  </div>
                ) : error ? (
                  <div className="bg-red-50 p-4 rounded-md text-red-800">
                    {error}
                  </div>
                ) : expression ? (
                  <ScrollArea className="h-[300px] w-full rounded-md border p-4 bg-slate-50 font-mono text-sm">
                    {expression}
                  </ScrollArea>
                ) : (
                  <div className="text-center py-4 text-muted-foreground rounded-md border bg-slate-50 h-[300px] flex items-center justify-center">
                    No expression available
                  </div>
                )}
              </div>
              
              <div className="space-y-2">
                <h3 className="font-medium text-lg">Power BI DAX</h3>
                {isDaxLoading ? (
                  <div className="flex justify-center items-center py-12">
                    <LoaderSpinner className="h-35 w-35 text-brand-600" />
                  </div>
                ) : error && daxExpression === null ? (
                  <div className="bg-red-50 p-4 rounded-md text-red-800">
                    {error}
                  </div>
                ) : daxExpression ? (
                  <ScrollArea className="h-[300px] w-full rounded-md border p-4 bg-slate-50 font-mono text-sm">
                    {daxExpression}
                  </ScrollArea>
                ) : (
                  <div className="text-center py-4 text-muted-foreground rounded-md border bg-slate-50 h-[300px] flex items-center justify-center">
                    Click "Convert" to see DAX conversion
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
          
          {type === 'attribute' && (
            <TabsContent value="info">
              {isAttributeLoading ? (
                <div className="flex justify-center items-center py-12">
                  <LoaderSpinner className="h-35 w-35 text-brand-600" />
                </div>
              ) : error ? (
                <div className="bg-red-50 p-4 rounded-md text-red-800">
                  {error}
                </div>
              ) : attributeDetails ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="border rounded-md">
                      <div className="p-2 border-b bg-muted/30">
                        <h3 className="text-sm font-medium flex items-center gap-2">
                          <Database className="h-4 w-4" />
                          Tables
                        </h3>
                      </div>
                      <div className="p-4">
                        {attributeDetails.tables.length > 0 ? (
                          <ul className="space-y-2">
                            {attributeDetails.tables.map((table) => (
                              <li key={table.id} className="flex items-center gap-2">
                                <span className="text-sm font-medium">{table.name}</span>
                                <span className="text-xs text-muted-foreground">({table.id})</span>
                              </li>
                            ))}
                          </ul>
                        ) : (
                          <p className="text-sm text-muted-foreground">No tables found</p>
                        )}
                      </div>
                    </div>
                    
                    <div className="border rounded-md">
                      <div className="p-2 border-b bg-muted/30">
                        <h3 className="text-sm font-medium flex items-center gap-2">
                          <GitBranch className="h-4 w-4" />
                          Dependencies
                        </h3>
                      </div>
                      <div className="p-4">
                        {attributeDetails.dependencies.length > 0 ? (
                          <ul className="space-y-2">
                            {attributeDetails.dependencies.map((dep) => (
                              <li key={dep.id} className="flex items-center gap-2">
                                <span className="text-sm font-medium">{dep.name}</span>
                                <span className="text-xs text-muted-foreground">({dep.id})</span>
                              </li>
                            ))}
                          </ul>
                        ) : (
                          <p className="text-sm text-muted-foreground">No dependencies found</p>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="border rounded-md">
                    <div className="p-2 border-b bg-muted/30">
                      <h3 className="text-sm font-medium flex items-center gap-2">
                        <Code className="h-4 w-4" />
                        Relationships
                      </h3>
                    </div>
                    <ScrollArea className="h-[400px]">
                      <div className="p-4 space-y-4">
                        {attributeDetails.relationships.length > 0 ? (
                          attributeDetails.relationships.map((rel, index) => (
                            <div key={index} className="border rounded-md p-3 space-y-2">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium">{rel.table.name}</span>
                                <span className="text-xs bg-muted px-2 py-1 rounded-full">
                                  {rel.relationship_type}
                                </span>
                              </div>
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <span>Related to:</span>
                                <span className="font-medium">{rel.related_attribute.name}</span>
                              </div>
                            </div>
                          ))
                        ) : (
                          <p className="text-sm text-muted-foreground">No relationships found</p>
                        )}
                      </div>
                    </ScrollArea>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12 text-muted-foreground">
                  No attribute information available
                </div>
              )}
            </TabsContent>
          )}
        </Tabs>
        
        <DialogFooter className="flex justify-between mt-4 gap-4">
          <Button 
            variant="outline" 
            onClick={onClose}
            disabled={!canClose}
          >
            Cancel
          </Button>
          
          {activeTab === 'expression' && !daxExpression && !isDaxLoading && (
            <Button 
              onClick={handleConvert} 
              className="bg-orange-500 hover:bg-orange-600"
              disabled={isLoading || !expression}
            >
              Convert
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ExpressionModal; 