from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
import logging
import traceback
from mstrio.project_objects import Report
from mstrio.project_objects.report import list_reports
from mstrio.modeling.schema import Attribute
from routers.utils import get_connection
from mstrio.modeling.metric import Metric

logger = logging.getLogger("bi_accelerator")
router = APIRouter(prefix="/api", tags=["reports"])

@router.get("/reports")
async def get_reports(project_id: str):
    logger.info(f"Fetching reports for project_id: {project_id}")
    try:
        # Connect to the selected project and fetch reports
        project_conn = get_connection(project_id)
        reports = list_reports(connection=project_conn)
        reports_data = [{"id": r.id, "name": r.name, "ext_type": str(r.ext_type).split(".")[-1]} for r in reports]
        logger.info(f"Successfully retrieved {len(reports_data)} reports for project {project_id}")
        logger.debug(f"Reports data: {reports_data}")
        return JSONResponse(content=reports_data)
    except Exception as e:
        logger.error(f"Error retrieving reports for project {project_id}: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to retrieve reports: {str(e)}")

@router.get("/report/{report_id}")
async def get_report_details(report_id: str, project_id: str):
    logger.info(f"Fetching details for report_id: {report_id} in project_id: {project_id}")
    try:
        project_conn = get_connection(project_id)
        report_obj = Report(connection=project_conn, id=report_id)
        logger.debug(f"Successfully loaded report object with name: {report_obj.name}")
        
        # Get raw properties
        raw_properties = {}
        try:
            raw_properties = report_obj.list_properties()
            # Convert any non-serializable types to strings
            for key, value in raw_properties.items():
                if not isinstance(value, (str, int, float, bool, type(None), list, dict)):
                    raw_properties[key] = str(value)
                elif isinstance(value, list):
                    # Handle lists that might contain non-serializable objects
                    raw_properties[key] = [
                        item if isinstance(item, (str, int, float, bool, type(None), dict)) else str(item)
                        for item in value
                    ]
                elif isinstance(value, dict):
                    # Handle dictionaries that might contain non-serializable values
                    for k, v in list(value.items()):
                        if not isinstance(v, (str, int, float, bool, type(None), list, dict)):
                            value[k] = str(v)
            logger.debug(f"Retrieved properties for report {report_id}")
        except Exception as prop_err:
            logger.error(f"Error retrieving properties for report {report_id}: {str(prop_err)}")
        
        # Get attributes and metrics
        attributes = []
        metrics = []
        
        try:
            attributes = []
            if hasattr(report_obj, "attributes"):
                for attr in report_obj.attributes:
                    attr_info = {}
                    if hasattr(attr, 'id') and hasattr(attr, 'name'):
                        attr_info = {"id": attr.id, "name": attr.name}
                    elif isinstance(attr, dict) and 'id' in attr and 'name' in attr:
                        attr_info = {"id": attr['id'], "name": attr['name']}
                    
                    # Check if attribute has Pass-Through function
                    has_pass_through = False
                    # try:
                    #     # Get the attribute object to check its dependencies
                    #     attr_obj = Attribute(connection=project_conn, id=attr_info["id"])
                        
                    #     # Check for pass-through functions in dependencies
                    #     dependencies = attr_obj.list_dependencies()
                    #     for dep in dependencies:
                    #         # Check if dependency is a pass-through function like ApplySimple
                    #         if isinstance(dep, dict) and 'name' in dep and 'applysimple' in dep['name'].lower():
                    #             has_pass_through = True
                    #             break
                    #         elif hasattr(dep, 'name') and 'applysimple' in dep.name.lower():
                    #             has_pass_through = True
                    #             break
                    # except Exception as func_err:
                    #     logger.warning(f"Could not check pass-through functions for attribute {attr_info['name']}: {str(func_err)}")
                    
                    # Add pass-through information to attribute
                    attr_info["has_pass_through_function"] = has_pass_through
                    attributes.append(attr_info)
                        
                logger.debug(f"Retrieved {len(attributes)} attributes for report {report_id}")
        except Exception as attr_err:
            logger.error(f"Error retrieving attributes for report {report_id}: {str(attr_err)}")
            attributes = []
        
        try:
            metrics = []
            if hasattr(report_obj, "metrics"):
                for metric in report_obj.metrics:
                    metric_info = {}
                    if hasattr(metric, 'id') and hasattr(metric, 'name'):
                        metric_info = {"id": metric.id, "name": metric.name}
                    elif isinstance(metric, dict) and 'id' in metric and 'name' in metric:
                        metric_info = {"id": metric['id'], "name": metric['name']}
                    
                    # Check if metric has Pass-Through function
                    has_pass_through = False
                    # try:
                    #     # Get the metric object to check its dependencies
                    #     metric_obj = Metric(connection=project_conn, id=metric_info["id"])
                        
                    #     # Check for pass-through functions in dependencies
                    #     dependencies = metric_obj.list_dependencies()
                    #     for dep in dependencies:
                    #         # Check if dependency is a pass-through function like ApplySimple
                    #         if isinstance(dep, dict) and 'name' in dep and 'applysimple' in dep['name'].lower():
                    #             has_pass_through = True
                    #             break
                    #         elif hasattr(dep, 'name') and 'applysimple' in dep.name.lower():
                    #             has_pass_through = True
                    #             break
                    # except Exception as func_err:
                    #     logger.warning(f"Could not check pass-through functions for metric {metric_info['name']}: {str(func_err)}")
                    
                    # Add pass-through information to metric
                    metric_info["has_pass_through_function"] = has_pass_through
                    metrics.append(metric_info)
                        
                logger.debug(f"Retrieved {len(metrics)} metrics for report {report_id}")
        except Exception as metric_err:
            logger.error(f"Error retrieving metrics for report {report_id}: {str(metric_err)}")
            metrics = []
        
        # Try to get SQL
        sql = "Error fetching SQL view: 'Report' object has no attribute 'sql'"
        try:
            # if hasattr(report_obj, "sql"):
            sql = report_obj.sql  
            logger.debug(f"Retrieved SQL for report {report_id}")
        except Exception as sql_err:
            logger.error(f"Error retrieving SQL for report {report_id}: {str(sql_err)}")
            sql = f"Error fetching SQL view: {str(sql_err)}"
        
        # Try to get report data
        data = None
        try:
            df = report_obj.to_dataframe()
            if not df.empty:
                # Format as a string representation similar to the example
                data = df.head().to_json(orient='records')
                logger.debug(f"Retrieved data preview for report {report_id}")
        except Exception as data_err:
            logger.error(f"Error retrieving data for report {report_id}: {str(data_err)}")
            data = "Error fetching report data"
        
        # Build the response with only the requested fields, ensuring all values are JSON serializable
        details = {
            "id": str(report_id),
            "name": str(report_obj.name),
            "description": str(raw_properties.get("description", None) or "None"),
            "type": str(raw_properties.get("type", None) or "None"),
            "subtype": raw_properties.get("subtype", None),
            "ext_type": str(raw_properties.get("ext_type", None) or "None"),
            "abbreviation": raw_properties.get("abbreviation", None),
            "instance_id": raw_properties.get("instance_id", None),
            "date_created": str(raw_properties.get("date_created", None) or "None"),
            "date_modified": str(raw_properties.get("date_modified", None) or "None"),
            "version": raw_properties.get("version", None),
            "owner": str(raw_properties.get("owner", None) or "None"),
            "view_media": raw_properties.get("view_media", None),
            "ancestors": raw_properties.get("ancestors", []),
            "certified_info": str(raw_properties.get("certified_info", None) or "None"),
            "attributes": attributes,
            "metrics": metrics,
            "acg": raw_properties.get("acg", None),
            "acl": raw_properties.get("acl", []),
            "Report SQL View:": sql,
            "Report Data (first few rows):": data
        }
        
        logger.info(f"Successfully retrieved details for report {report_id}")
        return JSONResponse(content=details)
    except Exception as e:
        logger.error(f"Error retrieving report {report_id} details: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=404, detail=f"Report not found or error: {str(e)}")

@router.get("/report-attribute/{attribute_id}")
async def get_report_attribute_info(attribute_id: str, project_id: str):
    logger.info(f"Fetching attribute info for attribute_id: {attribute_id} in project_id: {project_id}")
    try:
        project_conn = get_connection(project_id)
        attribute_obj = Attribute(connection=project_conn, id=attribute_id)
        logger.debug(f"Successfully loaded attribute object with name: {attribute_obj.name}")
        
        # Get tables
        tables = []
        try:
            tables_data = attribute_obj.list_tables()
            # Extract only names and IDs from tables data
            if isinstance(tables_data, list):
                for table in tables_data:
                    # Check if it's a SchemaObjectReference or similar object
                    table_info = {}
                    if hasattr(table, 'object_id'):
                        table_info["id"] = table.object_id
                    if hasattr(table, 'name'):
                        table_info["name"] = table.name
                    
                    # If we couldn't extract structured data, use the string representation
                    if not table_info and str(table):
                        # Try to extract ID and name using regex
                        import re
                        id_match = re.search(r"object_id='([^']+)'", str(table))
                        name_match = re.search(r"name='([^']+)'", str(table))
                        if id_match:
                            table_info["id"] = id_match.group(1)
                        if name_match:
                            table_info["name"] = name_match.group(1)
                    
                    if table_info:
                        tables.append(table_info)
            logger.debug(f"Retrieved tables for attribute {attribute_id}")
        except Exception as tables_err:
            logger.error(f"Error retrieving tables for attribute {attribute_id}: {str(tables_err)}")
            tables = []
        
        # Get relationship candidates
        relationships = []
        try:
            rel_candidates_data = attribute_obj.list_relationship_candidates()
            # Extract names and IDs from relationship candidates
            if isinstance(rel_candidates_data, dict):
                for table_name, candidates in rel_candidates_data.items():
                    # Find the table ID if available
                    table_id = None
                    for table in tables:
                        if table.get('name') == table_name:
                            table_id = table.get('id')
                            break
                    
                    table_info = {
                        "name": table_name,
                        "id": table_id
                    }
                    
                    for candidate in candidates:
                        candidate_info = {}
                        # Check if it's an object with id and name attributes
                        if hasattr(candidate, 'object_id'):
                            candidate_info["id"] = candidate.object_id
                        if hasattr(candidate, 'name'):
                            candidate_info["name"] = candidate.name
                        
                        # If we couldn't extract structured data, use string representation
                        if not candidate_info and str(candidate):
                            # Try to extract ID and name using regex
                            import re
                            id_match = re.search(r"object_id='([^']+)'", str(candidate))
                            name_match = re.search(r"name='([^']+)'", str(candidate))
                            if id_match:
                                candidate_info["id"] = id_match.group(1)
                            if name_match:
                                candidate_info["name"] = name_match.group(1)
                        
                        if candidate_info:
                            # Add as a proper relationship
                            relationship = {
                                "table": table_info,
                                "related_attribute": candidate_info,
                                "relationship_type": "related attribute in table"
                            }
                            relationships.append(relationship)
            # If it's a string (already converted earlier in error handling)
            elif isinstance(rel_candidates_data, str):
                # Try to parse the string to extract structured data
                import re
                table_matches = re.findall(r"'([^']+)': \[(.*?)\]", rel_candidates_data)
                for table_match in table_matches:
                    table_name = table_match[0]
                    candidates_str = table_match[1]
                    
                    # Find the table ID if available
                    table_id = None
                    for table in tables:
                        if table.get('name') == table_name:
                            table_id = table.get('id')
                            break
                    
                    table_info = {
                        "name": table_name,
                        "id": table_id
                    }
                    
                    # Extract each candidate
                    candidate_matches = re.finditer(r"SchemaObjectReference\([^)]*object_id='([^']+)'[^)]*name='([^']+)'", candidates_str)
                    for candidate_match in candidate_matches:
                        candidate_id = candidate_match.group(1)
                        candidate_name = candidate_match.group(2)
                        
                        # Add as a proper relationship
                        relationship = {
                            "table": table_info,
                            "related_attribute": {
                                "id": candidate_id,
                                "name": candidate_name
                            },
                            "relationship_type": "related attribute in table"
                        }
                        relationships.append(relationship)
            
            logger.debug(f"Retrieved relationship candidates for attribute {attribute_id}")
        except Exception as rel_err:
            logger.error(f"Error retrieving relationship candidates for attribute {attribute_id}: {str(rel_err)}")
            relationships = []
        
        # Get dependencies
        dependencies = []
        try:
            dependencies_data = attribute_obj.list_dependencies()
            # Extract only names and IDs from dependencies
            if isinstance(dependencies_data, list):
                for dep in dependencies_data:
                    # Check if it's a dict with id and name keys
                    if isinstance(dep, dict) and 'id' in dep and 'name' in dep:
                        dependencies.append({
                            "id": dep['id'],
                            "name": dep['name']
                        })
                    # Check if it's an object with id and name attributes
                    elif hasattr(dep, 'id') and hasattr(dep, 'name'):
                        dependencies.append({
                            "id": dep.id,
                            "name": dep.name
                        })
            logger.debug(f"Retrieved dependencies for attribute {attribute_id}")
        except Exception as dep_err:
            logger.error(f"Error retrieving dependencies for attribute {attribute_id}: {str(dep_err)}")
            dependencies = []
        
        # Build the simplified response with only names and IDs
        attribute_info = {
            "id": attribute_id,
            "name": attribute_obj.name,
            "tables": tables,
            "relationships": relationships,
            "dependencies": dependencies
        }
        
        logger.info(f"Successfully retrieved attribute info for attribute {attribute_id}")
        return JSONResponse(content=attribute_info)
    except Exception as e:
        logger.error(f"Error retrieving attribute {attribute_id} info: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=404, detail=f"Attribute not found or error: {str(e)}") 