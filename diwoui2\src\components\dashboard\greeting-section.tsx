import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { userProfile } from "@/lib/mock-data";
import { Plus } from "lucide-react";
import { useDashboardStore } from "@/store/dashboardStore";
import { Heading, Text } from "@/lib/design-system/typography";
import { useBrandTheme } from "@/themes/brand-theme-provider";
import { cn } from "@/lib/utils";

export function GreetingSection() {
  const { actions } = useDashboardStore();
  const { currentTheme } = useBrandTheme();
  const greeting = actions.getGreeting();
  const formattedDate = actions.formatCurrentDate();
  const firstName = userProfile.name.split(" ")[0];
  
  // Get theme-specific button classes
  const getButtonClasses = () => {
    if (currentTheme?.id === 'purple') {
      return "bg-purple-600 hover:bg-purple-700 text-white";
    } else if (currentTheme?.id === 'green') {
      return "bg-green-600 hover:bg-green-700 text-white";
    } else if (currentTheme?.id === 'google') {
      return "bg-blue-600 hover:bg-blue-700 text-white";
    } else {
      return "bg-primary hover:bg-primary/90 text-white";
    }
  };
  
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8 animate-fade-in">
      <div>
        <h1 className="text-4xl font-bold tracking-tight">
          <span className="text-foreground dark:text-gray-200">{greeting}, </span> 
          <span className="name-gradient">{firstName}</span>
        </h1>
        <Text color="muted" className="mt-1">{formattedDate}</Text>
      </div>
      <div className="sm:ml-auto">
        <Link to="/conversation/new">
          <Button 
            className={cn(
              "font-medium pulse-animation theme-button",
              getButtonClasses()
            )}
            style={{
              backgroundColor: `var(--theme-primary, ${currentTheme?.colors.primary[500] || '#D32F2F'})`,
            }}
          >
            <Plus className="mr-2 h-4 w-4" /> New Analysis
          </Button>
        </Link>
      </div>
    </div>
  );
}
