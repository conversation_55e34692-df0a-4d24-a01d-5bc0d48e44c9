import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { ChevronLeft } from 'lucide-react';
import { apiService, Project, ReportDetail as IReportDetail } from '@/services/api';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import Button from '@/components/shared/Button';
import { Loader } from '@/components/shared/Loader';

const statusBadge = (status: string) => (
  <span className={`inline-block px-3 py-1 rounded-full text-xs font-semibold ${status === 'Mapped' ? 'bg-blue-900 text-white' : 'bg-gray-200 text-gray-800'}`}>{status}</span>
);

// Helper function to clean column names
const cleanColumnName = (columnName: string): string => {
  if (columnName.includes('@')) {
    // Extract the part after the @ symbol
    const afterAt = columnName.split('@')[1];
    if (afterAt) {
      return afterAt.trim();
    }
  }
  return columnName.replace(/_/g, ' ');
};

const SpecReportDetail = () => {
  const { id: projectId, reportId } = useParams<{ id: string; reportId: string }>();
  const navigate = useNavigate();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);

  useEffect(() => {
    const storedProject = localStorage.getItem('selectedProject');
    if (storedProject) {
      const project = JSON.parse(storedProject);
      if (project.id === projectId) {
        setSelectedProject(project);
      } else {
        navigate('/specs');
      }
    } else {
      navigate('/specs');
    }
  }, [projectId, navigate]);

  const { data: report, isLoading, error } = useQuery<IReportDetail>({
    queryKey: ['specReportDetail', reportId, projectId],
    queryFn: () => (projectId && reportId) ? apiService.getReport(reportId, projectId) : Promise.reject('Missing project or report ID'),
    enabled: !!projectId && !!reportId,
  });

  // Parse preview table data
  let previewRows: any[] = [];
  let previewColumns: string[] = [];
  if (report?.data) {
    try {
      const jsonData = JSON.parse(report.data);
      if (Array.isArray(jsonData) && jsonData.length > 0) {
        previewRows = jsonData;
        previewColumns = Object.keys(jsonData[0]);
      }
    } catch {}
  }

  // Helper for mapping fields (attributes/metrics)
  const renderMappingTable = (items: any[], type: 'attribute' | 'metric') => {
    if (!items || items.length === 0) return <p className="text-muted-foreground">No {type === 'attribute' ? 'attributes' : 'metrics'} found</p>;
    // Try to use mapping fields if present, else fallback to name/id
    const hasMapping = items[0] && (items[0].mapped_name || items[0].power_bi_field || items[0].status);
    return (
      <table className="w-full border-collapse mb-8">
        <thead>
          <tr className="bg-slate-50">
            <th className="p-2 text-left border">{type === 'attribute' ? 'MicroStrategy Field' : 'MicroStrategy Metric'}</th>
            <th className="p-2 text-left border">Power BI {type === 'attribute' ? 'Field' : 'Measure'}</th>
            <th className="p-2 text-left border">Data Type</th>
            <th className="p-2 text-left border">Status</th>
          </tr>
        </thead>
        <tbody>
          {items.map((item, idx) => (
            <tr key={item.id || idx} className={idx % 2 === 0 ? '' : 'bg-slate-50'}>
              <td className="p-2 border font-mono">{item.name}</td>
              <td className="p-2 border font-bold">{item.mapped_name || item.power_bi_field || item.name}</td>
              <td className="p-2 border">{item.data_type || 'Text'}</td>
              <td className="p-2 border">{statusBadge(item.status || 'Mapped')}</td>
            </tr>
          ))}
        </tbody>
      </table>
    );
  };

  if (!selectedProject) return <Loader />;

  return (
    <div className="container px-4 mx-auto animate-fade-in">
      <div className="flex items-center mb-6">
        <Button variant="link" className="p-0 mr-4" onClick={() => navigate(`/specs/${projectId}/reports`)}>
          <ChevronLeft className="h-5 w-5" /> Back to Reports
        </Button>
        <h1 className="text-3xl font-bold">Migration Specs</h1>
        <div className="flex-1" />
        <Button variant="default" className="ml-4">Export to PDF</Button>
      </div>

      {/* Report Layout Section */}
      <div className="bg-white rounded-xl shadow p-8 mb-8">
        <h2 className="text-2xl font-bold mb-1">Report Layout</h2>
        <p className="text-muted-foreground mb-6">MicroStrategy report structure and configuration</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-6">
          <div>
            <div className="mb-2"><span className="font-semibold">Report Type</span><div>{report?.type || '-'}</div></div>
            <div className="mb-2"><span className="font-semibold">Report Name</span><div>{report?.name || '-'}</div></div>
            <div className="mb-2"><span className="font-semibold">Template</span><div>{report?.template || '-'}</div></div>
          </div>
          <div>
            <div className="mb-2"><span className="font-semibold">Description</span><div>{report?.description || '-'}</div></div>
            <div className="mb-2"><span className="font-semibold">Dimensions</span><div>{report?.dimensions || '-'}</div></div>
          </div>
        </div>   
        <h3 className="text-xl font-semibold mb-2">Report Preview</h3>
        <div className="bg-slate-50 rounded-lg p-4 overflow-x-auto">
          {previewRows.length > 0 ? (
            <table className="w-full border-collapse">
              <thead>
                <tr>
                  {previewColumns.map((col) => (
                    <th key={col} className="p-2 text-left border-b bg-slate-100 font-semibold">{cleanColumnName(col)}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {previewRows.map((row, idx) => (
                  <tr key={idx} className={idx % 2 === 0 ? '' : 'bg-white'}>
                    {previewColumns.map((col) => (
                      <td key={col} className="p-2 border-b">{row[col]}</td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <div className="text-muted-foreground">No preview data available.</div>
          )}
        </div>
      </div>

      {/* Mapping Section */}
      <div className="bg-white rounded-xl shadow p-8 mb-8">
        <h2 className="text-2xl font-bold mb-1">Mapping of Attributes and Metrics</h2>
        <p className="text-muted-foreground mb-6">Field mapping from MicroStrategy to Power BI</p>
        <h3 className="text-lg font-semibold mb-2">Attributes</h3>
        {renderMappingTable(report?.attributes || [], 'attribute')}
        <h3 className="text-lg font-semibold mb-2">Metrics</h3>
        {renderMappingTable(report?.metrics || [], 'metric')}
      </div>
    </div>
  );
};

export default SpecReportDetail; 
