import { But<PERSON> } from "@/components/ui/button";
import { AlertCard } from "@/components/dashboard/alert-card";
import { cn } from "@/lib/utils";
import { Grid2X2, List, AlertTriangle } from "lucide-react";
import { useDashboardStore } from "@/store/dashboardStore";
import { useBrandTheme } from "@/themes/brand-theme-provider";

export function SuggestionsSection() {
  const { viewMode, alerts, actions } = useDashboardStore();
  const { currentTheme } = useBrandTheme();
  
  // Get theme primary color or fallback to darker red
  const primaryColor = 
    currentTheme?.colors.primary[500] || 
    "var(--theme-primary, #D32F2F)";

  return (
    <div className="mt-10">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <AlertTriangle 
            className="h-5 w-5 text-primary" 
            style={{ color: primaryColor }}
          />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Attention Required</h2>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === "card" ? "default" : "outline"}
            size="sm"
            onClick={() => actions.setViewMode("card")}
            className={cn(
              "h-8 w-8 p-0",
              viewMode === "card" ? "bg-primary hover:bg-primary/90 text-white" : ""
            )}
            style={viewMode === "card" ? { backgroundColor: primaryColor } : {}}
            aria-label="Card view"
          >
            <Grid2X2 className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === "list" ? "default" : "outline"}
            size="sm"
            onClick={() => actions.setViewMode("list")}
            className={cn(
              "h-8 w-8 p-0",
              viewMode === "list" ? "bg-primary hover:bg-primary/90 text-white" : ""
            )}
            style={viewMode === "list" ? { backgroundColor: primaryColor } : {}}
            aria-label="List view"
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <div className={cn(
        "grid gap-6",
        viewMode === "card" ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3" : "grid-cols-1"
      )}>
        {alerts.map((alert) => (
          <AlertCard
            key={alert.id}
            id={alert.id}
            title={alert.title}
            description={alert.description}
            category={alert.category}
            severity={alert.severity as "low" | "medium" | "high" | "critical"}
            time={alert.time}
            action={alert.action}
          />
        ))}
      </div>
    </div>
  );
}
