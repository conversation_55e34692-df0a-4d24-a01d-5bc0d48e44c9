from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
import logging
import traceback
import os
import json
from openai import OpenAI
from mstrio.modeling import list_metrics, Metric
from routers.utils import get_connection

logger = logging.getLogger("bi_accelerator")
router = APIRouter(prefix="/api", tags=["metrics"])

@router.get("/metrics")
async def get_metrics(project_id: str):
    logger.info(f"Fetching metrics for project_id: {project_id}")
    # Get all metrics in the project
    try:
        project_conn = get_connection(project_id)
        metrics = list_metrics(connection=project_conn)
        metrics_data = [{"id": m.id, "name": m.name} for m in metrics]
        logger.info(f"Successfully retrieved {len(metrics_data)} metrics for project {project_id}")
        return JSONResponse(content=metrics_data)
    except Exception as e:
        logger.error(f"Error retrieving metrics for project {project_id}: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to retrieve metrics: {str(e)}")

@router.get("/metric/{metric_id}")
async def get_metric_details(metric_id: str, project_id: str):
    logger.info(f"Fetching details for metric_id: {metric_id} in project_id: {project_id}")
    # Get detailed information about a specific metric including dependencies
    try:
        project_conn = get_connection(project_id)
        metric_obj = Metric(connection=project_conn, id=metric_id)
        logger.debug(f"Successfully loaded metric object with name: {metric_obj.name}")
        
        # Get properties with error handling and convert non-serializable objects to strings
        properties = {}
        try:
            raw_properties = metric_obj.list_properties()
            # Convert non-serializable objects to their string representation
            for key, value in raw_properties.items():
                if isinstance(value, (str, int, float, bool, type(None))):
                    properties[key] = value
                elif isinstance(value, (list, tuple)):
                    # For collections, convert each item if needed
                    try:
                        properties[key] = [str(item) if not isinstance(item, (str, int, float, bool, type(None), dict, list)) else item for item in value]
                    except:
                        properties[key] = str(value)
                elif isinstance(value, dict):
                    # For dictionaries, attempt to convert non-serializable values
                    try:
                        serializable_dict = {}
                        for k, v in value.items():
                            if isinstance(v, (str, int, float, bool, type(None), dict, list)):
                                serializable_dict[k] = v
                            else:
                                serializable_dict[k] = str(v)
                        properties[key] = serializable_dict
                    except:
                        properties[key] = str(value)
                else:
                    # For any other type, convert to string
                    properties[key] = str(value)
            
            logger.debug(f"Retrieved properties for metric {metric_id}")
        except Exception as prop_err:
            logger.error(f"Error retrieving properties for metric {metric_id}: {str(prop_err)}")
            properties = {"error": str(prop_err)}
        
        # Get metric dependencies with error handling
        dependencies = []
        try:
            deps = metric_obj.list_dependencies()
            # Process dependencies to ensure they're serializable
            if isinstance(deps, list):
                dependencies = []
                for dep in deps:
                    if isinstance(dep, dict):
                        # Ensure all values in the dict are serializable
                        processed_dep = {}
                        for k, v in dep.items():
                            if isinstance(v, (str, int, float, bool, type(None), dict, list)):
                                processed_dep[k] = v
                            else:
                                processed_dep[k] = str(v)
                        dependencies.append(processed_dep)
                    else:
                        dependencies.append(str(dep))
            else:
                dependencies = str(deps)
                
            logger.debug(f"Retrieved dependencies for metric {metric_id}")
        except Exception as dep_err:
            logger.error(f"Error retrieving dependencies for metric {metric_id}: {str(dep_err)}")
            dependencies = [{"error": str(dep_err)}]
        
        details = {
            "id": metric_id,
            "name": metric_obj.name,
            "properties": properties,
            "dependencies": dependencies
        }
        
        logger.info(f"Successfully retrieved details for metric {metric_id}")
        return JSONResponse(content=details)
    except Exception as e:
        logger.error(f"Error retrieving metric {metric_id} details: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=404, detail=f"Metric not found or error: {str(e)}")

@router.get("/metric/{metric_id}/expression")
async def get_metric_expression(metric_id: str, project_id: str):
    logger.info(f"Fetching expression for metric_id: {metric_id} in project_id: {project_id}")
    try:
        project_conn = get_connection(project_id)
        metric_obj = Metric(connection=project_conn, id=metric_id)
        
        # Get the metric expression
        try:
            expression = metric_obj.expression
            
            # Handle Expression object by converting it to a string representation
            if hasattr(expression, '__dict__'):
                # If expression is an object with __dict__, convert its attributes to a dictionary
                expression_data = {}
                for key, value in expression.__dict__.items():
                    if isinstance(value, (str, int, float, bool, type(None))):
                        expression_data[key] = value
                    else:
                        expression_data[key] = str(value)
                expression = expression_data
            elif not isinstance(expression, (str, dict, int, float, bool, type(None))):
                # If it's not a basic type, convert it to string
                expression = str(expression)
            
            # If expression is empty or None, try to get it from properties
            if not expression:
                properties = metric_obj.list_properties()
                expression = properties.get("expression", "")
                if not isinstance(expression, (str, dict, int, float, bool, type(None))):
                    expression = str(expression)
            
            logger.info(f"Successfully retrieved expression for metric {metric_id}")
            return JSONResponse(content={"id": metric_id, "name": metric_obj.name, "expression": expression})
        except Exception as expr_err:
            logger.error(f"Error accessing expression for metric {metric_id}: {str(expr_err)}")
            logger.error(traceback.format_exc())
            raise HTTPException(status_code=500, detail=f"Error accessing metric expression: {str(expr_err)}")
    
    except Exception as e:
        logger.error(f"Error retrieving metric {metric_id} expression: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=404, detail=f"Metric not found or error: {str(e)}")

@router.get("/metric/{metric_id}/to_dax")
async def convert_metric_to_dax(metric_id: str, project_id: str):
    logger.info(f"Converting expression to DAX for metric_id: {metric_id} in project_id: {project_id}")
    try:
        # Get metric and its expression
        project_conn = get_connection(project_id)
        metric_obj = Metric(connection=project_conn, id=metric_id)
        
        # Get the metric expression
        try:
            expression = metric_obj.expression
            
            # Handle Expression object by converting it to a string representation
            if hasattr(expression, '__dict__'):
                # If expression is an object with __dict__, convert its attributes to a dictionary
                expression_data = {}
                for key, value in expression.__dict__.items():
                    if isinstance(value, (str, int, float, bool, type(None))):
                        expression_data[key] = value
                    else:
                        expression_data[key] = str(value)
                expression = str(expression_data)
            elif not isinstance(expression, (str, dict, int, float, bool, type(None))):
                # If it's not a basic type, convert it to string
                expression = str(expression)
            
            # If expression is empty or None, try to get it from properties
            if not expression:
                properties = metric_obj.list_properties()
                expression = properties.get("expression", "")
                if not isinstance(expression, (str, dict, int, float, bool, type(None))):
                    expression = str(expression)
            
            # If still no expression, return appropriate message
            if not expression:
                return JSONResponse(content={"dax": ""})
            
            # Call OpenAI to convert expression to DAX
            client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))
            
            prompt = f"""
            Convert the following MicroStrategy metric expression to a DAX expression:
            
            MicroStrategy metric: {metric_obj.name}
            Expression: {expression}
            
            Provide ONLY the equivalent DAX expression without any explanation or additional text.
            """
            
            try:
                response = client.chat.completions.create(
                    model="o3-mini",
                    messages=[
                        {"role": "system", "content": "You are an expert in both MicroStrategy expressions and DAX (Data Analysis Expressions) used in Power BI, Analysis Services, etc. Your task is to convert MicroStrategy metric expressions to their DAX equivalents. Return ONLY the DAX expression without any explanation, additional text, or markdown formatting."},
                        {"role": "user", "content": prompt}
                    ],
                    # temperature=0.2,
                )
                
                # Return simplified response with just the DAX expression
                return JSONResponse(content={"dax": response.choices[0].message.content.strip()})
                
            except Exception as openai_err:
                logger.error(f"Error calling OpenAI for metric {metric_id}: {str(openai_err)}")
                raise HTTPException(
                    status_code=500, 
                    detail=f"Error during OpenAI conversion: {str(openai_err)}"
                )
                
        except Exception as expr_err:
            logger.error(f"Error accessing expression for metric {metric_id}: {str(expr_err)}")
            logger.error(traceback.format_exc())
            raise HTTPException(
                status_code=500, 
                detail=f"Error accessing metric expression: {str(expr_err)}"
            )
    
    except Exception as e:
        logger.error(f"Error converting metric {metric_id} expression to DAX: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=404, 
            detail=f"Metric not found or error: {str(e)}"
        )

@router.get("/metrics/filter_by_dependency")
async def filter_metrics_by_dependency(project_id: str):
    logger.info(f"Filtering metrics with Apply functions dependencies for project_id: {project_id}")
    
    # List of Apply functions to search for
    apply_functions = ["ApplySimple", "ApplyComparison", "ApplyAgg", "ApplyLogic", "ApplyOLAP"]
    
    try:
        project_conn = get_connection(project_id)
        metrics = list_metrics(connection=project_conn)
        
        filtered_metrics = []
        
        for metric in metrics:
            try:
                metric_obj = Metric(connection=project_conn, id=metric.id)
                dependencies = metric_obj.list_dependencies()
                
                # Check if any dependency contains any of the Apply functions
                has_apply_function = False
                for dep in dependencies:
                    dep_name = dep.get('name', '')
                    if any(apply_func in dep_name for apply_func in apply_functions):
                        has_apply_function = True
                        break
                
                if has_apply_function:
                    # Store metric with its id and name
                    filtered_metrics.append({
                        "id": metric.id,
                        "name": metric.name
                    })
            except Exception as metric_err:
                logger.warning(f"Error processing metric {metric.id}: {str(metric_err)}")
                continue
        
        logger.info(f"Found {len(filtered_metrics)} metrics with Apply function dependencies")
        return JSONResponse(content=filtered_metrics)
    except Exception as e:
        logger.error(f"Error filtering metrics for project {project_id}: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to filter metrics: {str(e)}")

@router.post("/metrics/batch_convert_to_dax")
async def batch_convert_metrics_to_dax(metric_ids: list[str], project_id: str):
    logger.info(f"Batch converting {len(metric_ids)} metrics to DAX for project_id: {project_id}")
    try:
        project_conn = get_connection(project_id)
        results = []
        
        for metric_id in metric_ids:
            try:
                metric_obj = Metric(connection=project_conn, id=metric_id)
                
                # Extract expression
                expression = None
                if hasattr(metric_obj, 'expression'):
                    expression = metric_obj.expression
                    if not isinstance(expression, str):
                        expression = str(expression)
                    
                    # Clean up the expression text by removing extra information
                    expression = expression.strip()
                
                if not expression:
                    continue
                
                # Call OpenAI to convert expression to DAX
                client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))
                
                prompt = f"""
                Convert the following MicroStrategy metric expression to a DAX expression:
                
                MicroStrategy expression:
                {expression}
                
                Provide ONLY the equivalent DAX expression without any explanation or additional text.
                """
                
                try:
                    response = client.chat.completions.create(
                        model="o3-mini",
                        messages=[
                            {"role": "system", "content": "You are an expert in both MicroStrategy expressions and DAX (Data Analysis Expressions) used in Power BI, Analysis Services, etc. Your task is to convert MicroStrategy metric expressions to their DAX equivalents. Return ONLY the DAX expression without any explanation, additional text, or markdown formatting."},
                            {"role": "user", "content": prompt}
                        ],
                    )
                    
                    results.append({
                        "id": metric_id,
                        "name": metric_obj.name,
                        "mstr_expression": expression,
                        "dax_expression": response.choices[0].message.content.strip()
                    })
                    
                except Exception as openai_err:
                    logger.error(f"Error calling OpenAI for metric {metric_id}: {str(openai_err)}")
                    continue
                    
            except Exception as metric_err:
                logger.warning(f"Error processing metric {metric_id}: {str(metric_err)}")
                continue
        
        # Save results to JSON file
        try:
            output_dir = "converted_expressions"
            os.makedirs(output_dir, exist_ok=True)
            output_file = os.path.join(output_dir, f"metrics_dax_conversions_{project_id}.json")
            
            # Read existing conversions if file exists
            existing_data = []
            if os.path.exists(output_file):
                with open(output_file, 'r') as f:
                    existing_data = json.load(f)
            
            # Merge new results with existing data
            existing_ids = {item["id"] for item in existing_data}
            new_results = [item for item in results if item["id"] not in existing_ids]
            merged_data = existing_data + new_results
            
            # Save merged data
            with open(output_file, 'w') as f:
                json.dump(merged_data, f, indent=2)
            
            logger.info(f"Saved {len(new_results)} new conversions to {output_file}")
            
        except Exception as save_err:
            logger.error(f"Error saving conversions to JSON: {str(save_err)}")
        
        return JSONResponse(content=results)
        
    except Exception as e:
        logger.error(f"Error in batch conversion: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to convert metrics: {str(e)}")

@router.get("/metrics/converted_expressions/{project_id}")
async def get_converted_metrics(project_id: str):
    logger.info(f"Retrieving converted metrics for project_id: {project_id}")
    try:
        output_file = os.path.join("converted_expressions", f"metrics_dax_conversions_{project_id}.json")
        
        if not os.path.exists(output_file):
            return JSONResponse(content=[])
            
        with open(output_file, 'r') as f:
            data = json.load(f)
            
        return JSONResponse(content=data)
        
    except Exception as e:
        logger.error(f"Error retrieving converted metrics: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to retrieve converted metrics: {str(e)}")

@router.post("/metrics/process_apply_functions")
async def process_metrics_with_apply_functions(project_id: str):
    logger.info(f"Starting background processing of metrics with Apply functions for project_id: {project_id}")
    
    # Start a background task
    import threading
    
    def background_task():
        try:
            project_conn = get_connection(project_id)
            metrics = list_metrics(connection=project_conn)
            
            # List of Apply functions to search for
            apply_functions = ["ApplySimple", "ApplyComparison", "ApplyAgg", "ApplyLogic", "ApplyOLAP"]
            
            filtered_metrics = []
            processed_count = 0
            total_count = len(metrics)
            
            logger.info(f"Processing {total_count} metrics for project {project_id}")
            
            for metric in metrics:
                try:
                    metric_obj = Metric(connection=project_conn, id=metric.id)
                    dependencies = metric_obj.list_dependencies()
                    
                    # Check if any dependency contains any of the Apply functions
                    has_apply_function = False
                    for dep in dependencies:
                        dep_name = dep.get('name', '')
                        if any(apply_func in dep_name for apply_func in apply_functions):
                            has_apply_function = True
                            break
                    
                    if has_apply_function:
                        # Store metric with its id and name
                        filtered_metrics.append({
                            "id": metric.id,
                            "name": metric.name
                        })
                    
                    processed_count += 1
                    if processed_count % 10 == 0:  # Log progress every 10 items
                        logger.info(f"Processed {processed_count}/{total_count} metrics")
                        
                except Exception as metric_err:
                    logger.warning(f"Error processing metric {metric.id}: {str(metric_err)}")
                    continue
            
            # Save results to JSON file
            try:
                output_dir = "cached_data"
                os.makedirs(output_dir, exist_ok=True)
                output_file = os.path.join(output_dir, f"metrics_apply_functions_{project_id}.json")
                
                with open(output_file, 'w') as f:
                    json.dump(filtered_metrics, f, indent=2)
                
                logger.info(f"Saved {len(filtered_metrics)} metrics with Apply functions to {output_file}")
                
            except Exception as save_err:
                logger.error(f"Error saving metrics to JSON: {str(save_err)}")
                
        except Exception as e:
            logger.error(f"Error in background processing of metrics: {str(e)}")
            logger.error(traceback.format_exc())
    
    # Start the background thread
    thread = threading.Thread(target=background_task)
    thread.daemon = True
    thread.start()
    
    return JSONResponse(content={"status": "processing_started", "message": "Background processing of metrics started"})

@router.get("/metrics/cached_apply_functions/{project_id}")
async def get_cached_metrics_with_apply_functions(project_id: str):
    logger.info(f"Retrieving cached metrics with Apply functions for project_id: {project_id}")
    try:
        output_file = os.path.join("cached_data", f"metrics_apply_functions_{project_id}.json")
        
        if not os.path.exists(output_file):
            return JSONResponse(content=[])
            
        with open(output_file, 'r') as f:
            data = json.load(f)
            
        return JSONResponse(content=data)
        
    except Exception as e:
        logger.error(f"Error retrieving cached metrics: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to retrieve cached metrics: {str(e)}") 