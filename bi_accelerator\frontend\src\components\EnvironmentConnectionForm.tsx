import React, { useState, useEffect } from 'react';
import { apiService, EnvironmentCredentials } from '@/services/api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';

interface EnvironmentConnectionFormProps {
  onClose?: () => void;
  onSuccess?: () => void;
  onCancel?: () => void;
  initialData?: {
    name?: string;
    is_default?: boolean;
    credentials?: {
      url?: string;
      username?: string;
      password?: string;
      description?: string;
    }
  };
  isEditMode?: boolean;
}

const EnvironmentConnectionForm: React.FC<EnvironmentConnectionFormProps> = ({ 
  onClose, 
  onSuccess,
  onCancel,
  initialData,
  isEditMode = false
}) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    is_default: initialData?.is_default || false,
    credentials: {
      url: initialData?.credentials?.url || '',
      username: initialData?.credentials?.username || '',
      password: initialData?.credentials?.password?.replace(/\*/g, '') || '',
      description: initialData?.credentials?.description || ''
    }
  });
  const [loading, setLoading] = useState(false);
  const [rememberPassword, setRememberPassword] = useState(true);
  
  useEffect(() => {
    if (initialData) {
      setFormData({
        name: initialData.name || '',
        is_default: initialData.is_default || false,
        credentials: {
          url: initialData.credentials?.url || '',
          username: initialData.credentials?.username || '',
          password: initialData.credentials?.password?.replace(/\*/g, '') || '', 
          description: initialData.credentials?.description || ''
        }
      });
    }
  }, [initialData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    if (name.includes('.')) {
      // Handle nested properties (credentials.xxx)
      const [parent, child] = name.split('.');
      setFormData(prev => {
        // Create a proper type-safe copy
        if (parent === 'credentials') {
          return {
            ...prev,
            credentials: {
              ...prev.credentials,
              [child]: value
            }
          };
        }
        // Handle other nested objects if needed
        return prev;
      });
    } else {
      // Handle top-level properties
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Environment name is required",
        variant: "destructive",
      });
      return;
    }
    
    if (!formData.credentials.url.trim()) {
      toast({
        title: "Validation Error",
        description: "Server URL is required",
        variant: "destructive",
      });
      return;
    }
    
    setLoading(true);
    
    try {
      // Prepare data to send
      const dataToSubmit: EnvironmentCredentials = {
        ...formData,
        credentials: {
          ...formData.credentials,
          // If user doesn't want to remember password, don't send it to be stored
          ...(!rememberPassword ? { password: undefined } : {})
        }
      };
      
      // Call the API to save the environment
      const response = await apiService.saveEnvironment(dataToSubmit);
      
      toast({
        title: "Success",
        description: response.message || "Environment saved successfully",
      });
      
      // Call onSuccess if provided
      if (onSuccess) {
        onSuccess();
      } 
      // Fall back to original onClose behavior
      else if (onClose) {
        onClose();
      }
    } catch (error) {
      console.error('Environment save error:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save environment",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Environment Name</Label>
        <Input
          id="name"
          name="name"
          type="text"
          placeholder="Production"
          value={formData.name}
          onChange={handleInputChange}
          required
        />
        <p className="text-xs text-muted-foreground">A descriptive name for this environment</p>
      </div>
      
      <div className="flex items-center space-x-2">
        <Switch
          id="is_default"
          checked={formData.is_default}
          onCheckedChange={(checked) => 
            setFormData(prev => ({ ...prev, is_default: checked }))
          }
        />
        <Label htmlFor="is_default">Set as default environment</Label>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="credentials.url">MicroStrategy Server URL</Label>
        <Input
          id="credentials.url"
          name="credentials.url"
          type="text"
          placeholder="https://example.com/MicroStrategyLibrary/api"
          value={formData.credentials.url}
          onChange={handleInputChange}
          required
        />
        <p className="text-xs text-muted-foreground">The URL of your MicroStrategy server</p>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="credentials.username">Username</Label>
        <Input
          id="credentials.username"
          name="credentials.username"
          type="text"
          placeholder="Your username"
          value={formData.credentials.username}
          onChange={handleInputChange}
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="credentials.password">Password</Label>
        <Input
          id="credentials.password"
          name="credentials.password"
          type="password"
          placeholder="Your password"
          value={formData.credentials.password}
          onChange={handleInputChange}
        />
        
        <div className="flex items-center space-x-2 pt-2">
          <Checkbox 
            id="remember-password" 
            checked={rememberPassword}
            onCheckedChange={(checked: boolean) => setRememberPassword(checked)}
          />
          <label 
            htmlFor="remember-password" 
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Remember password
          </label>
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="credentials.description">Description (Optional)</Label>
        <Textarea
          id="credentials.description"
          name="credentials.description"
          placeholder="Enter a description for this environment"
          value={formData.credentials.description}
          onChange={handleInputChange}
          rows={3}
        />
      </div>
      
      <div className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2 pt-4">
        <Button 
          type="submit" 
          className="flex-1"
          disabled={loading}
        >
          {loading ? 'Saving...' : isEditMode ? 'Update Environment' : 'Save Environment'}
        </Button>
        
        {onCancel && (
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
        )}
      </div>
    </form>
  );
};

export default EnvironmentConnectionForm; 