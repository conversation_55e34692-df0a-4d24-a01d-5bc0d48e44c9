import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { ChevronLeft, LayoutDashboard, List, Code, ChevronRight, FileBarChart, ChevronDown } from 'lucide-react';
import { apiService, Project } from '@/services/api';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import Card, { CardHeader, CardTitle, CardContent } from '@/components/shared/Card';
import Button from '@/components/shared/Button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader } from '@/components/shared/Loader';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

const DashboardDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [activeTab, setActiveTab] = useState('info');
  // New state: one for the selected chapter, one for the selected visualization
  const [selectedChapter, setSelectedChapter] = useState<string>('');
  const [selectedVisualization, setSelectedVisualization] = useState<{
    visualizationKey: string;
    name: string;
  } | null>(null);
  
  useEffect(() => {
    const storedProject = localStorage.getItem('selectedProject');
    if (storedProject) {
      setSelectedProject(JSON.parse(storedProject));
    } else {
      navigate('/projects');
    }
  }, [navigate]);
  
  const { data: dashboard, isLoading, error } = useQuery({
    queryKey: ['dashboard', id, selectedProject?.id],
    queryFn: () =>
      selectedProject && id ? apiService.getDashboard(id, selectedProject.id) : Promise.resolve(null),
    enabled: !!selectedProject && !!id,
  });
  
  const { data: visualizationData, isLoading: isLoadingVizData } = useQuery({
    queryKey: ['visualization-data', id, selectedChapter, selectedVisualization?.visualizationKey],
    queryFn: () =>
      selectedProject && id && selectedChapter && selectedVisualization
        ? apiService.getVisualizationData(
            id,
            selectedChapter,
            selectedVisualization.visualizationKey,
            selectedProject.id
          )
        : Promise.resolve(null),
    enabled: !!selectedProject && !!id && !!selectedChapter && !!selectedVisualization,
  });
  
  if (!selectedProject) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <Alert>
          <AlertTitle>No project selected</AlertTitle>
          <AlertDescription>
            Please select a project first to view dashboard details.
            <Button variant="link" className="p-0 ml-2" onClick={() => navigate('/projects')}>
              Go to Projects
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }
  
  if (!id) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <Alert>
          <AlertTitle>No dashboard selected</AlertTitle>
          <AlertDescription>
            Please select a dashboard to view details.
            <Button variant="link" className="p-0 ml-2" onClick={() => navigate('/dashboards')}>
              Go to Dashboards
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }
  
  return (
    <div className="container px-4 mx-auto animate-fade-in">
      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/')}>
          Home
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/projects')}>
          Projects
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/dashboards')}>
          Dashboards
        </Button>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground truncate max-w-xs">
          {isLoading ? 'Loading...' : dashboard?.name || 'Dashboard Details'}
        </span>
      </div>
      
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div className="flex items-center">
          <Button variant="outline" size="icon" className="mr-4" onClick={() => navigate('/dashboards')}>
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center">
            <div className="p-2 rounded-lg bg-orange-100 mr-3">
              <LayoutDashboard className="h-6 w-6 text-orange-600" />
            </div>
            {isLoading ? (
              <Skeleton className="h-8 w-40" />
            ) : (
              <h1 className="text-2xl font-bold">{dashboard?.name || 'Dashboard Not Found'}</h1>
            )}
          </div>
        </div>
      </div>
      
      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader />
        </div>
      ) : error ? (
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load dashboard details. Please try again later.
          </AlertDescription>
        </Alert>
      ) : !dashboard ? (
        <Alert>
          <AlertTitle>Dashboard Not Found</AlertTitle>
          <AlertDescription>
            The requested dashboard could not be found.
          </AlertDescription>
        </Alert>
      ) : (
        <Tabs defaultValue="info" value={activeTab} onValueChange={setActiveTab} className="animate-fade-in">
          <TabsList className="grid grid-cols-3 w-full md:w-auto mb-8">
            <TabsTrigger value="info" className="flex items-center gap-2">
              <List className="h-4 w-4" />
              <span>Information</span>
            </TabsTrigger>
            <TabsTrigger value="cubes" className="flex items-center gap-2">
              <Code className="h-4 w-4" />
              <span>Connected Cubes</span>
            </TabsTrigger>
            <TabsTrigger value="data" className="flex items-center gap-2">
              <FileBarChart className="h-4 w-4" />
              <span>Data</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="info" className="animate-slide-up">
            <Card>
              <CardHeader>
                <CardTitle>Dashboard Information</CardTitle>
              </CardHeader>
              <CardContent>
                {/* Dashboard information content */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Dashboard ID</h3>
                    <p className="font-mono text-sm bg-muted p-2 rounded">{dashboard.id}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Type</h3>
                    <p>{dashboard.properties?.type || 'Standard Dashboard'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Created</h3>
                    <p>{dashboard.properties?.date_created || 'Unknown'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Modified</h3>
                    <p>{dashboard.properties?.date_modified || 'Unknown'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Owner</h3>
                    <p>{dashboard.properties?.owner || 'Unknown'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Version</h3>
                    <p>{dashboard.properties?.version || 'Unknown'}</p>
                  </div>
                  {dashboard.properties?.subtype && (
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Subtype</h3>
                      <p>{dashboard.properties.subtype}</p>
                    </div>
                  )}
                  {dashboard.properties?.ext_type && (
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Extended Type</h3>
                      <p>{dashboard.properties.ext_type}</p>
                    </div>
                  )}
                  {dashboard.properties?.instance_id && (
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Instance ID</h3>
                      <p className="font-mono text-sm bg-muted p-2 rounded">{dashboard.properties.instance_id}</p>
                    </div>
                  )}
                  {dashboard.properties?.certified_info && (
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Certification Status</h3>
                      <p>{dashboard.properties.certified_info}</p>
                    </div>
                  )}
                  {dashboard.properties?.acg !== undefined && (
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Access Control Group</h3>
                      <p>{dashboard.properties.acg}</p>
                    </div>
                  )}
                  {dashboard.properties?.template_info && (
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Template Status</h3>
                      <p>{dashboard.properties.template_info.template ? 'Is Template' : 'Not a Template'}</p>
                    </div>
                  )}
                </div>
                
                {dashboard.properties?.description && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Description</h3>
                    <p className="text-sm">{dashboard.properties.description}</p>
                  </div>
                )}
                
                {dashboard.properties?.ancestors && dashboard.properties.ancestors.length > 0 && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Location</h3>
                    <div className="flex flex-wrap items-center gap-1 text-sm">
                      {dashboard.properties.ancestors.map((ancestor: any, index: number) => (
                        <React.Fragment key={ancestor.id}>
                          <span>{ancestor.name}</span>
                          {index < dashboard.properties.ancestors.length - 1 && (
                            <ChevronRight className="h-3 w-3 mx-1 text-muted-foreground" />
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  </div>
                )}
                
                {dashboard.properties?.acl && dashboard.properties.acl.length > 0 && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Access Control List</h3>
                    <div className="bg-muted p-2 rounded overflow-auto max-h-40">
                      <ul className="list-disc pl-5 text-sm">
                        {dashboard.properties.acl.map((ace: string, index: number) => (
                          <li key={index} className="mb-1">{ace}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}

                {dashboard.chapters && dashboard.chapters.length > 0 && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Chapters and Pages</h3>
                    <div className="mt-2">
                      {dashboard.chapters.map((chapter) => (
                        <div key={chapter.key} className="mb-4 border rounded-md p-4">
                          <div className="flex items-center mb-2">
                            <LayoutDashboard className="h-4 w-4 mr-2 text-orange-500" />
                            <h4 className="text-sm font-medium">{chapter.name}</h4>
                            {dashboard.properties?.current_chapter === chapter.key && (
                              <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">Current</span>
                            )}
                          </div>
                          {chapter.pages && chapter.pages.length > 0 && (
                            <div className="pl-6">
                              {chapter.pages.map((page) => (
                                <div key={page.key} className="mb-2">
                                  <h5 className="text-sm">{page.name}</h5>
                                  {page.visualizations && page.visualizations.length > 0 && (
                                    <div className="pl-4 mt-1">
                                      <p className="text-xs text-muted-foreground mb-1">Visualizations:</p>
                                      <ul className="list-disc pl-5 text-xs">
                                        {page.visualizations.map((viz) => (
                                          <li key={viz.key}>
                                            {viz.name} <span className="text-muted-foreground">({viz.type})</span>
                                          </li>
                                        ))}
                                      </ul>
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="cubes" className="animate-slide-up">
            <Card>
              <CardHeader>
                <CardTitle>Connected Cubes</CardTitle>
              </CardHeader>
              <CardContent>
                {dashboard.connected_cubes && dashboard.connected_cubes.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {dashboard.connected_cubes.map((cube: any) => (
                      <Card key={cube.id} className="border hover:border-brand/50 hover:shadow-md transition-all">
                        <CardHeader className="pb-2">
                          <div className="flex items-center">
                            <div className="p-1 rounded-lg bg-blue-100 mr-2">
                              <LayoutDashboard className="h-4 w-4 text-blue-600" />
                            </div>
                            <CardTitle className="text-sm">{cube.name}</CardTitle>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <p className="text-xs text-muted-foreground">
                            Type: {cube.type || 'Standard Cube'}
                          </p>
                          <Button 
                            variant="ghost" 
                            className="mt-2 text-xs h-auto p-0"
                            onClick={() => navigate(`/cubes/${cube.id}`)}
                          >
                            View Cube Details
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground">No connected cubes found for this dashboard</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="data" className="animate-slide-up">
            <Card>
              <CardHeader>
                <CardTitle>Visualization Data</CardTitle>
              </CardHeader>
              <CardContent>
                {dashboard.chapters && dashboard.chapters.length > 0 ? (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Chapter selector card */}
                      <Card className="border-2 border-dashed hover:border-solid hover:border-brand/50 transition-all">
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm flex items-center gap-2">
                            <LayoutDashboard className="h-4 w-4 text-brand" />
                            Select Chapter
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <Select
                            value={selectedChapter}
                            onValueChange={(value) => {
                              setSelectedChapter(value);
                              setSelectedVisualization(null);
                            }}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Choose a chapter..." />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectGroup>
                                {dashboard.chapters.map((chapter: any) => (
                                  <SelectItem key={chapter.key} value={chapter.key}>
                                    {chapter.name}
                                  </SelectItem>
                                ))}
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                          <p className="mt-2 text-xs text-muted-foreground">
                            Select a chapter to view its available visualizations
                          </p>
                        </CardContent>
                      </Card>

                      {/* Visualization selector card */}
                      <Card className={`border-2 border-dashed hover:border-solid transition-all ${!selectedChapter ? 'opacity-50' : 'hover:border-brand/50'}`}>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm flex items-center gap-2">
                            <FileBarChart className="h-4 w-4 text-brand" />
                            Select Visualization
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <Select
                            value={selectedVisualization?.visualizationKey || ''}
                            onValueChange={(vizKey) => {
                              const chapter = dashboard.chapters.find((ch: any) => ch.key === selectedChapter);
                              if (chapter) {
                                let selectedViz = null;
                                for (const page of chapter.pages) {
                                  const viz = page.visualizations.find((v: any) => v.key === vizKey);
                                  if (viz) {
                                    selectedViz = {
                                      visualizationKey: vizKey,
                                      name: viz.name
                                    };
                                    break;
                                  }
                                }
                                setSelectedVisualization(selectedViz);
                              }
                            }}
                            disabled={!selectedChapter}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Choose a visualization..." />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectGroup>
                                {selectedChapter && (() => {
                                  const chapter = dashboard.chapters.find((ch: any) => ch.key === selectedChapter);
                                  if (chapter) {
                                    return chapter.pages.flatMap((page: any) =>
                                      page.visualizations.map((viz: any) => (
                                        <SelectItem key={viz.key} value={viz.key}>
                                          {viz.name}
                                        </SelectItem>
                                      ))
                                    );
                                  }
                                  return null;
                                })()}
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                          <p className="mt-2 text-xs text-muted-foreground">
                            {selectedChapter ? 'Select a visualization to view its data' : 'First select a chapter to enable visualization selection'}
                          </p>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Visualization data display */}
                    {selectedVisualization && (
                      <Card className="border-2">
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <CardTitle className="flex items-center gap-2">
                              <FileBarChart className="h-4 w-4 text-brand" />
                              {selectedVisualization.name}
                            </CardTitle>
                            {!isLoadingVizData && visualizationData && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  const dataStr = JSON.stringify(visualizationData, null, 2);
                                  const blob = new Blob([dataStr], { type: 'application/json' });
                                  const url = URL.createObjectURL(blob);
                                  const a = document.createElement('a');
                                  a.href = url;
                                  a.download = `${selectedVisualization.name.replace(/\s+/g, '_')}.json`;
                                  document.body.appendChild(a);
                                  a.click();
                                  document.body.removeChild(a);
                                  URL.revokeObjectURL(url);
                                }}
                                className="text-xs"
                              >
                                Download JSON
                              </Button>
                            )}
                          </div>
                        </CardHeader>
                        <CardContent>
                          {isLoadingVizData ? (
                            <div className="flex flex-col items-center justify-center py-12 text-center">
                              <Loader className="mb-4" />
                              <p className="text-sm text-muted-foreground">Loading visualization data...</p>
                            </div>
                          ) : visualizationData ? (
                            <ScrollArea className="h-[500px] w-full rounded-md border">
                              <pre className="p-4 text-sm bg-muted/50">
                                {JSON.stringify(visualizationData, null, 2)}
                              </pre>
                            </ScrollArea>
                          ) : (
                            <div className="flex flex-col items-center justify-center py-12 text-center">
                              <FileBarChart className="h-12 w-12 text-muted-foreground mb-4" />
                              <p className="text-sm text-muted-foreground">No data available for this visualization</p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <LayoutDashboard className="h-12 w-12 text-muted-foreground mb-4" />
                    <p className="text-muted-foreground mb-2">No visualizations found in this dashboard</p>
                    <p className="text-sm text-muted-foreground">Try selecting a different dashboard or check if this dashboard contains any chapters with visualizations.</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};

export default DashboardDetail;
