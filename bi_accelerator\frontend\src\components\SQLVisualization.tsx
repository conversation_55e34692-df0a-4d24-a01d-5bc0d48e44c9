import React, { use<PERSON><PERSON>back, useMemo, useEffect, useState } from 'react';
import React<PERSON>low, {
  Background,
  Controls,
  MiniMap,
  Node,
  Edge,
  NodeTypes,
  EdgeTypes,
  useNodesState,
  useEdgesState,
  MarkerType,
  Position,
  useReactFlow,
  ReactFlowProvider,
  Handle,
  ConnectionLineType,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { 
  SQLAnalysisTable, 
  SQLAnalysisLink,
  SQLModelStructure
} from '@/services/api';
import { Loader } from '@/components/shared/Loader';
import dagre from 'dagre';

interface TableNodeData {
  label: string;
  type: string;
  fields: Array<{
    name: string;
    type: string;
    description?: string;
  }>;
}

// Custom node component with handles for all four sides to improve edge connections
const TableNode: React.FC<{ data: TableNodeData }> = ({ data }) => {
  // Identify relationship fields (those with _ID in the name or that appear to be keys)
  const relationshipFields = data.fields.filter(field => 
    field.name.includes('_ID') || 
    field.name.endsWith('ID') || 
    field.name.includes('_DATE') || 
    field.name.endsWith('DATE') ||
    field.name.includes('_KEY') || 
    field.name.endsWith('KEY')
  );

  return (
    <>
      <div 
        className={`px-4 py-2 shadow-lg rounded-lg bg-white border-2 border-gray-200 min-w-[220px]`}
      >
        {/* Add handles for each potential relationship field */}
        {relationshipFields.map((field, index) => (
          <React.Fragment key={index}>
            <Handle 
              type="source" 
              position={Position.Right} 
              id={`${field.name}-source`} 
              style={{background: '#3b82f6', top: 40 + (index * 18)}} 
            />
            <Handle 
              type="target" 
              position={Position.Left} 
              id={`${field.name}-target`} 
              style={{background: '#ef4444', top: 40 + (index * 18)}} 
            />
          </React.Fragment>
        ))}
        
        {/* Add default handles on all sides as fallbacks for complex relationships */}
        <Handle type="source" position={Position.Top} id="top-source" style={{background: '#3b82f6'}} />
        <Handle type="target" position={Position.Top} id="top-target" style={{background: '#ef4444'}} />
        <Handle type="source" position={Position.Bottom} id="bottom-source" style={{background: '#3b82f6'}} />
        <Handle type="target" position={Position.Bottom} id="bottom-target" style={{background: '#ef4444'}} />
        
        <div className="flex items-center gap-2 mb-2">
          <div className={`w-3 h-3 rounded-full ${
            data.type === 'fact' ? 'bg-blue-500' :
            data.type === 'dimension' ? 'bg-green-500' :
            data.type === 'lookup' ? 'bg-yellow-500' : 'bg-gray-500'
          }`} />
          <h3 className="font-semibold text-sm">{data.label}</h3>
          <span className="text-xs text-gray-500">({data.type})</span>
        </div>
        <div className="border-t pt-2">
          {data.fields.map((field, index) => (
            <div 
              key={index} 
              className={`text-xs py-1 ${
                field.name.includes('_ID') || 
                field.name.endsWith('ID') || 
                field.name.includes('_DATE') || 
                field.name.endsWith('DATE') || 
                field.name.includes('_KEY') || 
                field.name.endsWith('KEY') 
                  ? 'font-medium text-blue-600' 
                  : ''
              }`}
            >
              <span className="font-medium">{field.name}</span>
              <span className="text-gray-500"> ({field.type})</span>
              {field.description && (
                <p className="text-gray-400 text-[10px]">{field.description}</p>
              )}
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

const nodeTypes: NodeTypes = {
  tableNode: TableNode,
};

// Enhanced graph layout configuration with better spacing
const dagreGraph = new dagre.graphlib.Graph();
dagreGraph.setDefaultEdgeLabel(() => ({}));

const getLayoutedElements = (nodes: Node[], edges: Edge[], direction = 'LR') => {
  const isHorizontal = direction === 'LR';
  dagreGraph.setGraph({ 
    rankdir: direction,
    nodesep: 250,  // Increased spacing between nodes horizontally
    ranksep: 200,  // Increased spacing between ranks (rows/columns)
    edgesep: 100,  // Increased spacing between edges
    ranker: 'network-simplex' // Better for tree-like structures
  });

  // Clear the graph before adding new nodes
  dagreGraph.nodes().forEach(n => dagreGraph.removeNode(n));

  // Add nodes to the graph with more realistic dimensions
  nodes.forEach((node) => {
    // Calculate node size based on number of fields
    const fieldCount = (node.data as TableNodeData).fields.length;
    const height = Math.max(120, 60 + fieldCount * 24); // More space for nodes with more fields
    const width = 250;
    
    dagreGraph.setNode(node.id, { width, height });
  });

  // Add edges to the graph
  edges.forEach((edge) => {
    dagreGraph.setEdge(edge.source, edge.target);
  });

  // Apply the layout
  dagre.layout(dagreGraph);

  // Get the positioned nodes and enhance them with better positioning
  const layoutedNodes = nodes.map((node) => {
    const nodeWithPosition = dagreGraph.node(node.id);
    return {
      ...node,
      position: {
        x: nodeWithPosition.x - nodeWithPosition.width / 2,
        y: nodeWithPosition.y - nodeWithPosition.height / 2,
      },
      // Add extra data to help with edge routing
      data: {
        ...node.data,
        dimensions: {
          width: nodeWithPosition.width,
          height: nodeWithPosition.height
        }
      }
    };
  });

  // Helper function to find field position within a node
  const getFieldPosition = (nodeId: string, fieldName: string) => {
    const node = layoutedNodes.find(n => n.id === nodeId);
    if (!node) return null;
    
    const fields = (node.data as TableNodeData).fields;
    const fieldIndex = fields.findIndex(f => f.name === fieldName);
    if (fieldIndex === -1) return null;
    
    // Calculate approximate position of the field within the node
    return {
      x: node.position.x + (fieldName.includes('_ID') ? 250 : 0), // Right side for source, left side for target
      y: node.position.y + 40 + (fieldIndex * 24), // Approximate y position based on field index
    };
  };

  // Enhance edges with better handle connections based on actual field names
  const layoutedEdges = edges.map(edge => {
    // Try to find the source and target fields from the edge data
    const sourceField = edge.data?.sourceField;
    const targetField = edge.data?.targetField;
    
    // Set source and target handles based on the field names
    let sourceHandle = sourceField ? `${sourceField}-source` : undefined;
    let targetHandle = targetField ? `${targetField}-target` : undefined;
    
    return {
      ...edge,
      sourceHandle,
      targetHandle,
      // Add custom path if we know field positions
      // This won't be in the final edge as ReactFlow will compute the path,
      // but it helps with the mental model
    };
  });

  return { nodes: layoutedNodes, edges: layoutedEdges };
};

// Helper to convert relationship type to symbol
const getRelationshipSymbol = (type: string): string => {
  switch (type.toLowerCase()) {
    case 'one-to-many':
      return '1:∞';
    case 'many-to-one':
      return '∞:1';
    case 'one-to-one':
      return '1:1';
    case 'many-to-many':
      return '∞:∞';
    case 'derived':
      return '⟿';
    default:
      return '→';
  }
};

// Updated interface to support both old and new API formats
interface SQLVisualizationProps {
  // Old API format props (for backward compatibility)
  nodes?: SQLAnalysisTable[];
  links?: SQLAnalysisLink[];
  
  // New API format props
  main_tables?: SQLAnalysisTable[];
  relationships?: SQLAnalysisLink[];
  model_structure?: SQLModelStructure;
}

// Wrapped component that uses React Flow hooks
const ReactFlowWrapper: React.FC<SQLVisualizationProps> = (props) => {
  // Handle both old and new API formats
  const nodes = useMemo(() => 
    props.nodes || props.main_tables || [],
    [props.nodes, props.main_tables]
  );
  
  const links = useMemo(() => 
    props.links || props.relationships || [],
    [props.links, props.relationships]
  );

  // Filter relationships to only include valid tables
  const filteredLinks = useMemo(() => {
    // Get IDs of all tables
    const validNodeIds = new Set(nodes.map(node => node.id));
    
    // Include all relationships between tables that exist in our nodes list
    return links.filter(link => 
      validNodeIds.has(link.source) && 
      validNodeIds.has(link.target)
    );
  }, [nodes, links]);

  // Transform the nodes into ReactFlow format
  const initialNodes: Node[] = useMemo(() => 
    nodes.map((node) => {
      return {
        id: node.id,
        type: 'tableNode',
        data: {
          label: node.name,
          type: node.type,
          fields: node.fields,
        },
        position: { x: 0, y: 0 }, // Initial position will be set by dagre
      };
    }), [nodes]);

  // Transform the links into ReactFlow edges with appropriate styling
  const initialEdges: Edge[] = useMemo(() => 
    filteredLinks.map((link, index) => {
      const isOneToMany = link.relationship === 'one-to-many';
      const isManyToOne = link.relationship === 'many-to-one';
      const isOneToOne = link.relationship === 'one-to-one';
      
      // Use symbols instead of text for relationship type
      const relationshipSymbol = getRelationshipSymbol(link.relationship);
      
      // Handle compound keys (fields separated by commas)
      const sourceFieldDisplay = link.sourceField;
      const targetFieldDisplay = link.targetField;
      
      // Check if this is a compound key relationship
      const isCompoundKey = link.sourceField.includes(',') || link.targetField.includes(',');
      
      // For connection handles, use the first field if it's a compound key
      const primarySourceField = link.sourceField.split(',')[0].trim();
      const primaryTargetField = link.targetField.split(',')[0].trim();
      
      return {
        id: `edge-${index}`,
        source: link.source,
        target: link.target,
        label: `${relationshipSymbol}  ${sourceFieldDisplay} → ${targetFieldDisplay}`,
        type: 'smoothstep',
        animated: false,
        data: {
          sourceField: primarySourceField,
          targetField: primaryTargetField,
          relationship: link.relationship,
          description: link.description,
          isCompoundKey
        },
        style: { 
          stroke: isOneToMany ? '#3b82f6' : 
                  isManyToOne ? '#ef4444' : 
                  isOneToOne ? '#10b981' : '#64748b',
          strokeWidth: 2,
        },
        labelStyle: { 
          fill: isOneToMany ? '#3b82f6' : 
                isManyToOne ? '#ef4444' : 
                isOneToOne ? '#10b981' : '#64748b',
          fontSize: 13,
          fontWeight: 600,
          fontFamily: 'monospace',
        },
        markerEnd: {
          type: MarkerType.ArrowClosed,
          color: isOneToMany ? '#3b82f6' : 
                 isManyToOne ? '#ef4444' : 
                 isOneToOne ? '#10b981' : '#64748b',
        },
        // Use specific handles for simple relationships, default handles for compound keys
        sourceHandle: isCompoundKey ? "top-source" : `${primarySourceField}-source`,
        targetHandle: isCompoundKey ? "bottom-target" : `${primaryTargetField}-target`,
      };
    }), [filteredLinks]);

  // Apply layout to nodes and edges - horizontal layout works better for data models
  const { nodes: layoutedNodes, edges: layoutedEdges } = useMemo(() => 
    getLayoutedElements(initialNodes, initialEdges, 'LR'),
    [initialNodes, initialEdges]
  );

  const [flowNodes, setNodes, onNodesChange] = useNodesState(layoutedNodes);
  const [flowEdges, setEdges, onEdgesChange] = useEdgesState(layoutedEdges);
  const reactFlowInstance = useReactFlow();

  // Use effect to fit view when component mounts, but without loading state
  useEffect(() => {
    setTimeout(() => {
      reactFlowInstance.fitView({ padding: 0.2 });
    }, 100);
  }, [reactFlowInstance]);

  return (
    <ReactFlow
      nodes={flowNodes}
      edges={flowEdges}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
      nodeTypes={nodeTypes}
      fitView
      minZoom={0.1}
      maxZoom={1.5}
      defaultViewport={{ x: 0, y: 0, zoom: 0.8 }}
      attributionPosition="bottom-right"
      className="bg-slate-50"
      // Better edge routing options
      connectionLineType={ConnectionLineType.SmoothStep}
      defaultEdgeOptions={{
        type: 'smoothstep',
        animated: false
      }}
    >
      <Background color="#94a3b8" gap={16} size={1} />
      <Controls />
      <MiniMap 
        nodeColor={(node) => {
          const data = node.data as TableNodeData;
          return data.type === 'fact' ? '#3b82f6' :
                 data.type === 'dimension' ? '#22c55e' :
                 data.type === 'lookup' ? '#eab308' : '#9ca3af';
        }}
        maskColor="#ffffff50"
        className="bg-white rounded-lg shadow-lg"
      />
    </ReactFlow>
  );
};

// Main component that wraps the ReactFlow component with a provider
const SQLVisualization: React.FC<SQLVisualizationProps> = (props) => {
  // Display model structure info in the legend if available
  const modelStructure = props.model_structure;
  
  return (
    <div className="w-full h-[800px] border rounded-lg relative">
      <ReactFlowProvider>
        <ReactFlowWrapper {...props} />
      </ReactFlowProvider>
      
      {/* Legend */}
      <div className="absolute bottom-4 left-4 bg-white p-4 rounded-lg shadow-lg z-10">
        <h4 className="font-medium text-sm mb-2">Table & Relationship Types</h4>
        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-blue-500" />
              <span className="text-xs">Fact Table</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-green-500" />
              <span className="text-xs">Dimension Table</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-yellow-500" />
              <span className="text-xs">Lookup Table</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-xs font-mono font-bold text-blue-500">1:∞</span>
              <span className="text-xs">One-to-Many</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs font-mono font-bold text-red-500">∞:1</span>
              <span className="text-xs">Many-to-One</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs font-mono font-bold text-green-500">1:1</span>
              <span className="text-xs">One-to-One</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs font-mono font-bold">*,*,*</span>
              <span className="text-xs">Compound Key</span>
            </div>
          </div>
        </div>
        
        {/* Show model type if available */}
        {modelStructure && (
          <div className="mt-3 pt-3 border-t">
            <p className="text-xs font-medium">
              Model Type: <span className="text-blue-600">{modelStructure.model_type}</span>
            </p>
            {modelStructure.description && (
              <p className="text-xs mt-1 text-gray-500">{modelStructure.description}</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SQLVisualization; 