from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Body, Query
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional, AsyncIterator
import os
import json
import logging
import pyodbc
import requests
import decimal
import datetime
import uuid
import asyncio
from collections import deque
from dotenv import load_dotenv
import hmac
import hashlib
import base64
import pandas as pd
import io
import re
 
# MicroStrategy Imports
from mstrio.connection import Connection
from mstrio.project_objects import Report, list_reports
 
# OpenAI Imports
import openai
 
# For Power BI authentication (Azure AD)
from azure.identity import ClientSecretCredential
 
# Import MicroStrategy connection utility
from routers.utils import get_connection
 
# Initialize router
router = APIRouter(prefix="/api/power-bi", tags=["power_bi"])
 
# Configure logging
logger = logging.getLogger("bi_accelerator")
 
# Load environment variables
load_dotenv()
 
# Task store for tracking dataset creation tasks
task_store = {}
 
# Define Pydantic models for requests and responses
class TableColumn(BaseModel):
    name: str
    dataType: str
 
class Table(BaseModel):
    name: str
    columns: List[TableColumn]
 
class Relationship(BaseModel):
    fromTable: str
    fromColumn: str
    toTable: str
    toColumn: str
 
class Schema(BaseModel):
    tables: List[Table]
    relationships: Optional[List[Relationship]] = []
 
class CreateDatasetRequest(BaseModel):
    report_id: str = Field(..., description="MicroStrategy Report ID")
    dataset_name: str = Field(..., description="Name for the Power BI dataset")
    include_data: bool = Field(True, description="Whether to fetch and push data to Power BI")
    project_id: Optional[str] = Field(None, description="MicroStrategy Project ID")
    workspace_id: str = Field(..., description="Power BI Workspace ID")
   
    class Config:
        json_schema_extra = {
            "example": {
                "report_id": "A428F8524EAD4DF409AAD49981473140",
                "dataset_name": "Sales Analysis Dataset",
                "include_data": True,
                "project_id": "B19DEDCC11D4E0EFC000EB9495D0F44F",
                "workspace_id": "12345678-1234-1234-1234-123456789012"
            }
        }
 
class DatasetCreationResponse(BaseModel):
    task_id: str
    dataset_name: str
    status: str
    message: str
 
class DownloadDaxExpressionsRequest(BaseModel):
    project_id: str = Field(..., description="MicroStrategy Project ID")
    include_metrics: bool = Field(True, description="Include metric DAX expressions")
    include_attributes: bool = Field(True, description="Include attribute DAX expressions")
 
# Helper Functions
def connect_to_microstrategy(project_id: str = None):
    """Establish connection to MicroStrategy using credentials utility"""
    try:
        # Use the get_connection utility from utils.py
        mstr_conn = get_connection(project_id)
        logger.info("Connected to MicroStrategy Project successfully!")
        return mstr_conn
    except Exception as e:
        logger.error(f"Error connecting to MicroStrategy: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to connect to MicroStrategy: {str(e)}")
 
def extract_schema_from_sql(sql_text: str) -> dict:
    """Extract schema information from SQL using OpenAI"""
    try:
        openai.api_key = os.getenv("OPENAI_API_KEY")
       
        prompt = f"""
Given the following SQL query from MicroStrategy, extract and output a JSON with the following structure:
{{
  "tables": [
      {{
         "name": "<table_name>",
         "columns": [
              {{"name": "<column_name>", "dataType": "<PowerBI-compatible data type>"}}
         ]
      }}
  ],
  "relationships": [
      {{
         "name": "<relationship_name_for_power_bi(need to be unique)>",
         "fromTable": "<table_name>",
         "fromColumn": "<column_name>",
         "toTable": "<table_name>",
         "toColumn": "<column_name>",
         "crossFilteringBehavior": "<OneDirection | BothDirections | Automatic>",
      }}
  ]
}}
If no relationships exist, output an empty array for "relationships". Use data types such as "Int64", "string", "double", "DateTime", or "bool".
If a table name starts with '#' (indicating it is a temporary table), ignore it and do not include it in the "tables" or "relationships" section. 
Instead, when you encounter a temporary table, look at its source query (the INSERT INTO or SELECT statement that populates it) and extract the base tables from the FROM and JOIN clauses of that source query
Here is the SQL:
\"\"\"{sql_text}\"\"\"
Output only valid JSON with no additional text or markdown.
"""
        response = openai.chat.completions.create(
            model="o3-mini",
            messages=[
                {"role": "system", "content": "You are a helpful assistant that extracts schema information from SQL queries."},
                {"role": "user", "content": prompt}
            ],
            # temperature=0
        )
        schema_str = response.choices[0].message.content.strip()
       
        # Remove markdown code fences if present
        if schema_str.startswith("```"):
            lines = schema_str.splitlines()
            if lines[0].startswith("```"):
                lines = lines[1:]
            if lines and lines[-1].startswith("```"):
                lines = lines[:-1]
            schema_str = "\n".join(lines).strip()
           
        schema_json = json.loads(schema_str)
        return schema_json
    except Exception as e:
        logger.error(f"Error extracting schema from SQL via OpenAI: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error extracting schema from SQL: {str(e)}")
 
def connect_to_sql_server():
    """Connect to SQL Server database"""
    try:
        db_server = os.getenv("SERVER")
        db_name = os.getenv("DATABASE")
        db_user = os.getenv("DB_USER") or os.getenv("USERNAME")
        db_pass = os.getenv("PASS")
        conn_str = f"Driver={{ODBC Driver 17 for SQL Server}};Server={db_server};Database={db_name};UID={db_user};PWD=*********;"
       
        sql_conn = pyodbc.connect(conn_str)
        logger.info("Connected to SQL Server successfully")
        return sql_conn
    except Exception as e:
        logger.error(f"Error connecting to SQL Server: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to connect to SQL Server: {str(e)}")
 
def get_sql_column_schema(connection, table_name):
    """
    Get column schema for a table from SQL Server's INFORMATION_SCHEMA.
    """
    try:
        query = f"""
            SELECT COLUMN_NAME, DATA_TYPE
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = '{table_name}'
            ORDER BY ORDINAL_POSITION
        """
        cursor = connection.cursor()
        cursor.execute(query)
        return cursor.fetchall()
    except Exception as e:
        logger.error(f"Error fetching schema for table {table_name}: {str(e)}")
        return []
 
def map_sql_type_to_powerbi_type(sql_type):
    """
    Map common SQL Server data types to Power BI data types.
    """
    sql_type_lower = sql_type.lower()
    if "int" in sql_type_lower:
        return "Int64"
    elif "char" in sql_type_lower or "text" in sql_type_lower or "varchar" in sql_type_lower:
        return "string"
    elif "decimal" in sql_type_lower or "numeric" in sql_type_lower or "float" in sql_type_lower or "real" in sql_type_lower:
        return "double"
    elif "date" in sql_type_lower or "time" in sql_type_lower:
        return "DateTime"
    elif "bit" in sql_type_lower or "bool" in sql_type_lower:
        return "bool"
    else:
        return "string"
 
def merge_schema_with_sqlserver(mstr_schema: dict, sql_conn) -> dict:
    """
    Merge MicroStrategy schema with SQL Server metadata
    """
    merged_tables = []
    print("here")
    print(mstr_schema)

    for table in mstr_schema.get("tables", []):
        table_name = table.get("name")
        # First, try to fetch using the table name as-is.
        columns_schema = get_sql_column_schema(sql_conn, table_name)
        # If not found, try with "dbo." prefix removed
        if not columns_schema:
            if "." in table_name:
                table_name_no_schema = table_name.split(".")[-1]
                columns_schema = get_sql_column_schema(sql_conn, table_name_no_schema)
            else:
                logger.warning(f"No columns found for table '{table_name}' in SQL Server.")
                continue
               
        # Build columns definition from SQL Server metadata
        columns_def = []
        for col_name, sql_type in columns_schema:
            pbi_type = map_sql_type_to_powerbi_type(sql_type)
            columns_def.append({"name": col_name, "dataType": pbi_type})
           
        merged_tables.append({"name": table_name.split(".",1)[1], "columns": columns_def})
   
    # Include relationships in the schema
    new_schema = {
        "tables": merged_tables,
        "relationships": mstr_schema.get("relationships", [])
    }
    print("here2")
    print(new_schema)
    return new_schema
 
def get_power_bi_token():
    """Get authentication token for Power BI API"""
    try:
        tenant_id = os.getenv("TENANT_ID")
        client_id = os.getenv("CLIENT_ID")
        client_secret = os.getenv("CLIENT_SECRET")
       
        credential = ClientSecretCredential(
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret
        )
        token = credential.get_token("https://analysis.windows.net/powerbi/api/.default").token
        return token
    except Exception as e:
        logger.error(f"Error getting Power BI token: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to authenticate with Power BI: {str(e)}")
 
def generate_unique_relationship_name(from_table, from_column, to_table, to_column):
    """
    Generate a unique relationship name using HMAC-SHA256
    """
    key = "powerbi_relationship_key"
    message = f"{from_table}:{from_column}:{to_table}:{to_column}".encode()
   
    # Create HMAC using SHA256
    h = hmac.new(key.encode(), message, hashlib.sha256)
   
    # Generate a Base64 encoded string and make it URL-safe
    signature = base64.b64encode(h.digest()).decode('utf-8')
    signature = signature.replace('+', '-').replace('/', '_').replace('=', '')
   
    # Create a readable prefix with a portion of the hash
    return f"Rel_{from_table}_{to_table}_{signature[:8]}"
 
def create_power_bi_dataset(dataset_name: str, schema: dict, workspace_id: str):
    """Create a Power BI push dataset"""
    try:
        print(schema)
        token = get_power_bi_token()
       
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}"
        }
        
        # Create dataset definition with tables
        dataset_def = {
            "name": dataset_name,
            "defaultMode": "Push",
            #"tables": schema.get("tables", [])
            "tables" :[{"name": table.get("name").split(".")[-1],  
                        "columns": table.get("columns", [])} for table in schema.get("tables", [])]
        }
        # Add relationships if they exist
        relationships = schema.get("relationships", [])
        if relationships:
            dataset_def["relationships"] = []
            # Track relationships to avoid duplicates - more robust tracking
            relationship_tracker = {}  # Use dict to track by table pairs
           
            # First log all relationships for debugging
            logger.info(f"Processing {len(relationships)} relationships from schema")
            for i, rel in enumerate(relationships):
                logger.info(f"Relationship {i+1}: {rel.get('fromTable')}.{rel.get('fromColumn')} -> {rel.get('toTable')}.{rel.get('toColumn')}")
           
            for rel in relationships:
                from_table = rel.get("fromTable").split(".")[-1].strip()
                from_column = rel.get("fromColumn")
                to_table = rel.get("toTable").split(".")[-1].strip()
                to_column = rel.get("toColumn")
               
                # Skip invalid relationships
                if not from_table or not from_column or not to_table or not to_column:
                    logger.warning(f"Skipping invalid relationship with missing attributes: {rel}")
                    continue
                   
                # Create a normalized key for table pair (sort to ensure same key regardless of direction)
                table_pair = tuple(sorted([from_table, to_table]))
               
                # Check if we've already added a relationship between these tables
                if table_pair in relationship_tracker:
                    # Check if this specific relationship already exists
                    for existing_rel in relationship_tracker[table_pair]:
                        if ((existing_rel["fromTable"] == from_table and
                             existing_rel["fromColumn"] == from_column and
                             existing_rel["toTable"] == to_table and
                             existing_rel["toColumn"] == to_column) or
                            (existing_rel["fromTable"] == to_table and
                             existing_rel["fromColumn"] == to_column and
                             existing_rel["toTable"] == from_table and
                             existing_rel["toColumn"] == from_column)):
                            logger.warning(f"Skipping duplicate relationship: {from_table}.{from_column} -> {to_table}.{to_column}")
                            break
                    else:
                        # If no exact match, we'll only allow ONE relationship between any two tables
                        # This is the safest approach to prevent ambiguous paths
                        logger.warning(f"Multiple relationships between {from_table} and {to_table} found. Using only the first one.")
                        continue
                    continue
                else:
                    # Initialize empty list for this table pair
                    relationship_tracker[table_pair] = []
               
                # Generate a unique name for this relationship
                rel_name = generate_unique_relationship_name(from_table, from_column, to_table, to_column)
               
                # Create the relationship definition
                relationship_def = {
                    "name": rel_name,
                    "fromTable": from_table,
                    "fromColumn": from_column,
                    "toTable": to_table,
                    "toColumn": to_column,
                    "crossFilteringBehavior": rel.get("crossFilteringBehavior", "automatic")
                }
               
                # Add to dataset definition and tracker
                dataset_def["relationships"].append(relationship_def)
                # print(dataset_def)
                relationship_tracker[table_pair].append(relationship_def)
               
                logger.info(f"Added relationship: {rel_name} - {from_table}.{from_column} -> {to_table}.{to_column}")
           
            logger.info(f"Final dataset has {len(dataset_def['relationships'])} relationships between {len(relationship_tracker)} table pairs")
           
            # Double-check for any potential ambiguous paths (additional validation)
            relationship_counts = {}
            for rel in dataset_def["relationships"]:
                pair = (rel["fromTable"], rel["toTable"])
                relationship_counts[pair] = relationship_counts.get(pair, 0) + 1
               
            for pair, count in relationship_counts.items():
                if count > 1:
                    logger.warning(f"Potential ambiguous path warning: {count} relationships between {pair[0]} and {pair[1]}")
       
        dataset_url = f"https://api.powerbi.com/v1.0/myorg/groups/{workspace_id}/datasets?defaultRetentionPolicy=basicFIFO"
        resp_create = requests.post(dataset_url, headers=headers, json=dataset_def)
       
        if resp_create.status_code not in [200, 201]:
            logger.error(f"Failed to create dataset. Status: {resp_create.status_code}, Response: {resp_create.text}")
            raise HTTPException(status_code=resp_create.status_code,
                               detail=f"Power BI API error: {resp_create.text}")
       
        return resp_create.json()
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating Power BI dataset: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create Power BI dataset: {str(e)}")
 
def fetch_table_data(sql_conn, table_name: str) -> list:
    """
    Fetch all rows from a SQL Server table
    """
    try:
        cursor = sql_conn.cursor()
        # Lookup the schema name for the given table
        if '.' in table_name:
            schema,tbl = table_name.split('.', 1)
        else:
            schema, tbl = None, table_name
        

        if schema is None:
            cursor.execute(f"SELECT TABLE_SCHEMA FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{table_name}'")
            row = cursor.fetchone()
            schema = row[0] if row else "dbo"
        else:
            schema = schema.strip()
        
        full_table_name = f"[{schema}].[{tbl}]"
       
        query = f"SELECT * FROM {full_table_name}"
        cursor.execute(query)
        rows = cursor.fetchall()
        col_names = [desc[0] for desc in cursor.description]
        data = [dict(zip(col_names, row)) for row in rows]
        return data
    except Exception as e:
        logger.error(f"Error fetching data from table {table_name}: {str(e)}")
        return []
 
def convert_data_types(data_rows):
    """
    Convert non-JSON serializable types to JSON-serializable ones.
    """
    new_rows = []
    for row in data_rows:
        converted_row = {}
        for k, v in row.items():
            if v is None:
                converted_row[k] = None
            elif isinstance(v, decimal.Decimal):
                converted_row[k] = float(v)
            elif isinstance(v, (datetime.date, datetime.datetime)):
                converted_row[k] = v.isoformat()
            elif isinstance(v, (bytes, bytearray)):
                converted_row[k] = v.decode('utf-8', errors='replace')
            else:
                try:
                    json.dumps({k: v})
                    converted_row[k] = v
                except (TypeError, OverflowError):
                    converted_row[k] = str(v)
        new_rows.append(converted_row)
    return new_rows
 
def push_data_to_power_bi(dataset_id: str, table_name: str, data_rows: list, workspace_id: str):
    """
    Push data rows to a Power BI dataset table
    """
    try:
        token = get_power_bi_token()
       
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}"
        }
       
        data_rows = convert_data_types(data_rows)
        rows_url = f"https://api.powerbi.com/v1.0/myorg/groups/{workspace_id}/datasets/{dataset_id}/tables/{table_name}/rows"
        payload = {"rows": data_rows}
       
        resp_push = requests.post(rows_url, headers=headers, json=payload)
       
        if resp_push.status_code not in [200, 201, 202]:
            logger.error(f"Failed to push data for table {table_name}. Status: {resp_push.status_code}, Response: {resp_push.text}")
            return {"status": "error", "message": f"Failed to push data: {resp_push.text}"}
       
        return {"status": "success", "message": f"Pushed {len(data_rows)} rows to table {table_name}"}
    except Exception as e:
        logger.error(f"Error pushing data to Power BI for table {table_name}: {str(e)}")
        return {"status": "error", "message": f"Error: {str(e)}"}
 
# SSE helper for sending updates
async def add_update(task_id: str, update: dict):
    """Add an update to the task's message queue"""
    if task_id in task_store:
        # Ensure update has a timestamp
        if "timestamp" not in update:
            update["timestamp"] = datetime.datetime.now().isoformat()
            
        # Add the update to the task's updates
        task_store[task_id]["updates"].append(update)
        
        # If this is a completion update, store the dataset_id
        if update.get("type") == "status" and update.get("status") == "completed" and "dataset_id" in update:
            task_store[task_id]["dataset_id"] = update["dataset_id"]
            
        # Signal any waiting clients that new data is available
        for event in task_store[task_id]["events"]:
            event.set()
            
        # Add a small delay to allow streaming to occur
        await asyncio.sleep(0.1)
        
        # Log important updates for debugging
        if update.get("type") == "status":
            logger.info(f"Task {task_id} status update: {update.get('status')} - {update.get('message')}")
 
def extract_executable_sql(sql_query: str) -> str:
    """
    Clean MicroStrategy SQL by removing metadata annotations that aren't valid SQL syntax.
    """
    # Look for MicroStrategy metadata markers
    metadata_markers = [
        "[Analytical engine calculation steps:",
        "Analytical engine calculation steps:",
        "-- Analytical engine"
    ]
    
    # Find the earliest occurrence of any metadata marker
    earliest_pos = len(sql_query)
    for marker in metadata_markers:
        pos = sql_query.find(marker)
        if pos != -1 and pos < earliest_pos:
            earliest_pos = pos
    
    # If we found a marker, truncate the SQL at that point
    if earliest_pos < len(sql_query):
        clean_sql = sql_query[:earliest_pos].strip()
    else:
        clean_sql = sql_query
    
    return clean_sql

def clean_column_name(col_name: str) -> str:
    """
    Clean MicroStrategy column names by removing XML-like tags and other special formatting.
    """
    # Remove XML-like tags (e.g., <pi>From_Date1</pi>)
    cleaned = re.sub(r'<[^>]+>', '', col_name)
    
    # Remove square brackets if present
    cleaned = cleaned.replace('[', '').replace(']', '')
    
    # Remove any leading/trailing whitespace
    cleaned = cleaned.strip()
    
    return cleaned

def extract_aggregate_expressions(sql_query: str) -> list:
    """
    Extract aggregate function expressions from the SQL query to use for column naming.
    This is a simple extraction - it looks for common aggregate functions in the SELECT clause.
    """
    # Common SQL aggregate functions
    agg_functions = ["SUM", "AVG", "COUNT", "MAX", "MIN"]
    
    # Find the SELECT clause - everything between SELECT and the first FROM
    select_match = re.search(r'SELECT\s+(.*?)\s+FROM', sql_query, re.IGNORECASE | re.DOTALL)
    if not select_match:
        return []
    
    select_clause = select_match.group(1)
    
    # Extract aggregate expressions
    agg_expressions = []
    for agg_func in agg_functions:
        # Look for patterns like: SUM([table].[column]) or SUM(column)
        matches = re.finditer(rf'{agg_func}\s*\(\s*(?:\[\w+\])?\s*\.?\s*(?:\[)?(\w+)(?:\])?\s*\)', 
                             select_clause, re.IGNORECASE)
        for match in matches:
            col_name = match.group(1)
            # Just use the column name directly without prefix
            agg_expressions.append(f"{col_name}")
    
    return agg_expressions

def fetch_data_from_free_form_sql(sql_query: str, connection) -> tuple:
    """
    Execute the given free-form SQL query on the SQL Server, return (columns, rows, column_defs).
    columns: list of column names
    rows: list of row dicts
    column_defs: list of column definition dicts for Power BI schema
    """
    try:
        clean_sql = extract_executable_sql(sql_query)
        logger.info(f"Executing cleaned SQL query: {clean_sql[:100]}...")
        
        cursor = connection.cursor()
        cursor.execute(clean_sql)
        rows = cursor.fetchall()    
        
        # Get column names and clean them
        original_col_names = [desc[0] for desc in cursor.description]
        clean_col_names = [clean_column_name(col) for col in original_col_names]
        
        # Fix empty column names (typically aggregated columns)
        # Extract aggregate expressions from SQL for naming
        agg_expressions = extract_aggregate_expressions(clean_sql)
        
        # Replace empty column names with meaningful names based on the aggregation
        for i, name in enumerate(clean_col_names):
            if not name:
                # If we have a corresponding aggregate expression, use it; otherwise use a generic name
                if i - len(clean_col_names) + len(agg_expressions) >= 0:
                    agg_idx = i - len(clean_col_names) + len(agg_expressions)
                    if agg_idx < len(agg_expressions):
                        # Use just the column name without "Agg_" prefix
                        clean_col_names[i] = agg_expressions[agg_idx]
                    else:
                        clean_col_names[i] = f"Column_{i+1}"
                else:
                    clean_col_names[i] = f"Column_{i+1}"
        
        # Map original column names to cleaned names for debugging
        col_mapping = dict(zip(original_col_names, clean_col_names))
        logger.info(f"Column name cleaning: {json.dumps(col_mapping)}")
        
        # Create row dicts with clean column names
        data = []
        for row in rows:
            row_dict = {}
            for i, val in enumerate(row):
                row_dict[clean_col_names[i]] = val
            data.append(row_dict)
        
        # Determine data types from the first non-null value in each column
        column_defs = []
        for col_name in clean_col_names:
            # Default type
            data_type = "string"
            for row in data:
                if row[col_name] is not None:
                    val = row[col_name]
                    if isinstance(val, int):
                        data_type = "Int64"
                    elif isinstance(val, float) or isinstance(val, decimal.Decimal):
                        data_type = "double"
                    elif isinstance(val, (datetime.date, datetime.datetime)):
                        data_type = "DateTime"
                    elif isinstance(val, bool):
                        data_type = "bool"
                    break
            column_defs.append({"name": col_name, "dataType": data_type})
            
        return clean_col_names, data, column_defs
    except Exception as e:
        logger.error(f"Error executing free-form SQL: {str(e)}")
        return [], [], []

# Background task for data transfer
async def transfer_data_to_power_bi(task_id: str, report_id: str, dataset_name: str, include_data: bool, project_id: str = None, workspace_id: str = None):
    """
    Background task to handle the complete data transfer process with SSE updates
    """
    try:
        # Use provided workspace_id or fall back to environment variable
        effective_workspace_id = workspace_id or os.getenv("PPU_WORKSPACE_ID")
        
        # Initialize task status
        await add_update(task_id, {
            "type": "status",
            "status": "started",
            "message": f"Starting dataset creation for '{dataset_name}'",
            "timestamp": datetime.datetime.now().isoformat()
        })
       
        # Step 1: Connect to MicroStrategy with optional project_id
        await add_update(task_id, {
            "type": "info",
            "message": "Connecting to MicroStrategy...",
            "timestamp": datetime.datetime.now().isoformat()
        })
        mstr_conn = connect_to_microstrategy(project_id)
       
        # Step 2: Load the selected report and fetch its SQL view
        await add_update(task_id, {
            "type": "info",
            "message": f"Loading MicroStrategy report ID: {report_id}",
            "timestamp": datetime.datetime.now().isoformat()
        })
        report_obj = Report(connection=mstr_conn, id=report_id)
        mstr_sql = report_obj.sql
        
        # Check if this is a freeform SQL report
        is_freeform_sql = str(report_obj.ext_type) == "ExtendedType.CUSTOM_SQL_FREE_FORM"
        
        report_type = "Free Form SQL Report" if is_freeform_sql else "Normal Report"
        await add_update(task_id, {
            "type": "info",
            "message": f"Detected report type: {report_type}",
            "timestamp": datetime.datetime.now().isoformat()
        })
        
        await add_update(task_id, {
            "type": "success",
            "message": f"Fetched SQL for report: {report_id}",
            "timestamp": datetime.datetime.now().isoformat()
        })
        
        # Step 3: Connect to SQL Server
        await add_update(task_id, {
            "type": "info",
            "message": "Connecting to SQL Server...",
            "timestamp": datetime.datetime.now().isoformat()
        })
        sql_conn = connect_to_sql_server()
        
        dataset_id = None
        
        # Handle different process flows based on report type
        if is_freeform_sql:
            # FREEFORM SQL REPORT FLOW
            await add_update(task_id, {
                "type": "info",
                "message": "Processing as Free Form SQL report...",
                "timestamp": datetime.datetime.now().isoformat()
            })
            
            # Execute the free-form SQL and get results
            await add_update(task_id, {
                "type": "info",
                "message": "Executing free-form SQL query to get schema...",
                "timestamp": datetime.datetime.now().isoformat()
            })
            
            col_names, data_rows, column_defs = fetch_data_from_free_form_sql(mstr_sql, sql_conn)
            
            if not col_names or not column_defs:
                await add_update(task_id, {
                    "type": "error",
                    "message": "Failed to extract column information from free-form SQL",
                    "timestamp": datetime.datetime.now().isoformat()
                })
                raise Exception("Failed to extract column information from free-form SQL")
            
            # Create a sanitized table name from the report name
            single_table_name = report_obj.name.replace(" ", "_").replace("-", "_")
            # Remove any other special characters
            single_table_name = re.sub(r'[^a-zA-Z0-9_]', '', single_table_name)
            # print(f"placeholder {single_table_name}")
            await add_update(task_id, {
                "type": "success",
                "message": f"Extracted schema with {len(column_defs)} columns",
                "timestamp": datetime.datetime.now().isoformat()
            })
            
            # Create dataset definition
            # dataset_def = {
            #     "name": dataset_name,
            #     "defaultMode": "Push",
            #     "tables": [
            #         {
            #             "name": single_table_name,
            #             "columns": column_defs
            #         }
            #     ]
            # }
            
            dataset_def = {
            "name": dataset_name,
            "defaultMode": "Push",
            "tables": [
                {
                    "name": single_table_name,
                    "columns": column_defs
                }
            ],
            "datasources": [
                {
                    "connectionDetails": {
                        "server": "dfztrainingdb",         # Replace with actual server
                        "database": "dfztraining",     # Replace with actual DB
                        "kind": "SQL",
                        "path": "dfztraining.database.windows.net"
                    },
                    "datasourceType": "SQL",
                    "gatewayId": "d7806a6d-4737-491e-9605-35e02d65b7e0",
                    "datasourceId": "0e9c6e99-ff91-4efe-948c-ae15110a44ec"
                }
            ]
        }



            # Create dataset in Power BI
            await add_update(task_id, {
                "type": "info",
                "message": f"Creating Power BI dataset: '{dataset_name}'...",
                "timestamp": datetime.datetime.now().isoformat()
            })
            
            # Create the dataset
            token = get_power_bi_token()
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }
            
            dataset_url = f"https://api.powerbi.com/v1.0/myorg/groups/{effective_workspace_id}/datasets?defaultRetentionPolicy=basicFIFO"
            resp_create = requests.post(dataset_url, headers=headers, json=dataset_def)
            
            if resp_create.status_code not in [200, 201]:
                logger.error(f"Failed to create dataset. Status: {resp_create.status_code}, Response: {resp_create.text}")
                await add_update(task_id, {
                    "type": "error",
                    "message": f"Failed to create Power BI dataset: {resp_create.text}",
                    "timestamp": datetime.datetime.now().isoformat()
                })
                raise HTTPException(status_code=resp_create.status_code,
                                   detail=f"Power BI API error: {resp_create.text}")
            
            dataset_response = resp_create.json()
            dataset_id = dataset_response.get("id")
            
            await add_update(task_id, {
                "type": "success",
                "message": f"Created Power BI dataset with ID: {dataset_id}",
                "timestamp": datetime.datetime.now().isoformat()
            })
            
            # Push data if requested
            if include_data and data_rows:
                await add_update(task_id, {
                    "type": "info",
                    "message": f"Pushing {len(data_rows)} rows to dataset...",
                    "timestamp": datetime.datetime.now().isoformat()
                })
                
                # Convert data types for JSON serialization
                data_rows = convert_data_types(data_rows)
                
                # Push data to Power BI
                rows_url = f"https://api.powerbi.com/v1.0/myorg/groups/{effective_workspace_id}/datasets/{dataset_id}/tables/{single_table_name}/rows"
                payload = {"rows": data_rows}
                
                resp_push = requests.post(rows_url, headers=headers, json=payload)
                
                if resp_push.status_code not in [200, 201, 202]:
                    logger.error(f"Failed to push data for table {single_table_name}. Status: {resp_push.status_code}, Response: {resp_push.text}")
                    await add_update(task_id, {
                        "type": "error",
                        "message": f"Failed to push data: {resp_push.text}",
                        "timestamp": datetime.datetime.now().isoformat()
                    })
                else:
                    await add_update(task_id, {
                        "type": "success",
                        "message": f"Successfully pushed {len(data_rows)} rows to dataset",
                        "timestamp": datetime.datetime.now().isoformat()
                    })
            
        else:
            # NORMAL REPORT FLOW - Existing code
            # Step 3: Extract schema from SQL
            await add_update(task_id, {
                "type": "info",
                "message": "Extracting schema from SQL query...",
                "timestamp": datetime.datetime.now().isoformat()
            })
            mstr_schema = extract_schema_from_sql(mstr_sql)
            await add_update(task_id, {
                "type": "success",
                "message": f"Extracted schema from SQL with {len(mstr_schema.get('tables', []))} tables",
                "timestamp": datetime.datetime.now().isoformat()
            })
       
            # Step 5: Merge schema with SQL Server metadata
            await add_update(task_id, {
                "type": "info",
                "message": "Merging schema with SQL Server metadata...",
                "timestamp": datetime.datetime.now().isoformat()
            })
            merged_schema = merge_schema_with_sqlserver(mstr_schema, sql_conn)
            await add_update(task_id, {
                "type": "success",
                "message": f"Merged schema with SQL Server metadata. Final schema has {len(merged_schema.get('tables', []))} tables",
                "timestamp": datetime.datetime.now().isoformat()
            })
       
            # Step 6: Create dataset in Power BI
            await add_update(task_id, {
                "type": "info",
                "message": f"Creating Power BI dataset: '{dataset_name}'...",
                "timestamp": datetime.datetime.now().isoformat()
            })
            dataset_response = create_power_bi_dataset(dataset_name, merged_schema, effective_workspace_id)
            dataset_id = dataset_response.get("id")
            await add_update(task_id, {
                "type": "success",
                "message": f"Created Power BI dataset with ID: {dataset_id}",
                "timestamp": datetime.datetime.now().isoformat()
            })
               
            # Push data if requested
            results = {"dataset_id": dataset_id, "tables": []}
       
            if include_data:
                await add_update(task_id, {
                    "type": "info",
                    "message": f"Starting data transfer for {len(merged_schema.get('tables', []))} tables",
                    "timestamp": datetime.datetime.now().isoformat()
                })
               
                for i, table in enumerate(merged_schema.get("tables", []), 1):
                    table_name = table.get("name")
                    await add_update(task_id, {
                        "type": "info",
                        "message": f"Processing table {i}/{len(merged_schema.get('tables', []))}: {table_name}",
                        "timestamp": datetime.datetime.now().isoformat()
                    })
                   
                    # Fetch data
                    await add_update(task_id, {
                        "type": "info",
                        "message": f"Fetching data for table: {table_name}",
                        "timestamp": datetime.datetime.now().isoformat()
                    })
                    data_rows = fetch_table_data(sql_conn, table_name)
                   
                    if data_rows:
                        await add_update(task_id, {
                            "type": "success",
                            "message": f"Retrieved {len(data_rows)} rows from {table_name}",
                            "timestamp": datetime.datetime.now().isoformat()
                        })
                       
                        # Push to Power BI
                        await add_update(task_id, {
                            "type": "info",
                            "message": f"Pushing data to Power BI for table: {table_name}",
                            "timestamp": datetime.datetime.now().isoformat()
                        })
                        push_result = push_data_to_power_bi(dataset_id, table_name, data_rows, effective_workspace_id)
                       
                        if push_result.get("status") == "success":
                            await add_update(task_id, {
                                "type": "success",
                                "message": push_result.get("message"),
                                "timestamp": datetime.datetime.now().isoformat()
                            })
                        else:
                            await add_update(task_id, {
                                "type": "error",
                                "message": push_result.get("message"),
                                "timestamp": datetime.datetime.now().isoformat()
                            })
                       
                        results["tables"].append({
                            "name": table_name,
                            "rows_count": len(data_rows),
                            "status": push_result.get("status"),
                            "message": push_result.get("message")
                        })
                    else:
                        await add_update(task_id, {
                            "type": "warning",
                            "message": f"No data found for table: {table_name}",
                            "timestamp": datetime.datetime.now().isoformat()
                        })
                        results["tables"].append({
                            "name": table_name,
                            "rows_count": 0,
                            "status": "warning",
                            "message": "No data found for this table"
                        })
       
        # Final success message - ensure this contains the dataset_id
        final_update = {
            "type": "status",
            "status": "completed",
            "message": f"Data transfer completed for dataset: {dataset_name}",
            "dataset_id": dataset_id,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
        # Explicitly set the dataset_id in the task store
        if task_id in task_store:
            task_store[task_id]["dataset_id"] = dataset_id
            task_store[task_id]["status"] = "completed"
            
        # Send the final update
        await add_update(task_id, final_update)
        
        # Log completion for debugging
        logger.info(f"Data transfer completed for dataset: {dataset_name} with ID: {dataset_id}")
        
        return {"status": "success", "dataset_id": dataset_id}
       
    except Exception as e:
        logger.error(f"Error in data transfer background task: {str(e)}")
        # Send error update
        error_update = {
            "type": "status",
            "status": "error",
            "message": f"Error in data transfer: {str(e)}",
            "timestamp": datetime.datetime.now().isoformat()
        }
        
        # Update task store status
        if task_id in task_store:
            task_store[task_id]["status"] = "error"
            
        # Send the error update
        await add_update(task_id, error_update)
        
        return {"status": "error", "message": str(e)}
 
# Define API Endpoints
@router.post("/create-dataset", response_model=DatasetCreationResponse)
async def create_dataset(
    background_tasks: BackgroundTasks,
    request: CreateDatasetRequest = Body(...)
):
    """
    Create a Power BI dataset from a MicroStrategy report
   
    This endpoint:
    1. Extracts SQL schema from a MicroStrategy report
    2. Creates a Power BI push dataset with the schema
    3. Optionally pushes the actual data to the dataset
    4. Returns a task ID that can be used to monitor progress via SSE
    """
    try:
        # Generate a unique task ID
        task_id = str(uuid.uuid4())
       
        # Initialize task in the store
        task_store[task_id] = {
            "dataset_name": request.dataset_name,
            "status": "pending",
            "dataset_id": None,
            "updates": [],
            "events": []  # For signaling new data to SSE clients
        }
       
        # Start the background task for data transfer
        background_tasks.add_task(
            transfer_data_to_power_bi,
            task_id,
            request.report_id,
            request.dataset_name,
            request.include_data,
            request.project_id,
            request.workspace_id
        )
       
        return JSONResponse(
            status_code=202,
            content={
                "task_id": task_id,
                "dataset_name": request.dataset_name,
                "status": "processing",
                "message": "Dataset creation has started. Connect to the SSE endpoint to receive live updates."
            }
        )
    except Exception as e:
        logger.error(f"Error in create_dataset endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
 
@router.get("/dataset-status/{task_id}")
async def get_dataset_status(task_id: str):
    """
    Get the current status of a dataset creation task
    """
    if task_id not in task_store:
        raise HTTPException(status_code=404, detail="Task not found")
   
    task = task_store[task_id]
    status = "completed" if task["dataset_id"] else "processing"
    if any(update.get("status") == "error" for update in task["updates"] if update.get("type") == "status"):
        status = "error"
   
    return {
        "task_id": task_id,
        "dataset_name": task["dataset_name"],
        "dataset_id": task["dataset_id"],
        "status": status,
        "updates_count": len(task["updates"])
    }
 
async def event_generator(task_id: str) -> AsyncIterator[str]:
    """Generate SSE events for the given task"""
    if task_id not in task_store:
        yield f"data: {json.dumps({'error': 'Task not found'})}\n\n"
        return
   
    # Send all existing updates first
    for update in task_store[task_id]["updates"]:
        yield f"data: {json.dumps(update)}\n\n"
        # Add a small delay to ensure events are flushed
        await asyncio.sleep(0.01)
   
    # Create an event that will be triggered when new updates are available
    last_sent = len(task_store[task_id]["updates"])
   
    # Keep the connection open for up to 10 minutes or until the task completes
    end_time = datetime.datetime.now() + datetime.timedelta(minutes=10)
   
    # Check if the task has already completed
    task_completed = False
    for update in task_store[task_id]["updates"]:
        if update.get("type") == "status" and update.get("status") in ["completed", "error"]:
            task_completed = True
            break
            
    if task_completed:
        logger.info(f"Task {task_id} already completed, sending closing message")
        yield f"data: {json.dumps({'type': 'info', 'message': 'Stream closing', 'timestamp': datetime.datetime.now().isoformat()})}\n\n"
        return
   
    while datetime.datetime.now() < end_time:
        # Create a new event for this iteration
        event = asyncio.Event()
        task_store[task_id]["events"].append(event)
       
        try:
            # Wait for new data or timeout after 2 seconds
            await asyncio.wait_for(event.wait(), timeout=2)
           
            # Send any new updates
            updates = task_store[task_id]["updates"][last_sent:]
            for update in updates:
                yield f"data: {json.dumps(update)}\n\n"
                # Force a small delay to ensure the data is flushed
                await asyncio.sleep(0.01)
                
                # Check if this update indicates completion
                if update.get("type") == "status" and update.get("status") in ["completed", "error"]:
                    logger.info(f"Task {task_id} completed with status: {update.get('status')}")
                    task_completed = True
           
            last_sent += len(updates)
           
            # Check if the task has completed or errored
            if task_completed:
                break
               
        except asyncio.TimeoutError:
            # Send a keepalive comment to prevent connection timeout
            yield ": keepalive\n\n"
            # Force a small delay to ensure the keepalive is flushed
            await asyncio.sleep(0.01)
        finally:
            # Always remove the event when done with it
            if event in task_store[task_id]["events"]:
                task_store[task_id]["events"].remove(event)
   
    # Send a final event to indicate the stream is closing
    yield f"data: {json.dumps({'type': 'info', 'message': 'Stream closing', 'timestamp': datetime.datetime.now().isoformat()})}\n\n"
 
@router.get("/dataset-updates/{task_id}")
async def stream_dataset_updates(task_id: str):
    """
    Stream dataset creation updates using Server-Sent Events (SSE)
   
    Connect to this endpoint to receive real-time updates on the dataset creation process.
    """
    if task_id not in task_store:
        raise HTTPException(status_code=404, detail="Task not found")
   
    return StreamingResponse(
        event_generator(task_id),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache, no-transform",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",  # Disable Nginx buffering
            "Transfer-Encoding": "chunked"
        }
    )

@router.post("/download-dax-expressions")
async def download_dax_expressions(request: DownloadDaxExpressionsRequest):
    """
    Download DAX expressions in Excel format.
    
    This endpoint reads the DAX expression data from the converted_expressions directory
    and returns it as an Excel file.
    
    Args:
        request: Contains project_id and flags for which expression types to include
        
    Returns:
        StreamingResponse: Excel file containing DAX expressions
    """
    project_id = request.project_id
    logger.info(f"Generating DAX expressions Excel for project: {project_id}")
    
    try:
        # Output buffer for Excel file
        output = io.BytesIO()
        
        # Load metrics data if requested
        metrics_data = []
        if request.include_metrics:
            metrics_file = os.path.join("converted_expressions", f"metrics_dax_conversions_{project_id}.json")
            if os.path.exists(metrics_file):
                try:
                    with open(metrics_file, 'r') as f:
                        metrics_json = json.load(f)
                        
                    for metric in metrics_json:
                        metrics_data.append({
                            "ID": metric.get("id", ""),
                            "Name": metric.get("name", ""),
                            "Type": "Metric",
                            "MicroStrategy Expression": metric.get("mstr_expression", ""),
                            "DAX Expression": metric.get("dax_expression", "")
                        })
                        print(metrics_data)
                except Exception as e:
                    logger.error(f"Error loading metrics DAX file: {str(e)}")
        
        # Load attributes data if requested
        attributes_data = []
        if request.include_attributes:
            attributes_file = os.path.join("converted_expressions", f"attributes_dax_conversions_{project_id}.json")
            if os.path.exists(attributes_file):
                try:
                    with open(attributes_file, 'r') as f:
                        attributes_json = json.load(f)
                        
                    for attribute in attributes_json:
                        for expr in attribute.get("expressions", []):
                            attributes_data.append({
                                "ID": attribute.get("id", ""),
                                "Name": attribute.get("name", ""),
                                "Type": "Attribute",
                                "MicroStrategy Expression": expr.get("mstr_expression", ""),
                                "DAX Expression": expr.get("dax_expression", "")
                            })
                except Exception as e:
                    logger.error(f"Error loading attributes DAX file: {str(e)}")
        
        # Combine data
        all_data = metrics_data + attributes_data
        
        if not all_data:
            logger.warning(f"No DAX expressions found for project: {project_id}")
            raise HTTPException(
                status_code=404, 
                detail=f"No DAX expressions found for project: {project_id}"
            )
        
        # Create dataframe
        df = pd.DataFrame(all_data)
        
        # Write to Excel
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            # Create an overview sheet with project info
            overview_df = pd.DataFrame({
                'Project Information': [
                    f"Project ID: {project_id}",
                    f"Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    f"Metrics: {len(metrics_data)}",
                    f"Attributes: {len(attributes_data)}"
                ]
            })
            overview_df.to_excel(writer, sheet_name='Overview', index=False)
            
            # Write all DAX expressions to a sheet
            if all_data:
                df.to_excel(writer, sheet_name='DAX Expressions', index=False)
            
            # Write metrics to a dedicated sheet if available
            if metrics_data:
                metrics_df = pd.DataFrame(metrics_data)
                metrics_df.to_excel(writer, sheet_name='Metrics', index=False)
            
            # Write attributes to a dedicated sheet if available
            if attributes_data:
                attributes_df = pd.DataFrame(attributes_data)
                attributes_df.to_excel(writer, sheet_name='Attributes', index=False)
        
        # Set up the response
        output.seek(0)
        filename = f"DAX_Expressions_{project_id}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        # Return the Excel file as a streaming response
        return StreamingResponse(
            output,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error generating DAX expressions Excel: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to generate DAX expressions Excel: {str(e)}")