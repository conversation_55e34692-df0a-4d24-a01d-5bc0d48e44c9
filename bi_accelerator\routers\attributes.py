from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
import logging
import traceback
import os
import json
from openai import OpenAI
from mstrio.modeling.schema import Attribute, list_attributes
from routers.utils import get_connection

logger = logging.getLogger("bi_accelerator")
router = APIRouter(prefix="/api", tags=["attributes"])

@router.get("/attributes")
async def get_attributes(project_id: str):
    logger.info(f"Fetching attributes for project_id: {project_id}")
    # Get all attributes in the project
    try:
        project_conn = get_connection(project_id)
        attributes = list_attributes(connection=project_conn)
        attributes_data = [{"id": a.id, "name": a.name} for a in attributes]
        logger.info(f"Successfully retrieved {len(attributes_data)} attributes for project {project_id}")
        return JSONResponse(content=attributes_data)
    except Exception as e:
        logger.error(f"Error retrieving attributes for project {project_id}: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to retrieve attributes: {str(e)}")

@router.get("/attribute/{attribute_id}")
async def get_attribute_details(attribute_id: str, project_id: str):
    logger.info(f"Fetching details for attribute_id: {attribute_id} in project_id: {project_id}")
    # Get detailed information about a specific attribute
    try:
        project_conn = get_connection(project_id)
        attribute_obj = Attribute(connection=project_conn, id=attribute_id)
        logger.debug(f"Successfully loaded attribute object with name: {attribute_obj.name}")
        
        # Get properties with error handling and convert non-serializable objects to strings
        properties = {}
        try:
            raw_properties = attribute_obj.list_properties()
            # Convert non-serializable objects to their string representation
            for key, value in raw_properties.items():
                if isinstance(value, (str, int, float, bool, type(None))):
                    properties[key] = value
                elif isinstance(value, (list, tuple)):
                    # For collections, convert each item if needed
                    try:
                        properties[key] = [str(item) if not isinstance(item, (str, int, float, bool, type(None), dict, list)) else item for item in value]
                    except:
                        properties[key] = str(value)
                elif isinstance(value, dict):
                    # For dictionaries, attempt to convert non-serializable values
                    try:
                        serializable_dict = {}
                        for k, v in value.items():
                            if isinstance(v, (str, int, float, bool, type(None), dict, list)):
                                serializable_dict[k] = v
                            else:
                                serializable_dict[k] = str(v)
                        properties[key] = serializable_dict
                    except:
                        properties[key] = str(value)
                else:
                    # For any other type, convert to string
                    properties[key] = str(value)
            
            logger.debug(f"Retrieved properties for attribute {attribute_id}")
        except Exception as prop_err:
            logger.error(f"Error retrieving properties for attribute {attribute_id}: {str(prop_err)}")
            properties = {"error": str(prop_err)}
        
        details = {
            "id": attribute_id,
            "name": attribute_obj.name,
            "properties": properties
        }
        
        logger.info(f"Successfully retrieved details for attribute {attribute_id}")
        return JSONResponse(content=details)
    except Exception as e:
        logger.error(f"Error retrieving attribute {attribute_id} details: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=404, detail=f"Attribute not found or error: {str(e)}")

@router.get("/attribute/{attribute_id}/expressions")
async def get_attribute_expressions(attribute_id: str, project_id: str):
    logger.info(f"Fetching expressions for attribute_id: {attribute_id} in project_id: {project_id}")
    try:
        project_conn = get_connection(project_id)
        attribute_obj = Attribute(connection=project_conn, id=attribute_id)
        
        # Get forms and their expressions
        try:
            forms_data = []
            
            # Process each form
            for form in attribute_obj.forms:
                form_data = {
                    "id": form.id,
                    "name": form.name,
                    "expressions": []
                }
                
                # Process expressions for each form
                for expression in form.expressions:
                    expr_data = {}
                    # Extract expression ID if available
                    if hasattr(expression, 'id'):
                        expr_data["id"] = expression.id
                    
                    # Handle the expression value
                    if hasattr(expression, 'expression'):
                        expr_value = expression.expression
                        # Handle serialization of expression
                        if hasattr(expr_value, 'text'):
                            expr_data["expression"] = expr_value.text
                        elif hasattr(expr_value, '__dict__'):
                            # Convert complex object to dict
                            expr_dict = {}
                            for key, value in expr_value.__dict__.items():
                                if isinstance(value, (str, int, float, bool, type(None))):
                                    expr_dict[key] = value
                                else:
                                    expr_dict[key] = str(value)
                            expr_data["expression"] = expr_dict
                        elif not isinstance(expr_value, (str, dict, int, float, bool, type(None))):
                            # Convert non-serializable to string
                            expr_data["expression"] = str(expr_value)
                        else:
                            expr_data["expression"] = expr_value
                    
                    form_data["expressions"].append(expr_data)
                
                forms_data.append(form_data)
            
            result = {
                "id": attribute_id,
                "name": attribute_obj.name,
                "forms": forms_data
            }
            
            logger.info(f"Successfully retrieved expressions for attribute {attribute_id}")
            return JSONResponse(content=result)
        except Exception as expr_err:
            logger.error(f"Error accessing expressions for attribute {attribute_id}: {str(expr_err)}")
            logger.error(traceback.format_exc())
            raise HTTPException(status_code=500, detail=f"Error accessing attribute expressions: {str(expr_err)}")
    
    except Exception as e:
        logger.error(f"Error retrieving attribute {attribute_id} expressions: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=404, detail=f"Attribute not found or error: {str(e)}")

@router.get("/attribute/{attribute_id}/to_dax")
async def convert_attribute_to_dax(attribute_id: str, project_id: str):
    logger.info(f"Converting expressions to DAX for attribute_id: {attribute_id} in project_id: {project_id}")
    try:
        # Get attribute and its expressions
        project_conn = get_connection(project_id)
        attribute_obj = Attribute(connection=project_conn, id=attribute_id)
        
        # Extract expressions from forms
        expressions_data = []
        for form in attribute_obj.forms:
            for expression in form.expressions:
                expr_value = None
                if hasattr(expression, 'expression'):
                    expr_value = expression.expression
                    # Handle different types of expression objects
                    if not isinstance(expr_value, str):
                        expr_value = str(expr_value)
                
                if expr_value:
                    expressions_data.append({
                        "form_name": form.name,
                        "expression": expr_value
                    })
        
        if not expressions_data:
            return JSONResponse(content={"dax": ""})
        
        # Call OpenAI to convert expressions to DAX
        # For simplicity, we'll just convert the first expression found
        client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))
        
        expr_data = expressions_data[0]  # Use the first expression
        prompt = f"""
        Convert the following MicroStrategy expression to a DAX expression:
        
        MicroStrategy expression:
        {expr_data['expression']}
        
        Provide ONLY the equivalent DAX expression without any explanation or additional text.
        """
        
        try:
            response = client.chat.completions.create(
                model="o3-mini",
                messages=[
                    {"role": "system", "content": "You are an expert in both MicroStrategy expressions and DAX (Data Analysis Expressions) used in Power BI, Analysis Services, etc. Your task is to convert MicroStrategy expressions to their DAX equivalents. Return ONLY the DAX expression without any explanation, additional text, or markdown formatting."},
                    {"role": "user", "content": prompt}
                ],
                # temperature=0.2,
            )
            
            # Return simplified response with just the DAX expression
            return JSONResponse(content={"dax": response.choices[0].message.content.strip()})
            
        except Exception as openai_err:
            logger.error(f"Error calling OpenAI for attribute {attribute_id}: {str(openai_err)}")
            raise HTTPException(
                status_code=500, 
                detail=f"Error during OpenAI conversion: {str(openai_err)}"
            )
        
    except Exception as e:
        logger.error(f"Error converting attribute {attribute_id} expressions to DAX: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to convert expressions to DAX: {str(e)}")

@router.get("/attributes/filter_by_dependency")
async def filter_attributes_by_dependency(project_id: str):
    logger.info(f"Filtering attributes with Apply functions dependencies for project_id: {project_id}")
    
    # List of Apply functions to search for
    apply_functions = ["ApplySimple", "ApplyComparison", "ApplyAgg", "ApplyLogic", "ApplyOLAP"]
    
    try:
        project_conn = get_connection(project_id)
        attributes = list_attributes(connection=project_conn)
        
        filtered_attributes = []
        
        for attr in attributes:
            try:
                attr_obj = Attribute(connection=project_conn, id=attr.id)
                dependencies = attr_obj.list_dependencies()
                
                # Check if any dependency contains any of the Apply functions
                has_apply_function = False
                for dep in dependencies:
                    dep_name = dep.get('name', '')
                    if any(apply_func in dep_name for apply_func in apply_functions):
                        has_apply_function = True
                        break
                
                if has_apply_function:
                    # Store attribute with its id and name
                    filtered_attributes.append({
                        "id": attr.id,
                        "name": attr.name
                    })
            except Exception as attr_err:
                logger.warning(f"Error processing attribute {attr.id}: {str(attr_err)}")
                continue
        
        logger.info(f"Found {len(filtered_attributes)} attributes with Apply function dependencies")
        return JSONResponse(content=filtered_attributes)
    except Exception as e:
        logger.error(f"Error filtering attributes for project {project_id}: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to filter attributes: {str(e)}")

@router.post("/attributes/batch_convert_to_dax")
async def batch_convert_attributes_to_dax(attribute_ids: list[str], project_id: str):
    logger.info(f"Batch converting {len(attribute_ids)} attributes to DAX for project_id: {project_id}")
    try:
        project_conn = get_connection(project_id)
        results = []
        
        for attribute_id in attribute_ids:
            try:
                attribute_obj = Attribute(connection=project_conn, id=attribute_id)
                
                # Extract expressions from forms
                expressions_data = []
                for form in attribute_obj.forms:
                    for expression in form.expressions:
                        expr_value = None
                        if hasattr(expression, 'expression'):
                            expr_value = expression.expression
                            # Handle different types of expression objects
                            if hasattr(expr_value, 'text'):
                                expr_value = expr_value.text
                            elif not isinstance(expr_value, str):
                                expr_value = str(expr_value)
                            
                            # Clean up the expression text by removing extra information
                            expr_value = expr_value.strip()
                        
                        if expr_value:
                            expressions_data.append({
                                "form_name": form.name,
                                "expression": expr_value
                            })
                
                if not expressions_data:
                    continue
                
                # Call OpenAI to convert expressions to DAX
                client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))
                
                # Convert each expression
                dax_expressions = []
                for expr_data in expressions_data:
                    prompt = f"""
                    Convert the following MicroStrategy expression to a DAX expression:
                    
                    MicroStrategy expression:
                    {expr_data['expression']}
                    
                    Provide ONLY the equivalent DAX expression without any explanation or additional text.
                    """
                    
                    try:
                        response = client.chat.completions.create(
                            model="o3-mini",
                            messages=[
                                {"role": "system", "content": "You are an expert in both MicroStrategy expressions and DAX (Data Analysis Expressions) used in Power BI, Analysis Services, etc. Your task is to convert MicroStrategy expressions to their DAX equivalents. Return ONLY the DAX expression without any explanation, additional text, or markdown formatting."},
                                {"role": "user", "content": prompt}
                            ],
                        )
                        
                        dax_expressions.append({
                            "mstr_expression": expr_data["expression"],
                            "dax_expression": response.choices[0].message.content.strip()
                        })
                        
                    except Exception as openai_err:
                        logger.error(f"Error calling OpenAI for attribute {attribute_id}: {str(openai_err)}")
                        continue
                
                if dax_expressions:
                    results.append({
                        "id": attribute_id,
                        "name": attribute_obj.name,
                        "expressions": dax_expressions
                    })
                    
            except Exception as attr_err:
                logger.warning(f"Error processing attribute {attribute_id}: {str(attr_err)}")
                continue
        
        # Save results to JSON file
        try:
            output_dir = "converted_expressions"
            os.makedirs(output_dir, exist_ok=True)
            output_file = os.path.join(output_dir, f"attributes_dax_conversions_{project_id}.json")
            
            # Read existing conversions if file exists
            existing_data = []
            if os.path.exists(output_file):
                with open(output_file, 'r') as f:
                    existing_data = json.load(f)
            
            # Merge new results with existing data
            existing_ids = {item["id"] for item in existing_data}
            new_results = [item for item in results if item["id"] not in existing_ids]
            merged_data = existing_data + new_results
            
            # Save merged data
            with open(output_file, 'w') as f:
                json.dump(merged_data, f, indent=2)
            
            logger.info(f"Saved {len(new_results)} new conversions to {output_file}")
            
        except Exception as save_err:
            logger.error(f"Error saving conversions to JSON: {str(save_err)}")
        
        return JSONResponse(content=results)
        
    except Exception as e:
        logger.error(f"Error in batch conversion: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to convert attributes: {str(e)}")

@router.get("/attributes/converted_expressions/{project_id}")
async def get_converted_attributes(project_id: str):
    logger.info(f"Retrieving converted attributes for project_id: {project_id}")
    try:
        output_file = os.path.join("converted_expressions", f"attributes_dax_conversions_{project_id}.json")
        
        if not os.path.exists(output_file):
            return JSONResponse(content=[])
            
        with open(output_file, 'r') as f:
            data = json.load(f)
            
        return JSONResponse(content=data)
        
    except Exception as e:
        logger.error(f"Error retrieving converted attributes: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to retrieve converted attributes: {str(e)}")

@router.post("/attributes/process_apply_functions")
async def process_apply_functions_in_background(project_id: str):
    logger.info(f"Starting background processing of attributes with Apply functions for project_id: {project_id}")
    
    # Start a background task
    import threading
    
    def background_task():
        try:
            project_conn = get_connection(project_id)
            attributes = list_attributes(connection=project_conn)
            
            # List of Apply functions to search for
            apply_functions = ["ApplySimple", "ApplyComparison", "ApplyAgg", "ApplyLogic", "ApplyOLAP"]
            
            filtered_attributes = []
            processed_count = 0
            total_count = len(attributes)
            
            logger.info(f"Processing {total_count} attributes for project {project_id}")
            
            for attr in attributes:
                try:
                    attr_obj = Attribute(connection=project_conn, id=attr.id)
                    dependencies = attr_obj.list_dependencies()
                    
                    # Check if any dependency contains any of the Apply functions
                    has_apply_function = False
                    for dep in dependencies:
                        dep_name = dep.get('name', '')
                        if any(apply_func in dep_name for apply_func in apply_functions):
                            has_apply_function = True
                            break
                    
                    if has_apply_function:
                        # Store attribute with its id and name
                        filtered_attributes.append({
                            "id": attr.id,
                            "name": attr.name
                        })
                    
                    processed_count += 1
                    if processed_count % 10 == 0:  # Log progress every 10 items
                        logger.info(f"Processed {processed_count}/{total_count} attributes")
                        
                except Exception as attr_err:
                    logger.warning(f"Error processing attribute {attr.id}: {str(attr_err)}")
                    continue
            
            # Save results to JSON file
            try:
                output_dir = "cached_data"
                os.makedirs(output_dir, exist_ok=True)
                output_file = os.path.join(output_dir, f"attributes_apply_functions_{project_id}.json")
                
                with open(output_file, 'w') as f:
                    json.dump(filtered_attributes, f, indent=2)
                
                logger.info(f"Saved {len(filtered_attributes)} attributes with Apply functions to {output_file}")
                
            except Exception as save_err:
                logger.error(f"Error saving attributes to JSON: {str(save_err)}")
                
        except Exception as e:
            logger.error(f"Error in background processing of attributes: {str(e)}")
            logger.error(traceback.format_exc())
    
    # Start the background thread
    thread = threading.Thread(target=background_task)
    thread.daemon = True
    thread.start()
    
    return JSONResponse(content={"status": "processing_started", "message": "Background processing of attributes started"})

@router.get("/attributes/cached_apply_functions/{project_id}")
async def get_cached_apply_functions(project_id: str):
    logger.info(f"Retrieving cached attributes with Apply functions for project_id: {project_id}")
    try:
        output_file = os.path.join("cached_data", f"attributes_apply_functions_{project_id}.json")
        
        if not os.path.exists(output_file):
            return JSONResponse(content=[])
            
        with open(output_file, 'r') as f:
            data = json.load(f)
            
        return JSONResponse(content=data)
        
    except Exception as e:
        logger.error(f"Error retrieving cached attributes: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to retrieve cached attributes: {str(e)}") 