import os
import json
import logging
from fastapi import APIRouter, HTTPException, Path, Query
from pydantic import BaseModel, <PERSON>
from typing import Dict, List, Optional

# Set up logging
logger = logging.getLogger("bi_accelerator")

# Define the router
router = APIRouter(
    prefix="/api/credentials",
    tags=["credentials"],
    responses={404: {"description": "Not found"}},
)

# Define the credential model
class Credentials(BaseModel):
    url: str
    username: str
    password: str
    description: Optional[str] = None

class EnvironmentCredentials(BaseModel):
    name: str = Field(..., description="Name of the environment (e.g., 'dev', 'prod')")
    is_default: bool = Field(False, description="Whether this is the default environment")
    credentials: Credentials

class EnvironmentList(BaseModel):
    environments: List[Dict] = []
    active_environment: Optional[str] = None

# File to store credentials
CREDENTIALS_FILE = "credentials.json"

def load_environments():
    """Load all saved environments"""
    try:
        if os.path.exists(CREDENTIALS_FILE):
            with open(CREDENTIALS_FILE, "r") as f:
                data = json.load(f)
                # Handle both old format (single environment) and new format (multiple environments)
                if "environments" in data:
                    return data
                else:
                    # Convert old format to new format
                    return {
                        "environments": [
                            {
                                "name": "default",
                                "is_default": True,
                                "credentials": data
                            }
                        ],
                        "active_environment": "default"
                    }
        return {"environments": [], "active_environment": None}
    except Exception as e:
        logger.error(f"Failed to load environments: {str(e)}")
        return {"environments": [], "active_environment": None}

def save_environments(environments_data):
    """Save all environments to file"""
    try:
        with open(CREDENTIALS_FILE, "w") as f:
            json.dump(environments_data, f)
        return True
    except Exception as e:
        logger.error(f"Failed to save environments: {str(e)}")
        return False

@router.post("")
async def save_credentials(env_credentials: EnvironmentCredentials):
    """
    Save MicroStrategy connection credentials for a specific environment
    """
    try:
        environments_data = load_environments()
        
        # Check if environment already exists
        for i, env in enumerate(environments_data["environments"]):
            if env["name"] == env_credentials.name:
                # Update existing environment
                environments_data["environments"][i] = env_credentials.dict()
                logger.info(f"Updated credentials for environment: {env_credentials.name}")
                
                # If this is set as default, update other environments
                if env_credentials.is_default:
                    for j, other_env in enumerate(environments_data["environments"]):
                        if j != i:
                            environments_data["environments"][j]["is_default"] = False
                    environments_data["active_environment"] = env_credentials.name
                
                save_environments(environments_data)
                return {"status": "success", "message": f"Credentials for environment '{env_credentials.name}' updated successfully"}
        
        # Add new environment
        env_dict = env_credentials.dict()
        environments_data["environments"].append(env_dict)
        
        # If this is the first environment or marked as default
        if not environments_data["environments"] or env_credentials.is_default:
            # Set all other environments to non-default
            for env in environments_data["environments"]:
                if env["name"] != env_credentials.name:
                    env["is_default"] = False
            environments_data["active_environment"] = env_credentials.name
        
        save_environments(environments_data)
        logger.info(f"Added new environment: {env_credentials.name}")
        return {"status": "success", "message": f"Credentials for environment '{env_credentials.name}' saved successfully"}
    except Exception as e:
        logger.error(f"Failed to save credentials: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to save credentials: {str(e)}")

@router.get("")
async def get_credentials(environment_name: Optional[str] = Query(None, description="Optional environment name to retrieve")):
    """
    Get stored MicroStrategy connection credentials
    """
    try:
        environments_data = load_environments()
        
        if not environments_data["environments"]:
            raise HTTPException(status_code=404, detail="No credentials found")
        
        if environment_name:
            # Get specific environment
            for env in environments_data["environments"]:
                if env["name"] == environment_name:
        # Don't return the actual password value for security
                    masked_env = env.copy()
                    masked_env["credentials"]["password"] = "********"
                    return masked_env
            raise HTTPException(status_code=404, detail=f"Environment '{environment_name}' not found")
        else:
            # Return list of all environments with masked passwords
            masked_environments = []
            for env in environments_data["environments"]:
                masked_env = env.copy()
                masked_env["credentials"]["password"] = "********"
                masked_environments.append(masked_env)
            
            return {
                "environments": masked_environments,
                "active_environment": environments_data["active_environment"]
            }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get credentials: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get credentials: {str(e)}")

@router.delete("/{environment_name}")
async def delete_environment_credentials(environment_name: str = Path(..., description="Name of the environment to delete")):
    """
    Delete stored MicroStrategy connection credentials for a specific environment
    """
    try:
        environments_data = load_environments()
        
        # Find and remove the specified environment
        found = False
        was_default = False
        was_active = environments_data["active_environment"] == environment_name
        
        new_environments = []
        for env in environments_data["environments"]:
            if env["name"] == environment_name:
                found = True
                was_default = env["is_default"]
            else:
                new_environments.append(env)
        
        if not found:
            return {"status": "success", "message": f"Environment '{environment_name}' not found"}
        
        environments_data["environments"] = new_environments
        
        # If we deleted the active environment, set a new one
        if was_active or was_default:
            if new_environments:
                # Set the first available environment as active
                environments_data["active_environment"] = new_environments[0]["name"]
                new_environments[0]["is_default"] = True
            else:
                environments_data["active_environment"] = None
        
        save_environments(environments_data)
        logger.info(f"Environment '{environment_name}' deleted successfully")
        return {"status": "success", "message": f"Environment '{environment_name}' deleted successfully"}
    except Exception as e:
        logger.error(f"Failed to delete environment: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete environment: {str(e)}")

@router.delete("")
async def delete_all_credentials():
    """
    Delete all stored MicroStrategy connection credentials
    """
    try:
        if os.path.exists(CREDENTIALS_FILE):
            os.remove(CREDENTIALS_FILE)
            
        logger.info("All credentials deleted successfully")
        return {"status": "success", "message": "All credentials deleted successfully"}
    except Exception as e:
        logger.error(f"Failed to delete credentials: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete credentials: {str(e)}") 

@router.put("/active/{environment_name}")
async def set_active_environment(environment_name: str = Path(..., description="Name of the environment to set as active")):
    """
    Set the active MicroStrategy environment
    """
    try:
        environments_data = load_environments()
        
        # Check if environment exists
        environment_exists = False
        for env in environments_data["environments"]:
            if env["name"] == environment_name:
                environment_exists = True
                break
                
        if not environment_exists:
            raise HTTPException(status_code=404, detail=f"Environment '{environment_name}' not found")
        
        # Set active environment
        environments_data["active_environment"] = environment_name
        save_environments(environments_data)
        
        logger.info(f"Set active environment to '{environment_name}'")
        return {"status": "success", "message": f"Active environment set to '{environment_name}'"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to set active environment: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to set active environment: {str(e)}") 