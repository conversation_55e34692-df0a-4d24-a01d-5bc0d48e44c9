from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
import logging
import traceback
from mstrio.project_objects.datasets.super_cube import SuperCube
from mstrio.project_objects.datasets.olap_cube import OlapCube
from mstrio.project_objects.datasets import list_all_cubes
from routers.utils import get_connection

logger = logging.getLogger("bi_accelerator")
router = APIRouter(prefix="/api", tags=["cubes"])

@router.get("/cubes")
async def get_cubes(project_id: str):
    logger.info(f"Fetching cubes for project_id: {project_id}")
    # Get all cubes in the project
    try:
        project_conn = get_connection(project_id)
        
        # Use list_all_cubes to get all cubes in the project
        cubes = []
        try:
            cubes = list_all_cubes(connection=project_conn)
            logger.debug(f"Retrieved {len(cubes)} cubes for project {project_id}")
        except Exception as cube_err:
            logger.error(f"Error retrieving cubes for project {project_id}: {str(cube_err)}")
            logger.error(traceback.format_exc())
            cubes = []
        
        # Create the response data, ensuring all values are JSON serializable
        cubes_data = []
        for c in cubes:
            try:
                # Make sure we convert any non-serializable types to strings
                cube_type = getattr(c, "_type", c.__class__.__name__)
                if not isinstance(cube_type, (str, int, float, bool, type(None))):
                    cube_type = str(cube_type)
                    
                cubes_data.append({
                    "id": c.id, 
                    "name": c.name, 
                    "type": cube_type
                })
            except Exception as e:
                logger.error(f"Error processing cube information: {str(e)}")
                logger.error(traceback.format_exc())
        
        logger.info(f"Successfully retrieved {len(cubes_data)} cubes for project {project_id}")
        return JSONResponse(content=cubes_data)
    except Exception as e:
        logger.error(f"Error retrieving cubes for project {project_id}: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Failed to retrieve cubes: {str(e)}")

@router.get("/cube/{cube_id}")
async def get_cube_details(cube_id: str, project_id: str, cube_type: str = None):
    logger.info(f"Fetching details for cube_id: {cube_id} in project_id: {project_id}")
    # Get detailed information about a specific cube
    try:
        project_conn = get_connection(project_id)
        
        # First, try to find the cube in the list of all cubes
        # This will determine the correct cube type if not specified
        if not cube_type:
            logger.debug(f"Cube type not specified, will try to determine from all cubes")
            try:
                all_cubes = list_all_cubes(connection=project_conn)
                for cube in all_cubes:
                    if cube.id == cube_id:
                        found_type = getattr(cube, "_type", cube.__class__.__name__)
                        # Make sure the type is serializable
                        if not isinstance(found_type, (str, int, float, bool, type(None))):
                            found_type = str(found_type)
                        cube_type = found_type
                        logger.debug(f"Found cube type: {cube_type}")
                        break
            except Exception as find_err:
                logger.error(f"Error determining cube type: {str(find_err)}")
                logger.error(traceback.format_exc())
        
        # Create cube object based on type
        cube_obj = None
        logger.debug(f"Creating cube object with type: {cube_type}")
        
        try:
            # If still no cube type or it's SuperCube, try SuperCube
            if not cube_type or cube_type.lower() in ['supercube', 'super_cube']:
                logger.debug("Attempting to load as SuperCube")
                cube_obj = SuperCube(connection=project_conn, id=cube_id)
                cube_type = "SuperCube"
            # If OlapCube, try OlapCube
            elif cube_type.lower() in ['olapcube', 'olap_cube']:
                logger.debug("Attempting to load as OlapCube")
                cube_obj = OlapCube(connection=project_conn, id=cube_id)
                cube_type = "OlapCube"
            else:
                # Generic fallback
                logger.debug(f"Unrecognized cube type '{cube_type}', trying as SuperCube first")
                try:
                    cube_obj = SuperCube(connection=project_conn, id=cube_id)
                    cube_type = "SuperCube"
                except:
                    logger.debug("Failed as SuperCube, trying as OlapCube")
                    cube_obj = OlapCube(connection=project_conn, id=cube_id)
                    cube_type = "OlapCube"
        except Exception as cube_err:
            logger.error(f"Error creating cube object: {str(cube_err)}")
            logger.error(traceback.format_exc())
            raise HTTPException(status_code=404, detail=f"Cube not found or error: {str(cube_err)}")
            
        logger.debug(f"Successfully loaded {cube_type} cube object with name: {cube_obj.name}")
        
        # Get cube properties with error handling and convert non-serializable objects to strings
        cube_properties = {}
        try:
            raw_properties = cube_obj.list_properties()
            # Convert non-serializable objects to their string representation
            for key, value in raw_properties.items():
                if isinstance(value, (str, int, float, bool, type(None))):
                    cube_properties[key] = value
                elif isinstance(value, (list, tuple)):
                    # For collections, convert each item if needed
                    try:
                        cube_properties[key] = [str(item) if not isinstance(item, (str, int, float, bool, type(None), dict, list)) else item for item in value]
                    except:
                        cube_properties[key] = str(value)
                elif isinstance(value, dict):
                    # For dictionaries, attempt to convert non-serializable values
                    try:
                        serializable_dict = {}
                        for k, v in value.items():
                            if isinstance(v, (str, int, float, bool, type(None), dict, list)):
                                serializable_dict[k] = v
                            else:
                                serializable_dict[k] = str(v)
                        cube_properties[key] = serializable_dict
                    except:
                        cube_properties[key] = str(value)
                else:
                    # For any other type, convert to string
                    cube_properties[key] = str(value)
            
            logger.debug(f"Retrieved properties for cube {cube_id}")
        except Exception as prop_err:
            logger.error(f"Error retrieving properties for cube {cube_id}: {str(prop_err)}")
            cube_properties = {"error": str(prop_err)}
        
        # Get cube attributes with error handling
        attributes = []
        try:
            cube_attrs = cube_obj.attributes
            for attr in cube_attrs:
                # Handle both object and dictionary representations
                if isinstance(attr, dict):
                    # For dictionary representation
                    attr_info = {
                        "id": attr.get("id", attr.get("attributeId", "unknown")),
                        "name": attr.get("name", "Unknown Attribute")
                    }
                else:
                    # For object representation
                    attr_info = {"id": attr.id, "name": attr.name}
                attributes.append(attr_info)
            logger.debug(f"Retrieved {len(attributes)} attributes for cube {cube_id}")
        except Exception as attr_err:
            logger.error(f"Error retrieving attributes for cube {cube_id}: {str(attr_err)}")
            logger.error(traceback.format_exc())
            attributes = [{"error": str(attr_err)}]
        
        # Get cube metrics with error handling
        metrics = []
        try:
            cube_metrics = cube_obj.metrics
            for metric in cube_metrics:
                # Handle both object and dictionary representations
                if isinstance(metric, dict):
                    # For dictionary representation
                    metric_info = {
                        "id": metric.get("id", metric.get("metricId", "unknown")),
                        "name": metric.get("name", "Unknown Metric")
                    }
                else:
                    # For object representation
                    metric_info = {"id": metric.id, "name": metric.name}
                metrics.append(metric_info)
            logger.debug(f"Retrieved {len(metrics)} metrics for cube {cube_id}")
        except Exception as metric_err:
            logger.error(f"Error retrieving metrics for cube {cube_id}: {str(metric_err)}")
            logger.error(traceback.format_exc())
            metrics = [{"error": str(metric_err)}]
        
        # Modified: Remove attributes and metrics from properties if they exist
        # to avoid duplication with the dedicated sections
        if "attributes" in cube_properties:
            del cube_properties["attributes"]
        if "metrics" in cube_properties:
            del cube_properties["metrics"]
        
        details = {
            "id": cube_id,
            "name": cube_obj.name,
            "type": cube_type,
            "properties": cube_properties,
            "attributes": attributes,
            "metrics": metrics
        }
        
        logger.info(f"Successfully retrieved details for {cube_type} cube {cube_id}")
        return JSONResponse(content=details)
    except Exception as e:
        logger.error(f"Error retrieving cube {cube_id} details: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=404, detail=f"Cube not found or error: {str(e)}")

@router.get("/cube/{cube_id}/sql")
async def get_cube_sql(cube_id: str, project_id: str, cube_type: str = None):
    logger.info(f"Fetching SQL for cube_id: {cube_id} in project_id: {project_id}")
    # Get SQL view of a cube
    try:
        project_conn = get_connection(project_id)
        
        # First, try to find the cube in the list of all cubes
        # This will determine the correct cube type if not specified
        if not cube_type:
            logger.debug(f"Cube type not specified, will try to determine from all cubes")
            try:
                all_cubes = list_all_cubes(connection=project_conn)
                for cube in all_cubes:
                    if cube.id == cube_id:
                        found_type = getattr(cube, "_type", cube.__class__.__name__)
                        # Make sure the type is serializable
                        if not isinstance(found_type, (str, int, float, bool, type(None))):
                            found_type = str(found_type)
                        cube_type = found_type
                        logger.debug(f"Found cube type: {cube_type}")
                        break
            except Exception as find_err:
                logger.error(f"Error determining cube type: {str(find_err)}")
                logger.error(traceback.format_exc())
        
        # Create cube object based on type
        cube_obj = None
        logger.debug(f"Creating cube object with type: {cube_type}")
        
        try:
            # If still no cube type or it's SuperCube, try SuperCube
            if not cube_type or cube_type.lower() in ['supercube', 'super_cube']:
                logger.debug("Attempting to load as SuperCube")
                cube_obj = SuperCube(connection=project_conn, id=cube_id)
                cube_type = "SuperCube"
            # If OlapCube, try OlapCube
            elif cube_type.lower() in ['olapcube', 'olap_cube']:
                logger.debug("Attempting to load as OlapCube")
                cube_obj = OlapCube(connection=project_conn, id=cube_id)
                cube_type = "OlapCube"
            else:
                # Generic fallback
                logger.debug(f"Unrecognized cube type '{cube_type}', trying as SuperCube first")
                try:
                    cube_obj = SuperCube(connection=project_conn, id=cube_id)
                    cube_type = "SuperCube"
                except:
                    logger.debug("Failed as SuperCube, trying as OlapCube")
                    cube_obj = OlapCube(connection=project_conn, id=cube_id)
                    cube_type = "OlapCube"
        except Exception as cube_err:
            logger.error(f"Error creating cube object: {str(cube_err)}")
            logger.error(traceback.format_exc())
            raise HTTPException(status_code=404, detail=f"Cube not found or error: {str(cube_err)}")
            
        logger.debug(f"Successfully loaded {cube_type} cube object with name: {cube_obj.name}")
        
        # Get SQL with error handling
        sql = ""
        try:
            sql = cube_obj.export_sql_view()
            logger.debug(f"Retrieved SQL for cube {cube_id}")
        except Exception as sql_err:
            logger.error(f"Error retrieving SQL for cube {cube_id}: {str(sql_err)}")
            sql = f"Error retrieving SQL: {str(sql_err)}"
        
        logger.info(f"Successfully retrieved SQL for {cube_type} cube {cube_id}")
        return JSONResponse(content={"cube_id": cube_id, "sql": sql})
    except Exception as e:
        logger.error(f"Error retrieving SQL for cube {cube_id}: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=404, detail=f"Failed to retrieve SQL: {str(e)}") 