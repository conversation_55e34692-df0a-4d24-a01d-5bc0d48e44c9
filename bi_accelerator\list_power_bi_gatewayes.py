


from azure.identity import ClientSecretCredential
import os
import requests

TENANT_ID = os.getenv("TENANT_ID")
CLIENT_ID = os.getenv("CLIENT_ID")
CLIENT_SECRET = os.getenv("CLIENT_SECRET")
WORKSPACE_ID = os.getenv("PPU_WORKSPACE_ID")  # Power BI workspace (group) ID
 
credential = ClientSecretCredential(
    tenant_id=TENANT_ID,
    client_id=CLIENT_ID,
    client_secret=CLIENT_SECRET
)
token = credential.get_token("https://analysis.windows.net/powerbi/api/.default").token
 
headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {token}"
}

def list_power_bi_gateways(headers):
    """
    List all available Power BI gateways that the authenticated user has access to.
    """
    try:
        gateways_url = "https://api.powerbi.com/v1.0/myorg/gateways"
        resp = requests.get(gateways_url, headers=headers)
        print(resp,"\n\n",resp.text)
        if resp.status_code == 200:
            gateways = resp.json().get("value", [])
            if gateways:
                print("\nAvailable Power BI Gateways:")
                for gw in gateways:
                    print(f"- {gw.get('name')} (ID: {gw.get('id')})")
                return True
            else:
                print("\nNo Power BI Gateways found.")
                return False
        else:
            print(f"\nFailed to retrieve gateways. Status code: {resp.status_code}")
            print(f"Response: {resp.text}")
            return False
    except Exception as e:
        print(f"\nError listing gateways: {e}")
        return False

list_power_bi_gateways(headers)
