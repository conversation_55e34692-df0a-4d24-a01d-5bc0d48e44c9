import React from 'react';
import { useBrandTheme } from '@/themes/brand-theme-provider';
import { useTheme } from '@/lib/theme-provider';
import { Button } from './button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';
import { Label } from './label';
import { Separator } from './separator';
import { Palette, Moon, Sun, Laptop } from 'lucide-react';

export function ThemeSwitcher() {
  const { theme, setTheme } = useTheme();
  const { currentTheme, allThemes, setCurrentBrandTheme } = useBrandTheme();

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-2">
        <div className="flex items-center gap-2">
          <Palette className="h-4 w-4" />
          <Label>Brand Theme</Label>
        </div>
        <Select
          value={currentTheme?.id || 'default'}
          onValueChange={(value) => setCurrentBrandTheme(value)}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select a theme" />
          </SelectTrigger>
          <SelectContent>
            {allThemes.map((theme) => (
              <SelectItem key={theme.id} value={theme.id}>
                {theme.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <Separator />

      <div className="flex flex-col gap-2">
        <Label>Color Mode</Label>
        <div className="flex gap-2">
          <Button
            variant={theme === 'light' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTheme('light')}
          >
            <Sun className="h-4 w-4 mr-2" />
            Light
          </Button>
          <Button
            variant={theme === 'dark' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTheme('dark')}
          >
            <Moon className="h-4 w-4 mr-2" />
            Dark
          </Button>
          <Button
            variant={theme === 'system' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTheme('system')}
          >
            <Laptop className="h-4 w-4 mr-2" />
            System
          </Button>
        </div>
      </div>
    </div>
  );
}
