from mstrio.connection import Connection
from mstrio.server.project import Project
from mstrio.project_objects import Report
from mstrio.project_objects.report import list_reports
from dotenv import load_dotenv
import os
 
load_dotenv()
 
URL = os.getenv('URL')
USER = os.getenv('USER')
PASSWORD = os.getenv('PASSWORD')
 
# Create a connection instance
conn = Connection(
    base_url=URL,  # Replace with your MSTR server URL
    username=USER,           # Replace with your username
    password=PASSWORD,           # Replace with your password
    project_name=None,   # Replace with your project name
    login_mode=1                        # Adjust login_mode if needed (e.g., 1 for standard authentication)
)
 
# Establish the connection
conn.connect()
print("Connected to MicroStrategy successfully!")
 
# List available projects
print("Available Projects:")
projects = list(Project._list_projects(connection=conn))
for p in projects:
    print(f"Project Name: {p.name}, Project ID: {p.id}")
 
# Let the user select a project by entering its ID
pid = input("\nEnter the project ID: ")
 
# Create a project-specific connection using the selected project ID
project_conn = Connection(
    base_url=URL,
    username=USER,
    password=PASSWORD,
    project_id=pid,   # Use the selected project ID
    login_mode=1
)
project_conn.connect()
print("\nConnected to MicroStrategy Project successfully!\n")
 
# Fetch and list all reports from the project
print("List of Reports in the Project:")
reports = list_reports(connection=project_conn)
for r in reports:
    print(f"- {r.name} (ID: {r.id}) (Type: {r.type})")
 
# Ask user to select a report by entering its ID
report_id = input("\nEnter the Report ID to fetch its data: ")
 
# Load the selected report using its ID
report_conn = Report(connection=project_conn, id=report_id)
 
# Fetch and display report properties/metadata
print("\nReport Metadata (Properties):")
report_prop=report_conn.list_properties()
for key, value in report_prop.items():
    print(f"{key}: {value}")
 
# report_attr=report_conn.attributes
# print(report_attr)
 
report_metrics=report_conn.metrics
print(report_metrics)
 
for attr in report_conn.attributes:
    print(attr)
for metric in report_conn.metrics:
    print(metric)
 
# Fetch and display the report SQL view
print("\nReport SQL View:")
report_sql=report_conn.sql
print(report_sql)
 
# Fetch and display the report data as a DataFrame
print("\nReport Data (first few rows):")
report_df = report_conn.to_dataframe()
print(report_df)